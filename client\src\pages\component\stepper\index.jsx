import React from 'react';
import PropTypes from 'prop-types';
import { Stepper, Step, StepL<PERSON>l, Button, Box, StepConnector } from '@mui/material';
import './stepper.css'
const DynamicStepper = ({ steps, activeStep, handleNext, handleBack, handleReset, orientation, disabled, disabledBackBtn, SubmitText }) => {

  return (
    <Box className='step-wrapper'>
      <Box className='stepper-label' >
        <Stepper
          activeStep={activeStep}
          orientation={orientation}
          connector={
            <StepConnector
              sx={{
                height: '100%',
                '& .MuiStepConnector-line': {
                  minHeight: '100%',
                  borderColor: 'divider'
                },
              }}
            />
          }
          sx={{ flex: 1 }}
        >
          {steps.map((step, index) => (
            <Step key={step.label} >
              <StepLabel>{step.label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Box>
      <Box className='stepper-divider' ></Box>
      <Box className='steps-content-wrapper'>
        <Box sx={{ width: '100%' }}>
          {activeStep < steps.length &&
            steps[activeStep].content
          }
        </Box>
        <Box className='stepper-action-wrapper'>
          {activeStep === steps.length ? (
            <Button onClick={handleReset}>Reset</Button>
          ) : (
            <Box>
              <Button disabled={activeStep === 0 || disabledBackBtn} onClick={handleBack} sx={{ mr: 1 }}>
                Back
              </Button>
              <Button variant="contained" onClick={handleNext} disabled={disabled}>
                {activeStep === steps.length - 1 ? SubmitText || 'Submit' : 'Next'}
              </Button>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

DynamicStepper.propTypes = {
  steps: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string.isRequired,
    content: PropTypes.node.isRequired,
  })).isRequired,
  activeStep: PropTypes.number.isRequired,
  handleNext: PropTypes.func.isRequired,
  handleBack: PropTypes.func.isRequired,
  handleReset: PropTypes.func.isRequired,
  orientation: PropTypes.oneOf(['horizontal', 'vertical']),
};

DynamicStepper.defaultProps = {
  orientation: 'vertical',
};

export default DynamicStepper;
