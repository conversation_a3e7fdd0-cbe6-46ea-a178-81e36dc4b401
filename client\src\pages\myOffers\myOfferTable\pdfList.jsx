import FolderIcon from '@mui/icons-material/Folder';
import { Button, IconButton, Typography } from '@mui/material';
import AlertDialog from 'pages/component/dialogbox';
import { useEffect, useState } from 'react';
import CloseIcon from "@mui/icons-material/Close";
import { getSpecsSheetAction, getSpecsSheetByOrderIdAction, setSpecsSheet } from 'redux/reducers/myOffersReducer';
import { showAlert } from 'utils/helper';
import { useDispatch, useSelector } from 'react-redux';
import { get } from 'lodash';
import { Link } from 'react-router-dom';
const PDFList = ({ generatePdf, rowData, isSingleRfqPage, material, loading }) => {
    const myOfferData = useSelector((state) => state.myOffer);
    const [open, setOpen] = useState(false);
    const [isbtnClicked, setIsBtnClicked] = useState(false);
    const [offerList, setOfferList] = useState([])
    const dispatch = useDispatch()
    const list = [{
        name: 'offerPdf',
        title: 'Offer.pdf',
        type: 'click',
        onClick: (id) => generatePdf(id)

    }]

    useEffect(() => {
        let specsSheet = get(myOfferData, 'specsSheet', [])
        let updatedSpecsSheet = specsSheet?.map((specSheet) => ({ name: specSheet?.group_items_name, title: specSheet?.group_items_name, link: specSheet?.group_items_display_name }))
        if (isSingleRfqPage) {
            setOfferList(updatedSpecsSheet)
        } else {
            setOfferList([...list, ...updatedSpecsSheet])
        }
    }, [myOfferData?.specsSheet])

    useEffect(() => {
        if (offerList && offerList?.length > 0 && isbtnClicked) {
            setOpen(true);
        }
    }, [offerList, isbtnClicked])

    const handleOpen = () => {
        setIsBtnClicked(true)
        if (isSingleRfqPage) {
            let partNumber = material?.Part_Number
            if (partNumber) {
                dispatch(getSpecsSheetAction(partNumber))
            } else {
                showAlert(dispatch, false, 'Part Number is required', true)
            }
        } else {
            if (rowData?.offerId) {
                dispatch(getSpecsSheetByOrderIdAction(rowData?.offerId))
            }
            else {
                showAlert(dispatch, false, 'Part Number is required', true)
            }
        }
    }

    const handleClose = () => {
        setOpen(false);
        setIsBtnClicked(false)
        setOfferList([])
        dispatch(setSpecsSheet([]))
    }

    const OfferList = () => {
        return (<>
            <div className='files-wrapper'>
                <Typography variant='h4' color='secondary' fontWeight={600} mb={2}>Files</Typography>
                <CloseIcon sx={{ fontSize: '14px', fontWeight: 'bold', textAlign: 'center' }} color='primary' onClick={() => handleClose()} className='cursor-pointer' />
            </div>

            {offerList && offerList?.length > 0 ?
                offerList?.map((offer) => <div className='pdfListContainer'>
                    <Typography variant='h6' color='secondary' fontWeight={600} >{offer?.title}</Typography>

                    {offer?.type === 'click' ?

                        <Button variant='contained' onClick={() => offer?.onClick(rowData?.id)} disabled={loading}>Download</Button>
                        :
                        <Link to={offer?.link} className="link" target="_blank" rel="noopener noreferrer">
                            <Button variant='contained'>Download</Button>
                        </Link>
                    }
                </div>
                ) : <p>No Offer Files</p>
            }

            <div className='file-list-footer' onClick={() => handleClose()}>
                <CloseIcon sx={{ fontSize: '12px', fontWeight: 'bold' }} color='primary' /> <Typography variant='caption' color='primary' fontWeight={600}>Close</Typography>
            </div>
        </>)
    }

    return (
        <>
            <AlertDialog
                title='Select Reason to Release RFQ' content='' onAgree={() => { }} cancel={() => handleClose()}
                Component={<OfferList />}
                open={open}
                showCard={false}
                borderRadius='20px'
            />
            {!isSingleRfqPage ?
                <FolderIcon onClick={() => handleOpen()} color='secondary' className='cursor-pointer' />
                :
                <Button variant="outlined" size="small" className="material-request-btn" style={{ marginTop: '10px' }} onClick={() => handleOpen()}>
                    Specs Sheet
                </Button>}
        </>
    )
}
export default PDFList