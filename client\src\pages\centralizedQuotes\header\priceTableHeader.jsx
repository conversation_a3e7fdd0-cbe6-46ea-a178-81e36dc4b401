import { Button } from "@mui/material";
import { useNavigate } from "react-router";
import { centralizedPageUrl } from "utils/constant";

const PriceTableHeader = ({ formData }) => {
  const navigate = useNavigate();

  const handleClose = () => {
    // Encode the formData
    const encodedData = encodeURIComponent(JSON.stringify(formData));

    // Navigate to the centralized page and pass the formData in the URL
    navigate(`${centralizedPageUrl}/${encodedData}`);
  };

  return (
    <div className='button-wrapper'>
      <Button variant="contained" onClick={handleClose}>
        Close
      </Button>
    </div>
  );
};

export default PriceTableHeader;
