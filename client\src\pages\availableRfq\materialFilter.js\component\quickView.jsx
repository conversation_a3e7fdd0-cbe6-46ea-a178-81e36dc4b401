import { Button } from '@mui/material';
import Modal from './materialDialogBox';
import { useEffect, useState } from 'react';
import { get } from 'lodash';
import { useDispatch, useSelector } from 'react-redux';
import { getSingleRfq, setSingleRfqAction } from 'redux/reducers/singleRfq';
import { useNavigate } from 'react-router';
import { COUNTRY_MANAGER, IRRELEVANT, NEW, QUOTING, RESERVED, reserveRfqPageUrl, singleRfqPageUrl } from 'utils/constant';
import { getUserDetail } from 'utils/auth';
import { assignRfqAction } from 'redux/reducers/RfqReducer';
import { assignAlertMessage } from 'utils/validationMessage';
import { handleReserveClick, showAlert } from 'utils/helper';
import RfqModelSkeletonCard from './skeletonCard';
import { reservedRfq } from 'pages/availableRfq/reservedRfq';

const QuickView = ({ materialRequestedData, isReservedRFQ, redirectTo, order, variant, nextId, loading, currentPage, filters }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [singleRfq, setSingleRfq] = useState({});
  const [actions, setActions] = useState([]);
  const dispatch = useDispatch();
  const singleRfqData = useSelector((state) => state.singleRfq);
  const navigate = useNavigate();

  const handleOpen = () => {
    dispatch(getSingleRfq(order?.RFQ_ID));
    setIsModalOpen(true);
  };

  const handleClose = () => {
    setIsModalOpen(false);
    dispatch(setSingleRfqAction({}));
  };

  const handleReserve = async () => {
    const { RFQ_ID } = order;
    const user = getUserDetail();
    const response = await dispatch(
      assignRfqAction({
        rfqId: RFQ_ID?.toString(),
        status: RESERVED,
        userId: user?.userId?.toString(),
        currentPage: currentPage,
        filters: [reservedRfq]
      })
    );
    if (response) {
      const { success } = get(response, 'payload', {});
      if (success) {
        handleClose();
        navigate(reserveRfqPageUrl);
      }
    }
  };

  const handleClick = async () => {
    const { isManuallyAdded } = order;
    const response = await handleReserveClick(dispatch, isManuallyAdded);
    if (response) {
      handleReserve();
    }
  };

  const handleOpenBtn = () => {
    const url = `${redirectTo}/${get(order, 'RFQ_ID', '#')}`;
    window.open(url, '_blank');
  };

  const handleNext = () => {
    const currentIndex = materialRequestedData.findIndex((rfq) => rfq.RFQ_ID === get(singleRfqData, 'data.RFQ_ID', ''));
    const nextIndex = currentIndex + 1;
    if (nextIndex < materialRequestedData.length) {
      const nextRfqId = materialRequestedData[nextIndex].RFQ_ID;
      dispatch(getSingleRfq(nextRfqId));
    }
  };

  const handleUpdateActions = () => {
    const user = getUserDetail();
    const updatedActions = [
      {
        text: 'Next',
        handler: handleNext,
        autoFocus: false,
        variant: 'outlined',
        disable: loading
      },
      { text: 'Open', handler: handleOpenBtn, autoFocus: true, variant: 'outlined' }
    ];

    // if (!get(singleRfqData, 'data', {})?.CurrentStatus || get(singleRfqData, 'data', {})?.CurrentStatus === NEW) {
    if (!isReservedRFQ && user?.role !== COUNTRY_MANAGER) {
      updatedActions.push({ text: 'Reserve', handler: handleClick, autoFocus: true, variant: 'contained' });
    }

    setActions(updatedActions);
  };

  useEffect(() => {
    setSingleRfq(get(singleRfqData, 'data', {}));
    handleUpdateActions();
  }, [singleRfqData?.data]);

  const isLoading = () => {
    return get(singleRfqData, 'status') === 'loading';
  };

  return (
    <>
      {isLoading() ? (
        <RfqModelSkeletonCard loading={isLoading()} handleClose={handleClose} open={isModalOpen} />
      ) : (
        <Modal
          icon={get(singleRfq, 'Logo', '')}
          title={get(singleRfq, 'RFQ_Name', '')}
          actions={actions}
          loading={isLoading()}
          secondaryContent={get(singleRfq, 'Materials', [])}
          open={isModalOpen}
          handleClose={handleClose}
        />
      )}

      <Button variant={variant} size="small" className="material-request-btn small-btn" onClick={() => handleOpen()}>
        Quick View
      </Button>
    </>
  );
};

export default QuickView;
