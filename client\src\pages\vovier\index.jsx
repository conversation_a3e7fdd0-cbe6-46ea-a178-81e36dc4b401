import React, { useEffect, useState } from 'react';
import ActionButton from '../component/actionButton';
import { Card, CardContent, Divider, Typography } from '@mui/material';
import TableComponent from 'pages/component/table/table';
import { Link, useLocation, useParams } from 'react-router-dom';
import { route_url } from 'config';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useDispatch, useSelector } from 'react-redux';
import { getMaterialHistory } from 'redux/reducers/historyReducer';
import { get } from 'lodash';
import Loader from 'components/Loader';
import { COUNTRY_MANAGER, historyPageUrl, reservedHistoryPageUrl, ReservedSingleRfqPageUrl, singleRfqPageUrl } from 'utils/constant';
import { getUserDetail } from 'utils/auth';
const Vovier = () => {
  const materialId = useParams()?.id;
  const rfqId = useParams()?.rfqId;
  const dispatch = useDispatch();
  const location = useLocation();
  const historyData = useSelector((state) => state.history);
  const [historyRows, setHistoryRows] = useState([]);
  const userDetail = getUserDetail();

  useEffect(() => {
    if (materialId) {
      dispatch(getMaterialHistory(materialId));
    }
  }, [materialId]);

  useEffect(() => {
    if (historyData && historyData.status === 'succeeded') {
      const newData = get(historyData, 'data.History', [])?.map((item, index) => ({
        id: get(item, 'QuoteID', index),
        client: (get(item, 'ContactName', '') || '') + ' ' + (get(item, 'ContactLastname', '') || '-'),
        kam: get(item, 'KAM'),
        date: get(item, 'DeliveryDate.value', ''),
        value: get(item, 'UnitPrice'),
        offer: get(item, 'OfferedPrice'),
        offered: get(item, 'Offered', false),
        status: get(item, 'status') === 'Won' ? true : false,
        quantity: get(item, 'Quantity'),
        supplierId: get(item, 'SupplierID'),
        supplierName: get(item, 'Name'),
        RFQID: get(item, 'RFQID'),
        totalCost: get(item, 'TotalCost'),
        sh: get(item, 'ShippingCost'),
        MaterialID: get(item, 'MaterialID'),
        offerCurrency: get(item, 'OfferCurrency'),
        currency: get(item, 'UnitCurrency'),
        margin: get(item, 'Margin')
      }));

      setHistoryRows(newData);
    } else {
      setHistoryRows([]);
    }
  }, [historyData]);
  
  const headers = [
    {
      name: 'date',
      type: 'date',
      title: 'Date',
      sortingactive: true
    },
    {
      name: 'supplierName',
      type: 'text',
      title: 'Supplier',
      sortingactive: true
    },
    {
      name: 'client',
      type: 'text',
      title: 'Client',
      sortingactive: true
    },
    {
      name: 'value',
      type: 'text',
      title: 'Value',
      sortingactive: true
    },
    {
      name: 'quantity',
      type: 'text',
      title: 'Quantity',
      sortingactive: true
    },
    {
      name: 'currency',
      type: 'text',
      title: 'Currency',
      sortingactive: true
    },
    {
      name: 'sh',
      type: 'text',
      title: 'Sh',
      sortingactive: true
    },
    {
      name: 'totalCost',
      type: 'text',
      title: 'Total Cost',
      sortingactive: true
    },
    {
      name: 'offer',
      type: 'text',
      title: 'Offer',
      sortingactive: true
    },
    {
      name: 'offerCurrency',
      type: 'text',
      title: 'Offer Currency',
      sortingactive: true
    },
    {
      name: 'margin',
      type: 'text',
      title: 'Margin',
      sortingactive: true
    },
    {
      name: 'kam',
      type: 'text',
      title: 'Kam',
      sortingactive: true
    },
    {
      name: 'actions',
      btnType: 'checkbox',
      type: 'actions',
      title: 'Offered',
      checkboxName: 'offered',
      sortingactive: false,
      component: ActionButton,
      buttonOnClick: (type, id) => {
        // setHistoryRows((prevResponse) => {
        //   const updatedMaterials = prevResponse.map((response) => {
        //     if (response?.id === id) {
        //       return {
        //         ...response,
        //         offered: !response?.offered
        //       };
        //     }
        //     return response;
        //   });
        //   return updatedMaterials;
        // });
      }
    },
    {
      name: 'actions',
      btnType: 'checkbox',
      type: 'actions',
      title: 'Won',
      checkboxName: 'status',
      sortingactive: false,
      component: ActionButton,
      buttonOnClick: (type, id) => {
        // setHistoryRows((prevResponse) => {
        //   const updatedMaterials = prevResponse.map((response) => {
        //     if (response?.id === id) {
        //       return {
        //         ...response,
        //         status: !response?.status
        //       };
        //     }
        //     return response;
        //   });
        //   return updatedMaterials;
        // });
      }
    }
  ];

  if (get(historyData, 'status') === 'loading') {
    return <Loader />;
  }

  const previousPage = location.pathname.includes(historyPageUrl)
    ? singleRfqPageUrl
    : location.pathname.includes(reservedHistoryPageUrl)
      ? ReservedSingleRfqPageUrl
      : '#';

  return (
    <div>
      <div style={{ display: 'flex' }}>
        <Link to={`${previousPage}/${rfqId || '#'}`}>
          <ArrowBackIcon color="primary" />
        </Link>
        <Typography variant="h5" color="primary">
          Vovier
        </Typography>
      </div>
      <Card>
        <Divider />
        <CardContent sx={{ width: '100%', overflow: 'hidden' }}>
          <Typography variant="h5" color="secondary">
            {get(historyData, 'data.MaterialDescription', '')}
          </Typography>
          <Typography variant="body1">Part Number : {get(historyData, 'data.PartNumber', '')}</Typography>
          <TableComponent
             columns={
              userDetail?.role === COUNTRY_MANAGER
                ? headers?.filter((item) => item?.name !== 'supplierName')
                : headers
            }
            rows={historyRows}
            title="Vovier"
            titleLink="dashboard"
            enablePagination={true}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default Vovier;
