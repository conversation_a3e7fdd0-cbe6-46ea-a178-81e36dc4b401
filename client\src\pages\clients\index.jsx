import Grid from '@mui/material/Grid';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { get } from 'lodash';
import { Card, CardContent, Divider, IconButton, Typography } from '@mui/material';
import TableComponent from 'pages/component/table/table';
import ActionButton from 'pages/component/actionButton';
import { useNavigate } from 'react-router';
import { addClientPageUrl, APPROVED, REJECTED, updateClientPageUrl, updateSupplierPageUrl } from 'utils/constant';
import Loader from 'components/Loader';
import { getClientList } from 'redux/reducers/offerReducer';
import AlertDialog from 'pages/component/dialogbox';
import { deleteClientAction } from 'redux/reducers/clientReducer';
import { tabsData } from './constant';
import OfferMaterialActionButton from 'pages/offer/offerMaterial/offerMaterialActionButton';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';

export default function Clients() {
  const [clients, setClients] = useState([]);
  const [allClients, setAllClients] = useState([]);
  const clientList = useSelector((state) => state.offer);
  const clientsData = useSelector((state) => state.client);
  const [open, setOpen] = useState(false);
  const [clientId, setClientId] = useState('');
  const [reason, setReason] = useState('');

  const dispatch = useDispatch();
  const navigate = useNavigate();
  useEffect(() => {
    const clientRows = get(clientList, 'clientList', []).map((item) => {
      const { Name, Address, BusinessActivity, Status, Reject_Reason, Municipality, ClientID, Web } = item || {};
      return {
        name: Name,
        id: ClientID,
        address: Address,
        businessActivity: BusinessActivity,
        municipality: Municipality,
        status: Status,
        reason: Reject_Reason,
        showReason: Status === REJECTED,
        isApprovedOrReject: Status === REJECTED || Status === APPROVED
      };
    });

    setClients(clientRows);
    setAllClients(clientRows);
  }, [clientList?.clientList]);

  useEffect(() => {
    dispatch(getClientList());
  }, [dispatch]);

  const headers = [
    {
      name: 'id',
      type: 'RFQ Id',
      title: 'Client ID',
      sortingactive: true,
      minWidth: '100px'
    },
    {
      name: 'name',
      type: 'text',
      title: 'Name',
      sortingactive: true,
      maxWidth: '100px'
    },
    {
      name: 'address',
      type: 'text',
      title: 'Address',
      sortingactive: true,
      maxWidth: '100px'
    },
    {
      name: 'businessActivity',
      type: 'text',
      title: 'Business Activity',
      sortingactive: true
    },
    {
      name: 'municipality',
      type: 'text',
      title: 'Municipality',
      sortingactive: true
    },
    {
      name: 'status',
      type: 'badge',
      title: 'Status',
      sortingactive: true,
      className: 'status-badge'
    },
    {
      name: 'actions',
      btnType: 'infoIcon',
      type: 'actions',
      toolTipName: 'reason',
      title: 'Reason',
      minWidth: '100px',
      sortingactive: false,
      component: ActionButton,
      showButton: 'showReason',
      buttonOnClick: (type, id, materialId, index, data) => {}
    },

    {
      name: 'actions',
      btnType: 'multipleButton',
      type: 'actions',
      btnName: 'use',
      title: 'Action',
      sortingactive: false,
      minWidth: '100px',

      component: ActionButton,

      multipleButtons: [
        {
          type: 'icon',
          icon: <EditOutlinedIcon fontSize="16px" />,
          buttonOnClick: (type, rowData) => {
            navigate(`${updateClientPageUrl}/${rowData?.id}`);
          },
          color: 'primary',
          tooltip: 'Edit',
          showButton: 'isApprovedOrReject'
        },
        {
          icon: <DeleteOutlineOutlinedIcon fontSize="16px" />,
          type: 'icon',
          buttonOnClick: (type, rowData) => {
            setClientId(rowData?.id);
            setOpen(true);
          },
          color: 'error',
          tooltip: 'Delete'
        }
      ]
    }
  ];

  const handleSearch = (filteredRows, searchValue) => {
    setClients(filteredRows);
  };

  const actionBtns = [{ title: 'Add Client', onClick: () => navigate(addClientPageUrl) }];
  const isLoading = () => {
    return clientList?.loading || clientsData?.status === 'loading';
  };

  const handleClose = () => {
    setOpen(false);
    setClientId('');
  };
  const handleDelete = async () => {
    const response = await dispatch(deleteClientAction(clientId));
    const { success } = get(response, 'payload', {});
    if (success) {
      handleClose();
    }
  };

  const buttons = [
    { label: 'Cancel', onClick: () => handleClose(), variant: 'outlined', color: 'error' },
    { label: 'Yes', onClick: () => handleDelete(), variant: 'contained', color: 'primary' }
  ];

  return (
    <Grid container spacing={2}>
      <AlertDialog
        Component={
          <Typography variant="body1" color="secondary">
            {!reason ? 'Are you sure you want to delete this client' : 'Reason'}
          </Typography>
        }
        open={open}
        showCard={false}
        borderRadius="20px"
        buttons={buttons}
      />
      {isLoading() && <Loader />}
      <Grid item xs={12}>
        <div>
          <Card>
            <Divider />
            <CardContent sx={{ width: '100%', overflow: 'hidden' }}>
              <TableComponent
                maxHeight={'100%'}
                enablePagination={true}
                columns={headers}
                rows={clients || []}
                title="MySupplierTable"
                enableSearch={true}
                handleSearch={(data, searchValue) => handleSearch(data, searchValue)}
                allRows={allClients || []}
                actionBtns={actionBtns}
                placeActionButtonsIn="search"
                isBadgeFilter={true}
                badgeFilterData={tabsData || []}
                showDeleteIcon={true}
              />
            </CardContent>
          </Card>
        </div>
      </Grid>
    </Grid>
  );
}
