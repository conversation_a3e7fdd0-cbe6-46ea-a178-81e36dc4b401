import React from 'react';
import PropTypes from 'prop-types';
import NoImagePlaceHolder from '../../assets/images/users/No-Image-Placeholder.svg.png'
const Image = ({ src, alt, className }) => {
  const imageOnError = (event) => {
    event.target.src = NoImagePlaceHolder;
  };
  return <img className={className} src={src||NoImagePlaceHolder} alt={alt} onError={(e) => imageOnError(e)} />;
};
Image.propTypes = {
  src: PropTypes.string.isRequired,
  alt: PropTypes.string.isRequired,
  className: PropTypes.string
};
export default Image;
