import {
  alpha,
  Box,
  Grid,
  <PERSON><PERSON>temAvatar,
  ListItemButton,
  ListItemSecondaryAction,
  ListItemText,
  Select,
  Stack,
  Tooltip,
  Typography
} from '@mui/material';
import { capitalize, get } from 'lodash';
import AvatarComponent from 'pages/component/avatar';
import Image from 'pages/component/image';
import { useDispatch } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { IRRELEVANT, QUOTING, RESERVED, addRfqApiUrl, addRfqPageUrl, emailResponsesPageUrl, singleRfqPageUrl } from 'utils/constant';
import { showAlert } from 'utils/helper';
import { assignAlertMessage } from 'utils/validationMessage';
import EditIcon from '@mui/icons-material/Edit';
import './material.css';
import config from 'config';
import Part_Number_Available_img from '../../assets/images/logo/Part_Number_Available .webp';
import Part_Number_Detected_img from '../../assets/images/logo/Part_Number_Detected .png';
import hot_brand_image_img from '../../assets/images/logo/Hot_Brand.png';
import MarkEmailUnreadIcon from '@mui/icons-material/MarkEmailUnread';

const MaterialCard = ({
  filters,
  pageUrl,
  currentPage,
  isReservedRFQ,
  redirectTo,
  materialRequestedData,
  nextId,
  order,
  buttons,
  secondaryContent,
  kamList,
  loading,
  editPageUrl
}) => {
  const { RFQ_ID, RFQ_Name, Logo, Part_Number_Available, Part_Number_Detected, Hot_Brand, Email_Status } = order || {};
  const style = {
    rfqTitle: {
      color: config?.primaryColor
    },
    prioritization_img: {
      width: Hot_Brand && !Part_Number_Available ? '100px' : '80px',
      height: Hot_Brand && !Part_Number_Available ? '65px' : '80px',
      borderRadius: '50%',
      objectFit: 'contain'
    }
  };
  const getImage = () => {
    const { Part_Number_Available, Part_Number_Detected, Hot_Brand } = order || {};
    if (Part_Number_Available) {
      return Part_Number_Available_img;
    } else if (Hot_Brand) {
      return hot_brand_image_img;
    } else if (Part_Number_Detected) {
      return Part_Number_Detected_img;
    }
  };

  const navigateToSingleRfq = () => {
    let url = `${redirectTo}/${get(order, 'RFQ_ID', '#')}`;
    window.open(url, '_blank');
  };

  const handleCardClick = (event) => {
    event.preventDefault();
    navigateToSingleRfq();
  };

  const shouldShowImage = (item) => {
    return item?.type === 'img' && (Part_Number_Available || Part_Number_Detected || Hot_Brand);
  };

  return (
    <ListItemButton
      sx={
        order &&
        order?.CurrentStatus &&
        order?.CurrentStatus === 'VOID' && {
          bgcolor: (theme) => alpha(theme.palette.error.main, 0.06),
          '&:hover': {
            backgroundColor: '#8f292a1a !important'
          }
        }
      }
      divider
      key={order.id}
      className="material-card"
      onClick={handleCardClick}
    >
      <ListItemAvatar className="material-icon">
        <Image src={Logo || ''} className="company-image" />
      </ListItemAvatar>
      <Grid item xs>
        <ListItemText
          primary={
            <Box display={'flex'} gap={2}>
              <Typography variant="subtitle1" color="secondary">
                <span style={{ ...style?.rfqTitle }}>{RFQ_ID || ''}</span> - {capitalize(RFQ_Name) || 'N/A'}{' '}
                {order?.CurrentStatus === 'VOID' && <span style={{ color: '#f00c0c' }}>({order?.CurrentStatus})</span>}
              </Typography>

              {Email_Status === 'REPLIED' && (
                <Box
                  onClick={(event) => {
                    event.stopPropagation();
                    window.open(`${emailResponsesPageUrl}/${RFQ_ID}`, '_blank');
                  }}
                >
                  <Tooltip title="Price Request Email Reply Recieved">
                    <MarkEmailUnreadIcon color="success" fontSize="small" />
                  </Tooltip>
                </Box>
              )}
            </Box>
          }
          className="secondary-container material-dates"
        />
        <Grid container className="secondary-container" spacing={2} pl={3}>
          {secondaryContent?.map((item, index) => (
            <Grid item xs={12} sm={6} md={4} key={index} p={0}>
              <Box display="flex" flexDirection="row" justifyContent="space-between" ml={2} p={0} alignItems={'center'}>
                {item?.type === 'text' && (
                  <Typography variant="body2" mt={2}>
                    {`${item?.label}: ${item?.value || 'N/A'}`}
                  </Typography>
                )}
                {/* 
                {index === 2 && Email_Status === 'REPLIED' && (
                  <Box pb={'25px'}>
                    <Tooltip title="Price Request Email Reply Recieved">
                      <MarkEmailUnreadIcon color="success" fontSize='small'/>
                    </Tooltip>
                  </Box>
                )} */}

                {shouldShowImage(item) && (
                  <Box display="flex" justifyContent="center" alignItems="center" style={{ flex: 1 }}>
                    <img src={getImage()} style={style?.prioritization_img} alt="Part Number" />
                  </Box>
                )}
              </Box>
            </Grid>
          ))}
        </Grid>
      </Grid>
      <div
        onClick={(event) => {
          event.stopPropagation();
        }}
      >
        <Stack alignItems="flex-end" spacing={1}>
          {order?.isManuallyAdded && (
            <Link to={`${editPageUrl}/${RFQ_ID || '#'}`} className="link">
              <EditIcon fontSize="10px" />
            </Link>
          )}
          {buttons?.map((button, index) => {
            switch (button.type) {
              case 'button':
                return (
                  <button.buttonComponent
                    materialRequestedData={materialRequestedData}
                    nextId={nextId}
                    pageUrl={pageUrl}
                    isReservedRFQ={isReservedRFQ}
                    redirectTo={redirectTo}
                    key={index}
                    order={order}
                    variant={button?.variant}
                    selectOptions={kamList}
                    loading={loading}
                    currentPage={currentPage}
                    filters={filters}
                    smallBtn={true}
                  />
                );
              case 'image':
                return (
                  <>
                    <Grid display="flex">
                      <Box display="block" mr={1} style={{ textAlign: 'right', marginTop: '8px' }}>
                        <Box display="block">
                          <Typography variant="caption" color="secondary" fontWeight={600}>
                            {get(button, 'name')}
                          </Typography>
                        </Box>
                      </Box>
                      <AvatarComponent src={get(button, 'src')} height="35px" width="35px" alt={get(button, 'name', '')} />
                    </Grid>
                  </>
                );
              default:
                return null;
            }
          })}
        </Stack>
      </div>
    </ListItemButton>
  );
};

export default MaterialCard;
