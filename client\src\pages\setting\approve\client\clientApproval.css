/* ClientRejectionPage.css */



.paper {
  padding: 32px;
  border-radius: 16px;
  background-color: #f4f6f8;
}

.icon {
  font-size: 60px;
}

.title {
  font-weight: 700;
  text-shadow: 1px 1px #ccc;
}

.description {
  /* margin-bottom: 32px; */
}

.clientDetailsTitle {
  margin-bottom: 24px;
  font-weight: 600;
}

.textArea {
  width: 100%;
  border-radius: 8px;
}

.button {
  padding: 21px 32px;
  border-radius: 16px;
  background-color: #f44336;
  box-shadow: 0 3px 15px rgba(255, 0, 0, 0.3);
  transition: background-color 0.3s, transform 0.3s;
  margin:4px
}

.button:hover {
  background-color: #d32f2f;
  transform: scale(1.05);
}

.client-details {
  margin-bottom: 20px;
}

/* ClientDetailTable.css */

.gridContainer {
  display: flex;
  flex-wrap: wrap;
}



.cardContent {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
}

.iconBox {
  display: flex;
  align-items: center;
  padding-right: 16px;
}

.labelText {
  font-weight: 500;
  text-align: left;
  color: #000;
  /* text.primary */
}

.valueText {
  color: #6b6b6b;
  /* text.secondary */
  text-align: left;
}