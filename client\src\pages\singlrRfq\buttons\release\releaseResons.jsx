import React, { useState } from 'react';
import { Select, MenuItem, ListItemText, FormControl, InputLabel, Typography, FormControlLabel, Checkbox } from '@mui/material';
import { styled } from '@mui/system';
import MainCard from 'components/MainCard';
import SelectComponent from 'pages/component/selectComponent';

const CustomCheckbox = styled(Checkbox)(({ theme }) => ({
  '& .MuiSvgIcon-root': {
    fontSize: 14,
    borderRadius: '100px'
  }
}));

const ReleaseReason = ({ reasons, handleChange, returnToPool, selectedReason }) => {
  const handleSelectChange = (event) => {
    const { value, name } = event?.target || {};
    handleChange(name, value);
  };

  const handleCheckboxChange = (event) => {
    const { checked, name } = event?.target || {};
    handleChange(name, checked);
  };

  return (
    <>
      <Typography variant="h4" color="secondary" fontWeight={600} mb={2}>
        Select Reason to Release RFQ
      </Typography>
      <div style={{ display: 'inline-grid' }}>
        <Typography variant="subtitle1">Reason for Release</Typography>
        <FormControl sx={{ width: 400, mt: 1 }}>
          <SelectComponent
            id="release-reason"
            name="reason"
            value={selectedReason || ''}
            onChange={handleSelectChange}
            items={reasons || []}
          />
        </FormControl>
        <FormControlLabel
          sx={{ marginLeft: '-5px', borderRadius: '8px', mt: 1 }}
          control={
            <CustomCheckbox
              checked={returnToPool || false}
              value={returnToPool || false}
              onChange={handleCheckboxChange}
              name="returnToPool"
            />
          }
          label="Return RFQ to Pool"
        />
      </div>
    </>
  );
};

export default ReleaseReason;
