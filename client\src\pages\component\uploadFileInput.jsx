import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { TextField, Button, Box } from '@mui/material';

const FileUploadInput = ({ label, accept, multiple, onFileSelect ,error,fileName}) => {
  const fileInputRef = useRef(null);

  const handleFileChange = (event) => {
    const files = Array.from(event.target.files);
    if (onFileSelect) {
      let fileName=multiple ? files.map((file) => file.name).join(', ') : files[0]?.name || ''
      onFileSelect(files,fileName);
    }
  };

  const handleClick = () => {
    fileInputRef.current.click();
  };

  return (
    <Box display="flex" alignItems="center" gap={1}>
      <TextField value={fileName} placeholder="Choose file" fullWidth onClick={handleClick} error={error}/>
      <input type="file" accept={accept} multiple={multiple} ref={fileInputRef} style={{ display: 'none' }} onChange={handleFileChange} />
    </Box>
  );
};

FileUploadInput.propTypes = {
  label: PropTypes.string,
  accept: PropTypes.string,
  multiple: PropTypes.bool,
  onFileSelect: PropTypes.func
};

FileUploadInput.defaultProps = {
  accept: '*',
  multiple: false,
  onFileSelect: null
};

export default FileUploadInput;
