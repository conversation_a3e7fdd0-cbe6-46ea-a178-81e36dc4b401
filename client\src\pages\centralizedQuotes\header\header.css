.button-wrapper {
  display: flex;
  justify-content: flex-end;
  gap: 10px
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.actions-container {
  min-width: 50%;
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: space-between;
}

.select-component {
  width: 200px;
  height: 40px;
}

.buttons-container {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 5px;
}

.filter-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.button-wrapper {
  width: 100%;
  display: flex;
  gap: 10px;
}

.auto-select {
  min-width: 200px;
  background-color: white;
  border-radius: 8px;
}