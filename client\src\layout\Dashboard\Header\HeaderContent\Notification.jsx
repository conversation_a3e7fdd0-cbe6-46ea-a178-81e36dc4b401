import { useEffect, useRef, useState } from 'react';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Avatar from '@mui/material/Avatar';
import Badge from '@mui/material/Badge';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import MainCard from 'components/MainCard';
import Transitions from 'components/@extended/Transitions';
import BellOutlined from '@ant-design/icons/BellOutlined';
import { useDispatch, useSelector } from 'react-redux';
import { getNotifications, updateNotificaionStatus } from 'redux/reducers/notificationReducer';
import { getUserDetail } from 'utils/auth';
import NotificationCard from './notificationCard';
import noNotification from '../../../../assets/images/users/noNotification.svg'
import { showAllNotificationPageUrl } from 'utils/constant';
import { Link } from 'react-router-dom';
import Loader from 'components/Loader';
import { LIMIT } from './showAllNotifications';

const avatarSX = {
  width: 36,
  height: 36,
  fontSize: '1rem'
};

const actionSX = {
  mt: '6px',
  ml: 1,
  top: 'auto',
  right: 'auto',
  alignSelf: 'flex-start',
  transform: 'none'
};

export default function Notification() {
  const allNotifications = useSelector((state) => state.notification);
  const theme = useTheme();
  const matchesXs = useMediaQuery(theme.breakpoints.down('md'));
  const anchorRef = useRef(null);
  const [open, setOpen] = useState(false);
  const [showAll, setShowAll] = useState(false);
  const dispatch = useDispatch();
  const [notifications, setNotifications] = useState([]);
  const [read, setRead] = useState(0);

  const getNotification = async () => {
    const user = getUserDetail();
    const response = await dispatch(getNotifications({ UserID: user?.userId?.toString(), limit: LIMIT, page: 1 }));
    const updatedNotification = response?.payload?.messages || [];
    setNotifications(updatedNotification || []);
    setRead(updatedNotification?.filter((notify) => notify?.read === false).length);
  }
  useEffect(() => {
    const fetchNotifications = async () => {
      await getNotification();
    };

    fetchNotifications();
  }, []);



  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
    handleNotificationClick();
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  const notificationsToShow = showAll ? notifications : notifications.slice(0, 10);

  const handleNotificationClick = async () => {
    const user = getUserDetail();
    const payload = {
      UserID: user?.userId?.toString(),
    };
    if (notifications.some((notify) => !notify?.read)) {
      const response = await dispatch(updateNotificaionStatus(payload));
      const { success, data, message } = response?.payload || {}
      if (success) {
        getNotification()
      }
    }
  };

  const isLoading = () => {
    return allNotifications?.loading;
  };

  return (
    <Box sx={{ flexShrink: 0, ml: 0.75 }}>
      <Box sx={{ position: 'absolute', right: 0, top: 60 }} ref={anchorRef}></Box>
      {isLoading() && <Loader />}
      <IconButton
        color="secondary"
        variant="light"
        sx={{ color: 'text.primary', bgcolor: open ? 'grey.100' : 'transparent' }}
        aria-label="open profile"
        aria-controls={open ? 'notification-popper' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
      >
        <Badge badgeContent={read} color="primary">
          <BellOutlined />
        </Badge>
      </IconButton>
      <Popper
        placement={matchesXs ? 'bottom' : 'bottom-end'}
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        popperOptions={{ modifiers: [{ name: 'offset', options: { offset: [matchesXs ? -5 : 0, 9] } }] }}
      >
        {({ TransitionProps }) => (
          <Transitions type="grow" position={matchesXs ? 'top' : 'top-right'} in={open} {...TransitionProps}>
            <Paper sx={{ boxShadow: theme.customShadows.z1, width: '100%', minWidth: 285, maxWidth: { xs: 285, md: 420 }, height: '80vh' }}>
              <ClickAwayListener onClickAway={handleClose}>
                <MainCard
                  title="Notification"
                  elevation={0}
                  border={false}
                  content={false}
                >
                  <List
                    component="nav"
                    sx={{
                      p: 0,
                      height: 'calc(80vh - 64px)',
                      overflowY: 'auto',
                      backgroundColor: '#e5e7e9',
                      '& .MuiListItemButton-root': {
                        py: 0.5,
                        '&.Mui-selected': { bgcolor: 'grey.50', color: 'text.primary' },
                        '& .MuiAvatar-root': avatarSX,
                        '& .MuiListItemSecondaryAction-root': { ...actionSX, position: 'relative' }
                      }
                    }}
                  >
                    {notificationsToShow.length > 0 ? (
                      notificationsToShow.map((notification, index) => (
                        <NotificationCard
                          RFQID={notification?.RFQID}
                          key={index}
                          title={notification.message}
                          message={notification.sender}
                          sender={notification.sender}
                          date={notification.date}
                          notification={notification}
                          read={notification?.read}
                        />
                      ))
                    ) : (
                      <div style={{ textAlign: 'center', marginBottom: '10px' }}>
                        <img src={noNotification} alt="No Notification" />
                        <Typography variant='body1'>No Notification</Typography>
                      </div>
                    )}
                    <Divider />

                  </List>

                  <Link to={showAllNotificationPageUrl} className='link'>
                    <ListItemButton sx={{ textAlign: 'center', py: `${12}px !important`, backgroundColor: '#f5f5f5' }} onClick={() => setOpen(false)}>
                      <ListItemText
                        primary={
                          <Typography variant="h6" color="primary">
                            Show More
                          </Typography>
                        }
                      />
                    </ListItemButton>
                  </Link>

                </MainCard>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>
    </Box>
  );
}
