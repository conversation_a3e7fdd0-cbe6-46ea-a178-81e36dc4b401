import React from 'react';
import { Tooltip, IconButton, Button, Checkbox } from '@mui/material';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';
import CheckBoxInput from './table/checkboxInput';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import PropTypes from 'prop-types';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
const label = { inputProps: { 'aria-label': 'Checkbox demo' } };

const ActionButton = ({
  onClick,
  loading,
  showDeleteButton,
  column,
  type,
  disabled,
  value,
  disabledBtn,
  rowData,
  showDeleteIcon,
  isColumnAction,
  columnValues
}) => {
  const { multipleButtons, btnName, hideDeleteBtn, buttonTitle, toolTipName, btnValue, btnColor, btnVariant, className, toolTip } =
    column || {};

  const renderButton = () => {
    const multipleActionButtons = (multipleButton) => {
      const { buttonOnClick, disabledBtn, disabledKey, type, btnValue, buttonTitle, color, showButton, icon, columnName, toolTipTitle } =
        multipleButton || {};
      const isShowButton = showButton ? rowData[showButton] : true;
      if (isShowButton) {
        // Switch case to check for button, checkbox, or icon
        switch (type) {
          case 'button':
            return (
              <Button
                size="small"
                variant="contained"
                onClick={() => buttonOnClick(btnValue || '', rowData)}
                color={color || 'primary'}
                disabled={disabledBtn || disabled || rowData[disabledKey]}
              >
                {buttonTitle || null}
              </Button>
            );

          case 'icon': {
            if (!multipleButton?.isVisible || multipleButton?.isVisible(rowData)) {
              return (
                <IconButton
                  onClick={() => buttonOnClick(btnValue || '', rowData)}
                  color={color || 'primary'}
                  disabled={disabledBtn || disabled}
                >
                  {icon || null}
                </IconButton>
              );
            } else {
              return null;
            }
          }

          case 'checkbox':
            return (
              <Tooltip title={toolTipTitle} aria-label="">
                <IconButton aria-label="" onClick={() => buttonOnClick('checkbox')} disabled={disabledBtn || disabled}>
                  <Checkbox
                    disabled={disabledBtn || disabled}
                    checked={isColumnAction ? columnValues[columnName] : value}
                    value={isColumnAction ? columnValues[columnName] : value}
                  />
                </IconButton>
              </Tooltip>
            );

          default:
            return null; // If no valid type is provided, return nothing
        }
      }
    };
    switch (type) {
      case 'update':
        return (
          <>
            <Tooltip title={toolTip || ''}>
              <Button
                variant={btnVariant || 'contained'}
                size="small"
                onClick={() => onClick(btnValue || 'edit')}
                className={className}
                disabled={disabledBtn}
                color={btnColor || 'primary'}
              >
                {buttonTitle || 'Edit'}
              </Button>
            </Tooltip>
            {showDeleteButton && !hideDeleteBtn && (
              <Button
                variant="outlined"
                color="error"
                size="small"
                onClick={() => onClick('delete')}
                sx={{ marginLeft: '10px' }}
                disabled={disabledBtn}
              >
                Delete
              </Button>
            )}
          </>
        );

      case 'logPrice':
        return (
          <>
            <Button
              variant={btnVariant || 'contained'}
              size="small"
              onClick={() => onClick()}
              className={className}
              disabled={disabledBtn}
              color={btnColor || 'primary'}
            >
              Log Prices
            </Button>
          </>
        );

      case 'checkbox':
        return (
          <>
            {rowData[btnName] !== 'none' && (
              <Tooltip title="" aria-label="">
                <IconButton aria-label="" onClick={() => onClick('checkbox')} disabled={disabledBtn || disabled}>
                  <Checkbox disabled={disabledBtn || disabled} checked={value} value={value} />
                </IconButton>
              </Tooltip>
            )}
          </>
        );

      case 'pdf':
        return (
          <Tooltip title="" aria-label="">
            <IconButton aria-label="" onClick={() => onClick('pdf')}>
              <PictureAsPdfIcon />
            </IconButton>
          </Tooltip>
        );
      case 'infoIcon':
        return (
          rowData[toolTipName] && (
            <Tooltip title={rowData[toolTipName]} aria-label="">
              <IconButton aria-label="" onClick={() => onClick('info')}>
                <InfoOutlinedIcon sx={{ fontSize: '16px' }} />
              </IconButton>
            </Tooltip>
          )
        );

      case 'view':
        return (
          <Tooltip title="" aria-label="">
            <IconButton aria-label="" onClick={() => onClick('view')}>
              <RemoveRedEyeOutlinedIcon sx={{ fontSize: '16px' }} />
            </IconButton>
          </Tooltip>
        );

      case 'editIcon':
        return (
          <>
            <IconButton aria-label="" onClick={() => onClick('edit')}>
              <EditOutlinedIcon sx={{ fontSize: '16px' }} />
            </IconButton>
            {showDeleteIcon && (
              <IconButton aria-label="" onClick={() => onClick('delete')} color="error">
                <DeleteOutlineOutlinedIcon sx={{ fontSize: '16px' }} />
              </IconButton>
            )}
          </>
        );

      case 'multipleButton':
        return (
          <>
            <div style={{ display: 'flex', gap: '10px' }}>
              {multipleButtons?.map((multipleButton, index) => (
                <Tooltip key={index} title={multipleButton.tooltip || ''}>
                  {multipleActionButtons(multipleButton)}
                </Tooltip>
              ))}
            </div>
          </>
        );

      default:
        return null;
    }
  };

  return <>{renderButton()}</>;
};

ActionButton.propTypes = {
  onClick: PropTypes.func.isRequired,
  loading: PropTypes.bool,
  showDeleteButton: PropTypes.bool,
  column: PropTypes.shape({
    btnName: PropTypes.string,
    buttonTitle: PropTypes.string,
    btnValue: PropTypes.string,
    btnColor: PropTypes.string,
    btnVariant: PropTypes.string,
    className: PropTypes.string,
    multipleButtons: PropTypes.arrayOf(
      PropTypes.shape({
        icon: PropTypes.node,
        buttonOnClick: PropTypes.func,
        color: PropTypes.string,
        tooltip: PropTypes.string,
        disabledBtn: PropTypes.bool,
        btnValue: PropTypes.string
      })
    )
  }),
  type: PropTypes.string.isRequired,
  disabled: PropTypes.bool,
  value: PropTypes.any,
  disabledBtn: PropTypes.bool,
  rowData: PropTypes.object
};

ActionButton.defaultProps = {
  loading: false,
  showDeleteButton: false,
  column: {},
  disabled: false,
  value: null,
  disabledBtn: false,
  rowData: {}
};

export default ActionButton;
