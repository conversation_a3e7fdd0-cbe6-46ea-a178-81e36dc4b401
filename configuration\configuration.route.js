const {
  getAllConfigurations,
  updateAllConfigurations,
  updateApprover,
  addTimeSlot,
  getTimeSlots,
  addDashboard,
  editDashboard,
  deleteDashboard,
  createSettingTable,
  addOrUpdateContentOfLegalTerms,
  getContentOfLegalTerms,
} = require("./configuration.controller");
const { validate } = require("../middlewares/validation.middleware");
const {
  updateAllConfigurationsSchema,
  updateApproverSchema,
  addTimeSlotSchema,
  getTimeSlotsSchema,
  addDashboardSchema,
  editDashboardSchema,
  deleteDashboardSchema,
  addOrUpdateContentOfAdjustmentToLegalTermsSchema,
  getContentOfAdjustmentToLegalTermsSchema,
} = require("../configuration/configuration.validation");

const router = require("express").Router();

router.get("/fetch-all-configurations", getAllConfigurations);

router.post(
  "/update-all-configurations",
  validate(updateAllConfigurationsSchema, "body"),
  updateAllConfigurations
);

router.post(
  "/update-approver/:email",
  validate(updateApproverSchema, "params"),
  updateApprover
);

router.post("/addTimeSlot", validate(addTimeSlotSchema, "body"), addTimeSlot);

router.post(
  "/getTimeSlots/:UserID",
  validate(getTimeSlotsSchema, "params"),
  getTimeSlots
);

router.post(
  "/addDashboard",
  validate(addDashboardSchema, "body"),
  addDashboard
);

router.put(
  "/editDashboard",
  validate(editDashboardSchema, "body"),
  editDashboard
);

router.delete(
  "/deleteDashboard/:ID",
  validate(deleteDashboardSchema, "params"),
  deleteDashboard
);

router.get("/createSettingTable", createSettingTable);

router.get(
  "/getLegalTerms/:language",
  validate(getContentOfAdjustmentToLegalTermsSchema, "params"),
  getContentOfLegalTerms
);

router.post(
  "/updateLegalTerms",
  validate(addOrUpdateContentOfAdjustmentToLegalTermsSchema, "body"),
  addOrUpdateContentOfLegalTerms
);

module.exports = router;
