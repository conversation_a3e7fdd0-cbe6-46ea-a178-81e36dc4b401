// ActionCell.js
import React from 'react';
import { TableCell } from '@mui/material';
import { get } from 'lodash';
import PropTypes from 'prop-types';
const ActionCell = ({
  column,
  data,
  index,
  loading,
  showDeleteButton,
  generatePdf,
  showDeleteIcon,
  columnValues,
  isColumnAction
}) => {
  return (
    <TableCell sx={{ minWidth: column?.minWidth }} key={index}>
      <column.component
        btnName={column?.btnName}
        isColumnAction={isColumnAction}
        type={column?.btnType}
        showDeleteIcon={showDeleteIcon}
        column={column}
        loading={loading}
        onClick={(type) =>
          column?.buttonOnClick(type, data?.id || data?._id, get(data, 'parentId', ''), index, data)
        }
        rowData={data}
        data={get(data, `${[column?.name]}`, '')}
        generatePdf={generatePdf}
        disabled={get(data, `${[column?.isDisable]}`)}
        disabledBtn={get(data, `${[column?.isDisable]}`) || get(column, 'disableBtn', false)}
        showDeleteButton={showDeleteButton}
        value={get(data, `${[column?.checkboxName]}`, false)}
        columnValues={columnValues}
      />
    </TableCell>
  );
};

ActionCell.propTypes = {
  column: PropTypes.shape({
    minWidth: PropTypes.number,
    component: PropTypes.elementType.isRequired,
    btnName: PropTypes.string,
    btnType: PropTypes.string,
    buttonOnClick: PropTypes.func.isRequired,
    name: PropTypes.string,
    isDisable: PropTypes.string,
    checkboxName: PropTypes.string,
    disableBtn: PropTypes.bool,
  }).isRequired,
  data: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  loading: PropTypes.bool.isRequired,
  showDeleteButton: PropTypes.bool.isRequired,
  generatePdf: PropTypes.func,
};

export default ActionCell;
