const express = require("express");
const cors = require("cors");
const path = require("path");
const compression = require("compression");
const { CORS_ORIGIN, NODE_ENV, PORT } = require("./constants");
const { AuthGuard } = require("./middlewares/guard.middleware");

const app = express();

app.use(cors({ credentials: true, origin: CORS_ORIGIN }));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "1mb" }));
app.use(express.static("public"));
app.use(
  compression({
    level: 6,
    filter: (req, res) => {
      if (req.headers["x-no-compression"]) {
        return false;
      }
      return compression.filter(req, res);
    },
  })
);
const {
  updateRFQStatusCron,
  updateNoRFQStatusCron,
  mailSuppliersCron,
  expireRequestCron,
  lostOfferCron,
  reserveAutomatedRFQCron,
  automatedRFQRequest,
  updateRepliedRFQRequestStatusCron,
} = require("./utils/cron.util");
updateRFQStatusCron();
updateNoRFQStatusCron();
mailSuppliersCron();
expireRequestCron();
lostOfferCron();
reserveAutomatedRFQCron();
automatedRFQRequest();
updateRepliedRFQRequestStatusCron();

const userRoute = require("./user/user.route");
app.use("/api/users", AuthGuard, userRoute);

const clientRoute = require("./clients/client.route");
app.use("/api/clients", AuthGuard, clientRoute);

const rfqRoute = require("./rfq/rfq.route");
app.use("/api/rfq", AuthGuard, rfqRoute);

const materialRoute = require("./material/material.route");
app.use("/api/material", AuthGuard, materialRoute);

const supplierRoute = require("./supplier/supplier.route");
app.use("/api/supplier", AuthGuard, supplierRoute);

const quoteRoute = require("./quote/quote.route");
app.use("/api/quote", AuthGuard, quoteRoute);

const offerRoute = require("./offer/offer.route");
app.use("/api/offer", AuthGuard, offerRoute);

const configurationRoute = require("./configuration/configuration.route");
app.use("/api/configuration", AuthGuard, configurationRoute);

const automationRoute = require("./automation/automation.route");
app.use("/api/automation", AuthGuard, automationRoute);

const centralizedQuotingRoute = require("./centralizedQuoting/centralizedQuoting.route");
app.use("/api/centralizedQuoting", AuthGuard, centralizedQuotingRoute);

const centralizedMaterialQuotingRoute = require("./centralizedMaterialQuoting/centralizedMaterialQuoting.route");
app.use("/api/centralizedMaterialQuoting", AuthGuard, centralizedMaterialQuotingRoute);

if (NODE_ENV === "production") {
  app.use(express.static(path.join(__dirname, "client", "dist")));
  app.get("/", (req, res) => {
    res.sendFile(path.resolve(__dirname, "client", "dist", "index.html"));
  });
  app.get("/*", (req, res) => {
    res.sendFile(path.resolve(__dirname, "client", "dist", "index.html"));
  });
} else {
  app.get("/", (req, res) => {
    res.send(`RFQ app is running on port: ${PORT}`);
  });
}

module.exports = app;
