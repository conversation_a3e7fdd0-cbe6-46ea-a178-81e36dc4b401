/* eslint-disable no-unused-vars */
import Grid from '@mui/material/Grid';
// import OrdersTable from './OrdersTable';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { filter, get } from 'lodash';
import './myOffers.css';
import MyOfferTable from './myOfferTable/myOfferTable';
import MyOfferCard from './myOfferSummary';
import { getMyOfferAnalytics, getMyOfferDetail } from 'redux/reducers/myOffersReducer';
import { getUserDetail } from 'utils/auth';
import Loader from 'components/Loader';
import { LOST } from 'utils/constant';
import { defaultOfferFilters } from './myOfferTable/constant';
// ==============================|| DASHBOARD - DEFAULT ||============================== //
const badgeStatus = [
  { id: '1', badgeContent: 'Won', color: 'won' },
  { id: '2', badgeContent: 'Lost', color: 'won' }
];
const lostBadgeStatus = [
  { id: '1', title: 'LOST', type: 'text' },
  { id: '2', badgeContent: 'Won', color: 'won' }
];
export default function MyOffers() {
  const [myOffers, setMyOffers] = useState([]);
  const myOfferData = useSelector((state) => state.myOffer);
  const [allOffer, setAllOffer] = useState([]);
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [limit, setLimit] = useState(10);
  const [filters, setFilters] = useState(defaultOfferFilters);

  const dispatch = useDispatch();

  useEffect(() => {
    let offers = [];
    let user = getUserDetail();
    get(myOfferData, 'data.offers', [])?.map((offer) => {
      let {
        Delivery_Date,
        Portal,
        RFQ_Date,
        RFQ_ID,
        RFQ_Name,
        OfferDate,
        RFQ_Number,
        Status,
        TotalOfferPrice,
        URL,
        Company_Name,
        OfferID,
        role
      } = offer || {};
      let singleOffer = {
        id: OfferID,
        offerId: OfferID,
        rfqId: RFQ_ID,
        name: RFQ_Name,
        portal: Portal,
        number: RFQ_Number,
        date: OfferDate?.value,
        deliver: Delivery_Date?.value,
        client: Company_Name,
        totalOffer: TotalOfferPrice,
        kam: role,
        url: URL,
        pdf: '',
        status: !Status ? badgeStatus : Status === LOST ? lostBadgeStatus : Status
      };
      offers.push(singleOffer);
    });
    setTotalCount(get(myOfferData, 'data.count', 0));
    setMyOffers(offers);
    setAllOffer(offers);
  }, [myOfferData?.data]);

  useEffect(() => {
    let user = getUserDetail();
    let userId = get(user, 'userId', '');
    const payload = {
      limit: limit || 10,
      userId: userId?.toString() || '',
      page: page || 1,
      filterData: filters && filters?.length > 0 && validateFilter(filters)?.filter((filter) => filter?.data !== 'all')
    };

    dispatch(getMyOfferDetail(payload));
    dispatch(getMyOfferAnalytics());
  }, [page, limit, filters]);

  const onSwitch = (isOpen) => {
    let updatedFilters = filters?.map((filter) => {
      if (filter?.filterName === 'status') {
        return { ...filter, heading: isOpen ? 'Open' : 'All' };
      }
      return filter;
    });
    setFilters(updatedFilters || []);
  };

  const validateFilter = (filters) => {
    return filters?.filter((filter) => {
      if ((filter?.data != null && filter.data !== '') || filter?.type === 'select') {
        return true;
      }
      return false;
    });
  };

  const onPageChange = (page) => {
    setPage(page + 1);
  };
  const onPageLimitChange = (limit) => {
    setLimit(limit);
  };

  const handleSearch = (search, type) => {
    let updatedFilters = filters?.map((filter) => {
      if (filter?.filterName === 'search' && type === 'search') {
        return { ...filter, data: search };
      }
      if (filter?.type === 'sort' && type === 'sort' && filter?.filterName === 'offerDateSort') {
        return { ...filter, data: search };
      }
      return filter;
    });
    setFilters(updatedFilters || []);
    setPage(1);
  };
  const isLoading = () => {
    return get(myOfferData, 'status', '') === 'loading' || myOfferData?.loading;
  };

  const handlFilterChange = (e) => {
    handleSearch(e?.target?.value, 'sort');
  };

  const getSortingField = (sortingData) => {
    switch (sortingData?.field) {
      case 'rfqId':
        return 'r.RFQ_ID';
      case 'name':
        return 'r.RFQ_Name';
      case 'portal':
        return 'r.Portal';
      case 'number':
        return 'r.RFQ_Number';
      case 'url':
        return 'r.URL';
      case 'deliver':
        return 'r.Delivery_Date';
      case 'client':
        return 'r.Company_Name';
      case 'date':
        return 'o.OfferDate';
      case 'totalOffer':
        return 'o.TotalOfferPrice';
      case 'kam':
        return 'u.FirstName';
    }
  };

  const handleSort = (sortingData) => {
    let updatedFilters = filters?.map((filter) => {
      if (filter?.filterName === 'freeSort') {
        return { ...filter, field: getSortingField(sortingData), data: sortingData?.data || 'ASC' };
      }
      return filter;
    });

    setFilters(updatedFilters || []);
    setPage(1);
  };

  return (
    <Grid container spacing={2}>
      {isLoading() && <Loader />}
      <Grid item xs={12}>
        <MyOfferCard onSwitch={onSwitch} cardDetails={get(myOfferData, 'analyticsData', {})} />
      </Grid>
      <Grid item xs={12}>
        <MyOfferTable
          handlFilterChange={handlFilterChange}
          rows={myOffers || []}
          allRows={allOffer}
          applyFilterOnEnter={true}
          handleSort={handleSort}
          onPageChange={onPageChange}
          onPageLimitChange={onPageLimitChange}
          totalCount={totalCount}
          filters={validateFilter(filters)}
          page={page}
          handleSearch={(data, searchValue) => handleSearch(searchValue, 'search')}
          loading={isLoading()}
        />
      </Grid>
    </Grid>
  );
}
