const { validate } = require("../middlewares/validation.middleware");
const {
  getQuote,
  calculateQuote,
  deleteQuote,
  updateQuoteOfferStatus,
  calculateMargin,
} = require("./quote.controller");
const {
  calculateQuoteSchema,
  getQuoteSchema,
  deleteQuoteSchema,
  updateQuoteOfferStatusSchema,
  calculateMarginSchema,
} = require("./quote.validation");

const router = require("express").Router();

router.post("/get/:RFQ_ID", validate(getQuoteSchema, "params"), getQuote);

router.post(
  "/update/status",
  validate(updateQuoteOfferStatusSchema, ["body"]),
  updateQuoteOfferStatus
);

router.post(
  "/calculate",
  validate(calculateQuoteSchema, ["body"]),
  calculateQuote
);

router.delete(
  "/delete/:id",
  validate(deleteQuoteSchema, ["params"]),
  deleteQuote
);

router.post(
  "/calculate-margin",
  validate(calculateMarginSchema, "body"),
  calculateMargin
);

module.exports = router;
