import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { post } from 'utils/axios';
import { notificationApiUrl, updateNotificationApiUrl } from 'utils/constant';

export const getNotifications = createAsyncThunk(notificationApiUrl, async (data, { dispatch }) => {
  try {

    const response = await post(notificationApiUrl, data, true, dispatch);
    if (response) {
      dispatch(notificationSlice?.actions?.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        return data;
      }
      if (!success) {

        return [];
      }
    } else {
      dispatch(notificationSlice?.actions?.setLoading(false));
    }
  } catch (error) {
    dispatch(notificationSlice?.actions?.setLoading(false));
    return [];
  }
});

export const updateNotificaionStatus = createAsyncThunk(updateNotificationApiUrl, async (data, { dispatch }) => {
  const {page,limit,...rest}=data||{}
  let payload={...rest}

  try {
    dispatch(notificationSlice?.actions?.setLoading(true));
    const response = await post(updateNotificationApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(notificationSlice?.actions?.setLoading(false));
      const { error, success, message } = get(response, 'data', {});
      if (success) {

        return {success:success,data};
      }
      if (!success) {

        return {success:false,data:[]}
      }
    } else {
      dispatch(notificationSlice?.actions?.setLoading(false));
    }
  } catch (error) {
    dispatch(notificationSlice?.actions?.setLoading(false));
    return {success:false,data:[]};
  }
});



const notificationSlice = createSlice({
  name: 'notification',
  initialState: {
    data: [],
    status: 'idle',
    error: null,
    loading: false,
  },
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
  }
  ,
  extraReducers: (builder) => {
    builder
      .addCase(getNotifications.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getNotifications.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload;
      })
      .addCase(getNotifications.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload || 'Failed to fetch RFQ data';
      });
  }
});
export const { setLoading } = notificationSlice.actions;
export default notificationSlice.reducer;
