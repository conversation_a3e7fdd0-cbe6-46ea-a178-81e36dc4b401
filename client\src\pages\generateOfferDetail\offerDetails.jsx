import React from 'react';
import { <PERSON><PERSON><PERSON>, TextField, Grid, Select, MenuItem, FormControl, InputLabel, Stack, Button } from '@mui/material';
import InputField from 'pages/component/inputField';
import { get } from 'lodash';
import TextAreaComponent from 'pages/component/textArea';
import SelectComponent from 'pages/component/selectComponent';
import { getApprovedClients } from 'utils/helper';
import DynamicAutocomplete from 'pages/component/autoComplete';

const renderFormControl = (option, onChange, value) => {
  switch (option.type) {
    case 'select':
      return (
        <SelectComponent
          className="offer-detail-select"
          onChange={onChange}
          name={option?.name}
          value={value || null}
          items={option?.values}
        />
      );

    case 'autocomplete':
      return (
        <DynamicAutocomplete
          options={option?.values || []}
          // label={option?.label}
          placeholder={`Search and select ${option?.label.toLowerCase()}`}
          value={option?.values?.find((item) => item.value === value) || null}
          onChange={(event, newValue) => {
            const syntheticEvent = {
              target: {
                name: option?.name,
                value: newValue ? newValue.value : ''
              }
            };
            onChange(syntheticEvent);
          }}
          getOptionLabel={(optionItem) => optionItem?.label || ''}
          isLoading={false}
          margin={'5px 0px'}
        />
      );
    case 'input':
      return (
        <InputField
          type={option?.inputType || 'text'}
          placeholder="Enter your search"
          value={value || ''}
          name={option?.name}
          onChange={(e) => onChange(e)}
          className="generate-offer-input-field"
        />
      );
    default:
      return null;
  }
};

const OfferDetails = ({ onChange, handleNotesChange, offer, clientList, calculateOffer, loading }) => {
  const options = [
    {
      label: 'Offer Currency',
      values: [
        { label: 'USD', value: 'USD' },
        { label: 'EUR', value: 'EUR' },
        { label: 'GBP', value: 'GBP' },
        { label: 'CLP', value: 'CLP' },
        { label: 'AUD', value: 'AUD' }
      ],
      type: 'autocomplete',
      name: 'offerCurrency'
    },
    {
      label: 'Language',
      values: [
        { label: 'Spanish', value: 'Spanish' },
        { label: 'English', value: 'English' }
      ],
      type: 'autocomplete',
      name: 'language'
    },

    { label: 'Client', values: getApprovedClients(clientList), type: 'autocomplete', name: 'clientId' },
    { label: 'Valid for', values: '', type: 'input', inputType: 'number', name: 'validFor' },
    { label: 'Payment Terms', values: '', type: 'input', inputType: 'text', name: 'paymentTerm' }
  ];

  const selectOptions = options
    .filter((option) => option.type === 'select')
    .reduce((acc, curr) => {
      acc[curr.name] = offer[curr.name] || '';
      return acc;
    }, {});

  return (
    <Stack>
      <Typography variant="h4" color="secondary">
        Offer Details
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={4}>
          {options?.map((option, index) => (
            <Grid container spacing={1} alignItems="center" key={index} ml={1}>
              <Grid item xs={3}>
                <Typography variant="body1">{option.label}:</Typography>
              </Grid>
              <Grid item xs={8}>
                {renderFormControl(option, onChange, offer[option?.name])}
              </Grid>
            </Grid>
          ))}
          {/* <Grid item xs={8} display="flex" justifyContent="flex-end">
            <Button variant="contained" onClick={calculateOffer} disabled={loading}>
              Calculate
            </Button>
          </Grid> */}
        </Grid>
        <Grid item xs={6} display="flex">
          <Typography variant="h5" mr={2}>
            Notes
          </Typography>
          <TextAreaComponent
            id="note"
            style={{ width: '100%' }}
            name="notes"
            value={offer?.notes || ''}
            multiline
            minRows={8}
            variant="outlined"
            fullWidth
            onChange={(name, e) => {
              handleNotesChange(name, e.target.value);
            }}
          />
        </Grid>
      </Grid>
    </Stack>
  );
};

export default OfferDetails;
