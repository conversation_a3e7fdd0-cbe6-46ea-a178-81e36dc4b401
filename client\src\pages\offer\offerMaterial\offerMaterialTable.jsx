import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Box, Button, Card, CardContent, Checkbox, Divider, ListItemButton, Stack, Typography } from '@mui/material';
import TableComponent from 'pages/component/table/table';
import ActionButton from 'pages/component/actionButton';
import OfferMaterialForm from './offerMaterialForm';
import { get, uniqueId } from 'lodash';
import { useDispatch, useSelector } from 'react-redux';
import {
  addSupplierAction,
  calculateQuoteAction,
  deleteQuoteAction,
  getOfferDetail,
  setUseQuoteList,
  useQuoteAction
} from 'redux/reducers/offerReducer';
import { CheckBox } from '@mui/icons-material';
import CheckBoxInput from 'pages/component/table/checkboxInput';
import { ALERT_SUCCESS } from 'redux/reducers/alertReducer';
import { addMessage, deleteMessage, editMessage } from 'utils/validationMessage';
import { deleletRequest, post } from 'utils/axios';
import { calculateQuoteApiUrl, deleteQuoteApiUrl } from 'utils/constant';
import { getApprovedSupplier, getSupplierName, showAlert } from 'utils/helper';
import Loader from 'components/Loader';
import { useParams } from 'react-router';
import RequestQuote from './requestQuote';
import { getColumnAction, offerMaterialTableColumn } from './constant';
import AlertDialog from 'pages/component/dialogbox';
import ConvertHtml from 'pages/singlrRfq/convertHtmlContent';
import RequestKamPrice from './requestedKamPrice';
import Benchmark from 'pages/offer/offerMaterial/benchMark';
import SelectComponent from 'pages/component/selectComponent';
import DynamicAutocomplete from 'pages/component/autoComplete';
import CalculateMargin from './calculateMargin/calculateMargin';
const initialValues = {
  date: '',
  supplier: '',
  unitPrice: 0,
  quantity: 0,
  weight: 0,
  currency: '',
  leadTime: '',
  notes: '',
  isTax: true
};

const OfferMaterial = ({ offer, sendPriceRequestOpenModal, selectSupplier, supplierList, handleExcludeChange, loader }) => {
  const [headers, setHeaders] = useState([]);
  const offerDetail = useSelector((state) => state.offer);
  const [open, setOpen] = useState(false);
  const [marginDetail, setMarginDetail] = useState({});
  const [notesDetail, setNotesDetail] = useState('');
  const formikRefs = useRef({});
  const initialMaterialState = {
    ...initialValues,
    isEditing: false,
    editingMaterialId: null,
    selectedSupplier: initialValues,
    supplierIndex: null
  };

  const rfqId = useParams()?.id;
  const dispatch = useDispatch();
  const [materials, setMaterials] = useState([]);

  useEffect(() => {
    const initialMaterials = get(offer, 'materials', [])?.map((material, index) => ({
      ...initialMaterialState,
      ...material
    }));
    setMaterials(initialMaterials);
  }, [offer?.materials]);

  useEffect(() => {
    setHeaders([
      ...offerMaterialTableColumn?.filter((column) => !column?.hide),
      ...getColumnAction(loader, getSingleSupplier, deleteSupplier, selectSupplier, showNotes, setMarginDetail)
    ]);
  }, [materials, loader]);

  const addSupplier = async (newSupplier, materialId, resetForm) => {
    const { supplier, currency, leadTime, unitPrice, quantity, isTax, weight, notes } = newSupplier || {};
    let initialPayload = {
      materialId: materialId?.toString(),
      rfqId: rfqId,
      isEdit: false,
      leadTime: leadTime,
      supplierId: supplier?.toString(),
      unitPrice: Number(unitPrice),
      currency: currency?.toString(),
      weight: Number(weight),
      quantity: Number(quantity),
      isTax: isTax,
      isOffered: false,
      ClientID: offer?.ClientID || '',
      notes
    };
    const response = await dispatch(calculateQuoteAction(initialPayload));

    const { success, error, data } = get(response, 'payload', {});
    if (success) {
      const useQuoteList = get(offerDetail, 'useQuoteList', []);
      const updatedQuoteList = useQuoteList?.map((material) => {
        // Check if materialId matches and update the quotes array accordingly
        if (material.materialId === materialId) {
          // Replace with the actual materialId you're targeting
          return {
            ...material,
            quotes: material.quotes.filter((quote) => quote.id !== newSupplier.id)
          };
        }
        return material;
      });
      dispatch(setUseQuoteList(updatedQuoteList));
      resetForm();
    }
  };

  const showNotes = (data) => {
    setNotesDetail(data?.notes);
    setOpen(true);
  };

  const deleteSupplier = (materialId, supplierId, type, data) => {
    let materialIndex = materials?.findIndex((material) => material?.materialId === materialId);
    if (materialIndex !== -1) {
      let updatedMaterials = [...materials];
      let updatedMaterial = { ...updatedMaterials[materialIndex] };
      const supplierToDelete = updatedMaterial?.suppliers?.find((supplier) => supplier?.id === supplierId);

      if (type === 'delete') {
        let payload = {
          quoteId: supplierToDelete?.id,
          rfqId: rfqId
        };
        dispatch(deleteQuoteAction(payload));
      } else {
        let showSuppliers = updatedMaterial?.showSuppliers;
        if (showSuppliers) {
          showSuppliers = JSON.parse(showSuppliers);
          const updatedSuppliers = showSuppliers?.filter((item) => item !== data?.supplierId);

          const payload = {
            Material_ID: Number(updatedMaterial?.Material_ID),
            RFQ_ID: Number(rfqId),
            showSuppliers: updatedSuppliers
          };
          dispatch(useQuoteAction(payload));
        }
      }
    }
  };

  const getSingleSupplier = (materialId, quoteId, type) => {
    const updatedMaterials = materials?.map((material) => {
      if (material.materialId === materialId) {
        return {
          ...material,
          isEditing: type === 'edit',
          editingMaterialId: materialId,
          selectedSupplier: material?.suppliers?.find((supplier) => supplier?.id === quoteId),
          supplierIndex: material?.suppliers?.findIndex((supplier) => supplier?.id === quoteId)
        };
      } else {
        return material;
      }
    });
    setMaterials(updatedMaterials);
    // dispatch(addSupplierAction({ ...offer, materials: updatedMaterials }));
  };

  const editSupplier = async (newSupplier, materialId, quoteId, resetForm) => {
    const { supplier, offer: isOffered, currency, leadTime, notes, unitPrice, quantity, isTax, weight, id } = newSupplier || {};
    let initialPayload = {
      materialId: materialId?.toString(),
      rfqId: rfqId,
      quoteId: id?.toString(),
      isEdit: true,
      leadTime: leadTime,
      supplierId: supplier?.toString(),
      unitPrice: Number(unitPrice),
      currency: currency?.toString(),
      weight: Number(weight),
      quantity: Number(quantity),
      isTax: isTax,
      isOffered: isOffered,
      notes,
      ClientID: offer?.ClientID
    };

    const response = await dispatch(calculateQuoteAction(initialPayload));
    const { success, error, data } = get(response, 'payload', {});
    if (success) {
      resetForm();
    }
  };
  const handleClose = () => {
    setOpen(false);
    setNotesDetail('');
  };
  const [supplier, setSupplier] = useState('');
  const [currency, setCurrency] = useState('');
  const currencySelectOptions = useMemo(
    () => [
      { label: 'USD', value: 'USD' },
      { label: 'EUR', value: 'EUR' },
      { label: 'GBP', value: 'GBP' },
      { label: 'CLP', value: 'CLP' },
      { label: 'AUD', value: 'AUD' }
    ],
    []
  );
  const resetApplyValues = () => {
    setCurrency(() => '');
    setSupplier(() => '');
  };
  const handleApplyValues = () => {
    Object.values(formikRefs.current).forEach((ref) => {
      if (ref) {
        if (supplier) {
          ref.setFieldValue('supplier', supplier);
        }
        if (currency) {
          ref.setFieldValue('currency', currency);
        }
      }
    });

    resetApplyValues();
  };
  const handleCopyQuantities = () => {
    Object.values(formikRefs.current).forEach((ref) => {
      if (ref && ref?.quantity) {
        ref.setFieldValue('quantity', ref?.quantity);
      }
    });

    resetApplyValues();
  };

  return (
    <div>
      <AlertDialog
        title="Select Reason to Release RFQ"
        content=""
        onAgree={() => {}}
        cancel={() => handleClose()}
        Component={
          <>
            <div style={{ minWidth: '400px' }}>
              <Typography variant="h5" color="secondary">
                Notes
              </Typography>
              <ConvertHtml content={notesDetail || ''} />
            </div>
          </>
        }
        open={open}
        showCancelBtn={true}
        showCard={false}
        borderRadius="20px"
      />

      <Stack alignItems="flex-end" my={2} direction="row" gap={2}>
        <div className="generateOffer-input" style={{ marginRight: '10px' }}>
          <Typography variant="h6">Supplier</Typography>
          <>
            <DynamicAutocomplete
              options={getApprovedSupplier(supplierList)}
              value={supplier ? getApprovedSupplier(supplierList).find((item) => item.value === supplier) || null : null}
              onChange={(_, newValue) => setSupplier(newValue ? newValue.value : '')}
              getOptionLabel={(option) => option?.label || ''}
              placeholder="Select supplier"
              label=""
              isNotRemoveButton={false}
            />
          </>
        </div>

        <div className="generateOffer-input" style={{ marginRight: '10px' }}>
          <Typography variant="h6">Currency</Typography>
          <>
            <DynamicAutocomplete
              options={currencySelectOptions}
              value={currency ? currencySelectOptions.find((item) => item.value === currency) || null : null}
              onChange={(_, newValue) => setCurrency(newValue ? newValue.value : '')}
              getOptionLabel={(option) => option?.label || ''}
              placeholder="Select currency"
              label=""
              isNotRemoveButton={false}
            />
          </>
        </div>

        <Button disabled={!supplier && !currency} onClick={handleApplyValues} variant="contained">
          Apply
        </Button>
        <Button onClick={handleCopyQuantities} variant="outlined">
          Copy Quantities
        </Button>
      </Stack>

      <Box display={'flex'} justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h4" color="secondary">
          Materials
        </Typography>

        <Button variant="contained" onClick={() => sendPriceRequestOpenModal()} disabled={loader}>
          Send Email Request
        </Button>
      </Box>

      {materials && materials?.length > 0 ? (
        materials?.map((material, index) => (
          <Card key={index} className="card-container material-table-card">
            <CardContent className="card-content">
              <div className="request-quote-wrapper">
                <div className="material-description-wrapper">
                  <Typography variant="h5" color="secondary" className="material-description">
                    {get(material, 'Material_Description', '')}
                  </Typography>
                </div>
                <Benchmark material={material} disableBtn={loader} />

                {material?.supplierPricesFound && (
                  <RequestQuote material={material} materials={materials} setMaterials={setMaterials} rfqId={rfqId} loading={loader} />
                )}

                <RequestKamPrice material={material} materials={materials} setMaterials={setMaterials} rfqId={rfqId} loading={loader} />
              </div>

              <Box sx={{ display: 'flex' }}>
                <Typography variant="body1" mt={2} mb={1}>
                  Part Number : {get(material, 'Part_Number', '')}
                </Typography>
                <Typography variant="body1" mt={2} mb={1} ml={4}>
                  QTY : {get(material, 'Quantity_Required', '')}
                </Typography>
              </Box>

              <div className="generateOffer-input exclude-input" style={{ marginRight: '10px' }}>
                <Typography variant="body1" mt={1} mr={1}>
                  Exclude
                </Typography>

                <CheckBoxInput
                  checked={material?.exclude || false}
                  value={material?.exclude || false}
                  onChange={() => handleExcludeChange(material.materialId)}
                />
              </div>

              <OfferMaterialForm
                quantity={material?.Quantity_Required}
                loading={loader}
                supplierList={supplierList}
                suppliers={material?.suppliers}
                addSupplier={(data, resetForm) => addSupplier(data, material?.materialId, resetForm)}
                editSupplier={(data, resetForm) => editSupplier(data, material?.materialId, data?.id, resetForm)}
                editMode={material?.isEditing}
                ref={(el) => {
                  if (el) formikRefs.current[index] = el;
                }}
                initialValues={
                  { ...material?.selectedSupplier, weight: material?.selectedSupplier?.weight || material?.prepopulatedWeight || 0 } || {
                    ...initialValues,
                    weight: material?.prepopulatedWeight || 0
                  }
                }
              />

              <TableComponent
                columns={headers}
                defaultPage="5"
                rows={get(material, 'suppliers', [])?.map((supplier) => ({
                  ...supplier,
                  supplierName: getSupplierName(supplier, supplierList),
                  parentId: material?.materialId,
                  ClientID: offer?.ClientID
                }))}
                title="OfferMaterial"
                titleLink="dashboard"
                showDeleteButton
                enablePagination={true}
              />
            </CardContent>
          </Card>
        ))
      ) : (
        <p>No Materials Found</p>
      )}
    </div>
  );
};

export default OfferMaterial;
