const router = require("express").Router();
const {
  getClients,
  addClient,
  getSingleClient,
  editClient,
  deleteClient,
  updateClientStatus,
  getCountries,
  getStateList,
  importCountry,
  importState,
  getRegion
} = require("./client.controller");

const {
  getClientSchema,
  addClientSchema,
  getSingleClientSchema,
  editClientSchema,
  deleteClientSchema,
  updateClientStatusSchema,
  getStateListSchema
} = require("./client.validation");

const { validate } = require("../middlewares/validation.middleware");

router.get("/get", validate(getClientSchema, "body"), getClients);

router.post("/add", validate(addClientSchema, "body"), addClient);

router.post(
  "/get/:ClientID",
  validate(getSingleClientSchema, "params"),
  getSingleClient
);

router.post("/edit", validate(editClientSchema, "body"), editClient);

router.post(
  "/delete/:ClientID",
  validate(deleteClientSchema, "params"),
  deleteClient
);

router.post(
  "/update-status",
  validate(updateClientStatusSchema, "body"),
  updateClientStatus
);

router.get("/countries",  getCountries);
router.get("/states/:CountryId", validate(getStateListSchema, "params"),  getStateList);
router.get("/improt-country",  importCountry);
router.get("/improt-state",  importState);
router.get("/regions",  getRegion);

module.exports = router;
