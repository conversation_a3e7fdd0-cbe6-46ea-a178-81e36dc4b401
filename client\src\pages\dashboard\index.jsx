import { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import DashboardUI from './dynamicDashboard';
import { useDispatch, useSelector } from 'react-redux';
import { capitalize, get } from 'lodash';
import { addPageTitle } from 'redux/reducers/pageTitleReducer';

const DynamicDashboards = () => {
  const { id } = useParams();
  const setting = useSelector((state) => state.setting);
  const [dashboard, setDashboard] = useState({});
  const dispatch = useDispatch();

  useEffect(() => {
    if (id) {
      let selectedDashboard = get(setting, 'data.dashboards', [])?.filter((dashboard) => dashboard?.ID == id);
      if (selectedDashboard && selectedDashboard?.length > 0) {
        dispatch(addPageTitle(selectedDashboard[0]?.Name));
        setDashboard({ ...selectedDashboard[0] });
      }
    }
  }, [setting?.data, id]);

  return (
    <>
      <DashboardUI src={dashboard?.Link} />
    </>
  );
};
export default DynamicDashboards;
