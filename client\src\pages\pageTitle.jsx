import React, { useEffect } from 'react';
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import PropTypes from 'prop-types';
import { Grid, IconButton, Typography } from '@mui/material';
import { Link } from 'react-router-dom';

const PageTitle = ({ title, backLink }) => {
  return (<>
    <Grid container  >
      <Grid item lg={6} md={6} sm={6} xs={12} p={1}>
        <Typography variant="h4" color='secondary'>
          {title}
        </Typography>
      </Grid>
    </Grid>
  </>)
};

PageTitle.propTypes = {
  title: PropTypes.string.isRequired,
};

export default PageTitle;
