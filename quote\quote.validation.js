const Joi = require("joi");
const {
  numberValidation,
  booleanValidation,
  stringValidation,
  currencyValidation,
} = require("../utils/validator.util");

const getQuoteSchema = Joi.object({
  RFQ_ID: stringValidation,
});

const updateQuoteOfferStatusSchema = Joi.object({
  quoteId: stringValidation,
  isOffered: booleanValidation,
});

const calculateQuoteSchema = Joi.object({
  quoteId: Joi.when("isEdit", { is: true, then: stringValidation }),
  rfqId: stringValidation,
  materialId: stringValidation,
  supplierId: stringValidation,
  unitPrice: numberValidation,
  quantity: numberValidation,
  weight: numberValidation,
  currency: currencyValidation,
  isOffered: booleanValidation,
  isTax: booleanValidation,
  leadTime: numberValidation,
  isEdit: booleanValidation,
  notes: stringValidation.optional().allow(""),
  ClientID:stringValidation
});

const deleteQuoteSchema = Joi.object({
  id: stringValidation,
});

const calculateMarginSchema = Joi.object({
  offerCurrency: currencyValidation,
  desiredUnitPrice: numberValidation,
  quoteCurrency: currencyValidation,
  unitPrice: numberValidation,
  supplierId: numberValidation,
  weight: numberValidation,
  isTax: booleanValidation,
  ClientID:stringValidation
});

module.exports = {
  getQuoteSchema,
  updateQuoteOfferStatusSchema,
  calculateQuoteSchema,
  deleteQuoteSchema,
  calculateMarginSchema,
};
