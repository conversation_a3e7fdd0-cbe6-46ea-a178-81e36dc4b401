.logo {
  height: 30px;
  margin: 8px 0;
}
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

/* Style the scrollbar thumb */
::-webkit-scrollbar-thumb {
  background-color: #888;
}
.error-message {
  color: #ff4d4f;
}

.material-value {
  color: rgb(0, 0, 0);
  font-weight: 500;
}
.material-value-void {
  color: rgb(240, 12, 12);
  font-weight: 500;
}

.link {
  text-decoration: none;
  color: #1677ff !important;
}

.link:hover {
  text-decoration: none;
  /* Removes underline on hover */
  color: #1677ff !important;
}

.over-view-label {
  word-wrap: break-word;
  font-weight: 500;
  min-width: 75px;
  display: inline-block;
}

.over-view-value {
  word-wrap: break-word;
  font-weight: 500;
}
.over-view-label-status {
  word-wrap: break-word;
  font-weight: 500;
  min-width: 110px;
  display: inline-block;
}

.over-view-box {
  max-height: 200px !important;
}

.over-view-inner-container {
  margin-bottom: 4px;
  max-width: 400px;
  display: flex;
}

.card-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

@media only screen and (min-width: 1280px) {
  .rfq-container {
    max-width: 100%;
    margin: 0 auto;
  }
}

@media only screen and (min-width: 1366px) {
  .rfq-container {
    max-width: 1200px;
    max-width: 1200px;
    margin: 0 auto;
  }
}

@media only screen and (min-width: 1440px) {
  .rfq-container {
    max-width: 1366px;
    min-width: 1366px;
    margin: 0 auto;
  }
}

@media only screen and (min-width: 1600px) {
  .rfq-container {
    max-width: 1400px;
    min-width: 1400px;
    margin: 0 auto;
  }
}

.assign-wrapper {
  display: flex;
  justify-content: space-between;
}

.over-view-container {
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
}

.error-border {
  border: 1px solid #ff4d4f;
}

.error-border:hover {
  border: 1px solid #ff4d4f;
}

.custom-title-class {
  font-size: 16px;
}

.MuiButtonBase-root {
  cursor: pointer;
}

.textarea::placeholder {
  color: rgb(175, 172, 172);
}

.MuiOutlinedInput-input::placeholder {
  color: rgb(0, 0, 0);
}
.css-z3c6am-MuiFormControl-root-MuiTextField-root {
  width: 100% !important;
}

.search-container {
  width: 20%;
  margin-bottom: 10px;
}

.searchIcon {
  position: absolute;
  left: 0;
  top: 10px;
  margin-left: 10px;
}

.textFieldWrapper {
  padding: 5px;
  padding-left: 40px;
  padding-right: 40px;
  color: white;

  border: 0ch;
}

.closeIcon {
  position: absolute;
  right: 0;
  top: 10px;
  margin-right: 10px;
  cursor: pointer;
}

.searchWrapper {
  position: relative;
  width: 250px;
  padding: 2px;
  border-radius: 25px;
  background-color: #f1f1f1;
}

.box {
  width: 250px;
  border-radius: 25px;
  margin-right: 10px;
  background-color: white;
}

.pdfListContainer {
  display: flex;
  min-width: 400px;
  justify-content: space-between;
  border: 1px solid #e0e0e0;
  padding: 10px;
  border-radius: 8px;
  align-items: center;
  margin: 10px;
}

.files-wrapper {
  display: flex;
  justify-content: space-between;
}

.file-list-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px;
  cursor: pointer;
}

.material-request-btn {
  width: 100px;
}

.cursor-pointer {
  cursor: pointer;
}

.small-btn {
  height: 23px;
}

.table-cardHeader-class {
  display: flex;
  justify-content: space-between;
  margin: 5px;
  align-items: center;
}

.maincard-border {
  margin-top: 1;
  width: 100%;
  border: 1px solid #e0e0e0;
}

.name-text-field {
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
  overflow: hidden;
  opacity: 1;
  outline: none;
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 0px 0px;
  border: 1px solid rgb(230, 230, 231);
  margin-right: 10px;
}

.sorting-container {
  display: flex;
  gap: 20px;
}

.nav-collapse {
  padding-left: 30px;
  padding-right: 20px;
  display: flex;
  justify-content: space-between;
  width: 100%;
  cursor: pointer;
}

.collapse-icon {
  font-size: 20px;
  margin-bottom: 10px;
}

.table-cells {
  text-transform: capitalize;
}

.header-wrapper {
  min-width: 350px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.status-badge {
  padding-left: 30px;
  margin-left: 30px;
}

.nav-collapse-icon {
  display: flex;
  gap: 10px;
}

.nav-item-label {
  margin-left: 7px;
  background-color: #f7fcff;
}

.email-request-container {
  padding: 16px;
  border-radius: 12px;
  background-color: #f9f9f9;
}

.email-request-title {
  margin-bottom: 16px;
}

.email-request-divider {
  margin-bottom: 24px;
}

.email-request-info-box {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
  background-color: #fff;
}

.email-request-component-box {
  width: 100%;
  margin-top: 20px;
}

.email-request-icon {
  margin-top: 2px;
}

.email-request-label {
  min-width: 100px;
  margin-bottom: 3px;
}

.email-request-text {
  font-weight: 500;
  word-break: break-word;
}

.email-request-component {
  width: 100%;
}

.email-request-section {
  padding: 5px;
}

.email-request-rfq-title {
  font-weight: 700;
  color: #1976d2;
}

.email-request-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  gap: 16px;
  position: sticky;
  bottom: 0;
  background: #fff;
  padding: 12px;
  box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  z-index: 3;
}

.email-request-icon-button {
  text-align: center;
  margin-bottom: 6px;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.15);
}

.email-request-icon-button .icon {
  margin-left: 4px;
  margin-top: 2px;
  font-size: 15px;
}

.email-request-header {
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
}

.multiselect-chip-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-left: 5px;
  margin-top: 5px;
}

.multiselect-chip {
  max-width: 100%;
  border-radius: 25px;
  background-color: #dde5ee;
}

.multiselect-chip .MuiChip-deleteIcon {
  border-radius: 50%;
  color: #1677ff;
}

.multiselect-chip-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5;
  max-height: 100px;
  overflow: auto;
}

.email-response-container {
  margin: 0 auto;
}

.email-response-title {
  font-weight: 700;
  color: black;
}

.email-response-card {
  border-radius: 12px;
  transition: 0.3s;
  background-color: #f9f9f9;
  padding: 16px;
}

.email-response-card:hover {
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
  border-color: #1976d2;
}

.email-avatar {
  background-color: #1976d2 !important;
  width: 40px !important;
  height: 40px !important;
  color: white !important;
}

.email-name {
  font-weight: 600;
  color: #1976d2;
}

.email-address {
  color: gray;
}

.email-response-icon {
  color: gray !important;
}

.email-subject {
  font-weight: 600;
}

.email-time {
  color: gray;
}

.email-divider {
  margin: 16px 0;
}

.email-reply-title {
  font-weight: 500;
  color: black;
  margin-bottom: 8px;
}

.email-reply-text {
  color: #737373;
  line-height: 1.6;
  white-space: pre-line;
}

.email-no-response {
  text-align: center;
  color: gray;
}
