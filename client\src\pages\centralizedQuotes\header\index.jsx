import React from "react";
import { <PERSON>po<PERSON>, But<PERSON> } from "@mui/material";
import RequestedPriceHeader from "./requestedPriceHeader";
import PriceTableHeader from "./priceTableHeader";
import SupplierRequestPriceHeader from "./supplierRequestPriceHeader";
import './header.css'
import { FORMREQUEST, PRICETABLE, REQUESTEDPRICE } from "../constant";

const Header = ({ formData, setFormData, supplierList, loading, }) => {

  const renderHeaderContent = () => {
    switch (formData?.changeComponent) {
      case REQUESTEDPRICE:
        return (
          <RequestedPriceHeader setFormData={setFormData} formData={formData} supplierList={supplierList} loading={loading} />
        );
      case PRICETABLE:
        return (
          <PriceTableHeader setFormData={setFormData} formData={formData} loading={loading} />
        );
      case FORMREQUEST:
        return (
          <SupplierRequestPriceHeader setFormData={setFormData} formData={formData} supplierList={supplierList} loading={loading} />
        );
      default:
        return null;
    }
  };

  return <>{renderHeaderContent()}</>;
};

export default Header;
