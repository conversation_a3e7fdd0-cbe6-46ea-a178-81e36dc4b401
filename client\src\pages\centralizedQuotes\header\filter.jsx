import { Button, Popover, Box } from "@mui/material";
import { useEffect, useState } from "react";
import { ARRAY_TYPE, defaultFilterPayload, filterData } from "../constant";
import MaterialFilter from "pages/availableRfq/materialFilter.js";
import { getAllCentralizedRequestAction } from "redux/reducers/centralizedReducer";
import { useDispatch, useSelector } from "react-redux";
import { ARRAY, DATE, TEXT } from "pages/availableRfq/materialFilter.js/component/constant";
import { get } from "lodash";
import { getAllKamAction } from "redux/reducers/RfqReducer";
import { getAllUserAction } from "redux/reducers/userReducer";
export const createCentralizedFilterPayload = (data) => {
  if (!Array.isArray(data)) return [];
  const filtered = data
    .filter((item) => {
      if (!item || !item.data) return false;

      switch (item?.type) {
        case ARRAY:
          return Array.isArray(item.data) && item.data.length > 0;
        case TEXT:
        case DATE:
          return item.data.trim?.() !== '';
        default:
          return true;
      }
    })
    .map((filteredData) => {
      let data;

      switch (filteredData?.type) {
        case ARRAY_TYPE:
          data = filteredData.data || [];
          break;
        default:
          data = filteredData?.data?.toString();
          break;
      }

      return {
        heading: filteredData?.field,
        data: data,
      };
    });

  return { ...defaultFilterPayload, filterData: filtered }
};
const FilterComponent = ({ loading, setFormData, formData }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const usersListData = useSelector((state) => state.users);
  const [kamFilterOptions, setKamFilterOptions] = useState(filterData);
  const [maxHeight, setMaxHeight] = useState('auto');
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getAllUserAction());
  }, []);

  useEffect(() => {
    const kamList = [{
      heading: 'KAM',
      type: 'select',
      field: 'KAM',
      data: '',
      options: get(usersListData, 'data', [])?.map((item) => ({
        label: `${item.FirstName} ${item.LastName}`,
        value: item.UserID?.toString()
      })) || []
    }];

    setKamFilterOptions([...filterData, ...kamList]);
    if (!formData?.filters?.filterValues || formData?.filters?.filterValues?.length === 0)
      setFormData({ ...formData, filters: { ...formData?.filters, filterValues: [...filterData, ...kamList] } })
  }, [usersListData?.data]);

  const onFilterChange = (event) => {
    setAnchorEl(event.currentTarget);

    // Calculate available screen height dynamically
    const screenHeight = window.innerHeight;
    const popoverMaxHeight = Math.min(screenHeight * 0.8, 500);

    setMaxHeight(`${popoverMaxHeight}px`);
  };

  const closePopover = () => {
    setAnchorEl(null);
  };

  const applyFilters = (data) => {
    let updatedFilters = data && data?.length > 0 ? data : formData?.filters?.filterValues?.map((value) => {
      if (!value?.notShowFilter) {
        return { ...value, data: value?.type === ARRAY ? [] : '' }
      }
      else {

        return value
      }
    })

    const payload = createCentralizedFilterPayload(updatedFilters);
    setFormData({ ...formData, filters: { ...formData?.filters, filterValues: updatedFilters || [], page: 1 } });

    // dispatch(getAllCentralizedRequestAction(payload));
  };

  const open = Boolean(anchorEl);
  const { filterValues } = formData?.filters || {};

  return (
    <div>
      <Button
        variant="contained"
        color={open ? 'error' : 'primary'}
        onClick={onFilterChange}
      >
        {!open ? 'Filters' : 'Cancel'}
      </Button>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={closePopover}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: {
            marginTop: 1,
            maxHeight: maxHeight,  // Dynamic height based on screen size
            overflowY: 'auto',
          },
        }}
      >
        <Box
          sx={{
            width: 350,
            p: 2,
            maxHeight: '100%',
            overflowY: 'auto',
          }}
        >
          <MaterialFilter
            loading={loading}
            filterMaterialData={filterValues?.length > 0 ? filterValues : kamFilterOptions}
            handleFilter={applyFilters}
            label="Filters"
          />
        </Box>
      </Popover>
    </div>
  );
};

export default FilterComponent;
