import React, { useEffect, useState } from 'react';
import { get } from 'lodash';
import PropTypes from 'prop-types';
import RadioButtonsGroup from 'pages/component/radioBtn';
import SelectComponent from 'pages/component/selectComponent';

const FilterSelect = ({ data, handleFilterChange }) => {
  const [filterData, setFilterData] = useState([]);

  useEffect(() => {
    let optionData = get(data, 'options', []);
    if (optionData && optionData?.length > 0) {
      setFilterData(optionData);
    }
  }, [data]);

  const createRadioOptions = (optionsData) => {
    return optionsData?.map((item) => ({
      label: item?.label,
      value: item?.value,
      select: item?.select
    }));
  };

  return (
    <>
      <div className="radio-buttons">
        <SelectComponent
          name="select"
          size="small"
          onSelectChange={(e) => {
            handleFilterChange(get(e, 'target.value'));
          }}
          value={get(data, 'data', '')}
          items={createRadioOptions(filterData)}
        />
      </div>
    </>
  );
};

FilterSelect.propTypes = {
  data: PropTypes.array.isRequired,
  handleFilterChange: PropTypes.func.isRequired
};

export default FilterSelect;
