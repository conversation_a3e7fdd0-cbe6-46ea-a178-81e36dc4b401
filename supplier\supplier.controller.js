const {
  mainDataset,
  landingZoneDataset,
  scrapedDataset,
  bigQueryClient,
  automationDataset,
  sourcingDataset,
} = require("../db");
const {
  DATASET_ID_MAIN,
  DATASET_ID_SCRAPE,
  JWT_SECRET,
} = require("../constants");
const { apiHandler, apiError, apiResponse } = require("../utils/api.util");
const {
  NOT_FOUND,
  FETCH,
  ADD_SUCCESS,
  SUCCESS,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  EXISTS,
  CUSTOM_ERROR,
  SENT_SUCCESS,
  INVALID,
  CUSTOM_SUCCESS,
  STATUS_SUCCESS,
} = require("../utils/message.util");
const {
  isEmpty,
  setDate,
  generateID,
  getCurrentDateTime,
  createBatches,
  isEmptyV2,
} = require("../utils/misc.util");
const { BASE_URL } = require("../constants");
const { sendMail } = require("../utils/email.util");
const { sendNotification } = require("../utils/notifications.util");
const jwt = require("jsonwebtoken");
const {
  newClientTemplate,
  newSupplierTemplate,
  updateSupplierTemplate,
  supplierMaterialRequestTemplate,
} = require("../utils/templates");

const getSupplier = apiHandler(async (req, res) => {
  let { page, limit, search, sort, filters } = req.body;

  limit = limit || Number.MAX_SAFE_INTEGER;
  let offset = ((page || 1) - 1) * (limit || Number.MAX_SAFE_INTEGER);

  let whereExpression = ``;
  let orderByExpression = ``;

  // whereExpression += `WHERE Status = 'APPROVED' OR Status IS NULL`;

  whereExpression += !isEmpty(search)
    ? `WHERE ContactLastname LIKE '%${search}%' OR Email LIKE '%${search}%' OR Name LIKE '%${search}%' OR ContactName LIKE '%${search}%' `
    : ``;

  orderByExpression += !isEmpty(sort)
    ? ` ORDER BY ${sort.field} ${sort.order} `
    : ` ORDER BY Name ASC `;

  if (!isEmpty(filters)) {
    filters.forEach((filter) => {
      if (filter.heading === "Email-Supplier") {
        console.log("here");
        let brandList = ""; // Initialize brandList outside the conditional block
        if (filter.brands && filter.brands.length > 0) {
          console.log("in brand here");
          // Convert brands array to a string with quoted values
          brandList = filter.brands.map((brand) => `'${brand}'`).join(", ");
        }
        console.log("above brandList", brandList);
        // Only add the `IN` clause if brandList is not empty
        if (brandList) {
          console.log("in brand list");
          let filterBrand = ` AND Brand IN (${brandList})`;
          whereExpression +=
            whereExpression === ""
              ? ` WHERE ${filter.field} = ${filter.data} ${filterBrand}`
              : ` AND ${filter.field} = ${filter.data} ${filterBrand}`;
        } else {
          // Handle the case where no brands are provided
          whereExpression +=
            whereExpression === ""
              ? ` WHERE ${filter.field} = ${filter.data}`
              : ` AND ${filter.field} = ${filter.data}`;
        }
        console.log("whereExpression", whereExpression);
      }
    });
  }

  let [existingSuppliers] = await mainDataset.query({
    query: `SELECT 
    s.SupplierID, 
    Notes, 
    ContactLastname, 
    Shipping, 
    Tipo, 
    Email, 
    Country, 
    Name, 
    Web, 
    ContactName, 
    Language,
    s.Created_At,
    s.Updated_At,
    Created_By, 
    Status, 
    Updated_By, 
    Reject_Reason, 
    token, 
    ARRAY_AGG(sb.Brand IGNORE NULLS) AS brands
FROM Suppliers AS s
LEFT JOIN Suppliers_Brand AS sb ON s.SupplierID = sb.SupplierID
    ${whereExpression}
GROUP BY 
    s.SupplierID, 
    Notes, 
    ContactLastname, 
    Shipping, 
    Tipo, 
    Email, 
    Country, 
    Name, 
    Web, 
    Language,
    ContactName, 
    s.Created_At,
    s.Updated_At,
    Created_By, 
    Status, 
    Updated_By, 
    Reject_Reason, 
    token
    `,
  });

  if (isEmpty(existingSuppliers)) {
    return apiError(NOT_FOUND, "Suppliers", null, res);
  }

  existingSuppliers = existingSuppliers.filter(
    (supplier) => !isEmpty(supplier.Name)
  );

  let existingSuppliersIds = [];
  let suppliers = [];

  for (const supplier of existingSuppliers) {
    if (!existingSuppliersIds.includes(supplier.SupplierID)) {
      suppliers.push(supplier);
      existingSuppliersIds.push(supplier.SupplierID);
    }
  }

  const count = suppliers.length;
  existingSuppliers = suppliers.splice(offset, limit);

  existingSuppliers.sort(
    (a, b) => new Date(b.Updated_At) - new Date(a.Updated_At)
  );

  const data = {
    count: count,
    suppliers: existingSuppliers,
  };

  return apiResponse(FETCH, "Suppliers", data, res);
});

const fetchSuppliers = apiHandler(async (req, res) => {
  const { brand, partNumber, RFQ_ID, Material_ID } = req.body;

  const [[selectedSuppliers]] = await scrapedDataset.query({
    query: `
      SELECT showSuppliers
      FROM Material
      WHERE Material_ID = @Material_ID
    `,
    params: {
      Material_ID: parseInt(Material_ID),
    },
  });

  let showSuppliers = [];
  if (!isEmpty(selectedSuppliers)) {
    showSuppliers = JSON.parse(selectedSuppliers.showSuppliers);
  }

  const query = `
  SELECT supplier.Is_Form_Supplier, sb.SupplierID as supplierID, supplier.name, sb.brand, pn.manufacturer_catalog_number as Part_Number, pn.manufacturer_name, sp.*, sr.materialRequested, sr.status as statusOfRequest, sp.currency as UnitCurrency, CASE WHEN TRUE THEN TRUE ELSE FALSE END AS existsInSupplierBrand
  FROM ${DATASET_ID_MAIN}.Suppliers_Brand as sb
  LEFT JOIN ${DATASET_ID_MAIN}.Suppliers as supplier ON supplier.SupplierID = sb.SupplierID 
  LEFT JOIN ${DATASET_ID_MAIN}.partnumbers as pn ON UPPER(TRIM(sb.Brand)) = UPPER(TRIM(pn.manufacturer_name))
  LEFT JOIN ${DATASET_ID_MAIN}.supplier_price as sp ON CAST(sp.supplierID as string) = CAST(sb.supplierID as string) AND TRIM(UPPER(sp.brand)) = TRIM(UPPER(sb.Brand)) AND TRIM(sp.partNumber) = TRIM(pn.manufacturer_catalog_number)
  LEFT JOIN ${DATASET_ID_MAIN}.Supplier_Requests as sr ON CAST(sr.supplierID as string) = CAST(sb.supplierID as string)
  WHERE TRIM(UPPER(sb.Brand)) = TRIM(UPPER(@brand)) 
  AND TRIM(pn.manufacturer_catalog_number) = TRIM(@partNumber) AND supplier.Is_Form_Supplier IS NOT NULL AND supplier.Is_Form_Supplier = TRUE
  `;

  const options = {
    query: query,
    params: {
      brand: brand,
      partNumber: partNumber,
    },
  };

  let [quoteSuppliers] = await bigQueryClient.query(options);

  let [bulkSuppliers] = await mainDataset.query({
    query: `SELECT CAST(bsp.supplierID as INT) as supplierID, supplier.name, Price as unitPrice, PartNumber as Part_Number, bsp.Date as date, bsp.Currency as UnitCurrency, CASE WHEN TRUE THEN FALSE ELSE TRUE END AS existsInSupplierBrand 
    FROM bulk_supplier_price as bsp
    LEFT JOIN Suppliers as supplier ON CAST(supplier.SupplierID as STRING) = CAST(bsp.supplierID as STRING)
    WHERE bsp.PartNumber = @partNumber`,
    params: {
      partNumber: partNumber,
    },
  });

  if (!isEmpty(bulkSuppliers)) {
    for (const bulkSupplier of bulkSuppliers) {
      for (const quoteSupplier of quoteSuppliers) {
        if (
          bulkSupplier.supplierID === quoteSupplier.supplierID &&
          bulkSupplier.Part_Number === quoteSupplier.Part_Number
        ) {
          const bulkSupplierDate = new Date(bulkSupplier?.date?.value);
          const quoteSupplierDate = new Date(quoteSupplier?.date?.value);

          if (
            bulkSupplierDate > quoteSupplierDate ||
            isEmpty(quoteSupplierDate)
          ) {
            quoteSupplier.unitPrice = bulkSupplier.unitPrice;
            quoteSupplier.date = bulkSupplier.date;
            quoteSupplier.UnitCurrency = bulkSupplier.UnitCurrency;
          }
          bulkSuppliers = bulkSuppliers.filter(
            (supplier) => supplier.supplierID !== bulkSupplier.supplierID
          );
        }
      }
    }
  }

  let allSuppliers = [];
  if (!isEmpty(bulkSuppliers)) {
    allSuppliers = quoteSuppliers.concat(bulkSuppliers);
  } else {
    allSuppliers = quoteSuppliers;
  }

  for (const supplier of allSuppliers) {
    if (!isEmpty(supplier.Price)) {
      supplier.unitPrice = supplier.Price;
      supplier.date = supplier.bulk_date;
    }
    if (!supplier.unitPrice) {
      supplier.status = "NOPRICE";
    } else {
      supplier.status = "RECIEVED";
    }
  }
  for (const supplier of allSuppliers) {
    if (!isEmpty(supplier.materialRequested)) {
      const materialRequested = JSON.parse(supplier.materialRequested);
      supplier.materialRequested = JSON.parse(supplier.materialRequested);
      for (const existingMaterial of materialRequested) {
        if (
          // existingMaterial.rfqID === RFQ_ID.toString() &&
          // existingMaterial.materialID === Material_ID.toString() &&
          existingMaterial.partNumber === partNumber.toString() &&
          existingMaterial.status !== "RESPONDED"
          // && supplier.statusOfRequest !== "RESPONDED"
        ) {
          supplier.status = "REQUESTED";
        }
      }
    }
  }

  if (isEmpty(allSuppliers)) {
    return apiError(NOT_FOUND, "Suppliers", null, res);
  }

  for (const supplier of allSuppliers) {
    if (showSuppliers?.includes(supplier.supplierID)) {
      supplier.use = true;
    }
  }

  allSuppliers = allSuppliers.sort((a, b) => {
    return a.supplierID - b.supplierID;
  });

  const supplierIDs = [];
  const filteredSuppliers = [];

  for (const supplier of allSuppliers) {
    if (!supplierIDs.includes(supplier.supplierID)) {
      filteredSuppliers.push(supplier);
      supplierIDs.push(supplier.supplierID);
    }
  }

  allSuppliers = filteredSuppliers;

  // allSuppliers = allSuppliers.filter((supplier) => !isEmpty(supplier.name));

  return apiResponse(FETCH, "Suppliers", allSuppliers, res);
});

const addSupplierPrice = apiHandler(async (req, res) => {
  const { data, RequestID, supplierID, isIndividual } = req.body;
  const UserID = data[0].Created_By;
  let allMaterialsResponded = true;
  for (let i = 0; i < data.length; i++) {
    const request = data[i];

    if (isEmpty(isIndividual)) {
      if (!isEmpty(request.currency)) {
        request.status = "RESPONDED";
      }
    } else {
      if (!isEmpty(request.isIndividualPrice)) {
        request.status = "RESPONDED";
      }
      allMaterialsResponded = false;
      delete request.isIndividualPrice;
    }

    if (isEmpty(request.currency)) {
      allMaterialsResponded = false;
    } else {
      // Check if this is the last material being responded to
      const allOthersResponded = data.every(
        (material, index) => index === i || material.status === "RESPONDED"
      );
      if (allOthersResponded) {
        allMaterialsResponded = true;
      }
    }
  }

  const [saveSupplierRequests] = await mainDataset.query({
    query: `
      UPDATE Supplier_Requests SET
      materialRequested = @materialRequested
      WHERE ID = @RequestID
    `,
    params: {
      RequestID: parseInt(RequestID),
      materialRequested: JSON.stringify(data),
    },
  });

  for (const supplier of data) {
    const [existingSupplier] = await mainDataset.query({
      query: `
        SELECT *
        FROM supplier_price
        WHERE supplierID = @supplierID AND partNumber=@partNumber AND UPPER(brand)=UPPER(@brand)`,
      params: {
        supplierID,
        partNumber: supplier.partNumber,
        brand: supplier.brand.toUpperCase(),
      },
    });
    if (
      supplier.isAutomatedRFQ === true &&
      !isEmpty(supplier.productCondition)
    ) {
      const [existingScrapedMaterial] = await automationDataset.query({
        query: `
        SELECT Part_Number FROM to_scraping
        WHERE Part_Number = @Part_Number
        `,
        params: {
          Part_Number: supplier.partNumber.trim(),
        },
      });
      if (isEmpty(existingScrapedMaterial)) {
        const [toScrapping] = await automationDataset.query({
          query: `
          INSERT INTO to_scraping
          (Material_ID, Part_Number, Brand)
          VALUES
          (@Material_ID, @Part_Number, @Brand)
          `,
          params: {
            Material_ID: supplier.materialID,
            Part_Number: supplier.partNumber.trim(),
            Brand: supplier.brand.trim().toUpperCase(),
          },
        });
      }
    }
    if (isEmpty(existingSupplier)) {
      const id = generateID();
      const [newSupplier] = await mainDataset.query({
        query: `
          INSERT INTO supplier_price
          (partNumber, brand, description, quantity, unitPrice, totalPrice, notes, date, supplierID, productCondition, ID, requestID, currency,leadTime)
          Values
          (@partNumber, @brand, @description, @quantity, @unitPrice, @totalPrice, @notes, @date, @supplierID, @productCondition, @id, @RequestID, @currency,@leadTime)

        `,
        params: {
          partNumber: supplier.partNumber,
          brand: supplier.brand.toUpperCase(),
          description: supplier.description,
          quantity: parseInt(supplier.quantity),
          unitPrice: parseFloat(supplier.unitPrice),
          totalPrice: parseFloat(supplier.totalPrice),
          notes: supplier.notes || "",
          date: setDate(),
          supplierID,
          productCondition: supplier.productCondition,
          id: id.toString(),
          RequestID,
          currency: supplier.currency,
          leadTime: supplier.leadTime,
        },
      });
    } else {
      const [oldSupplier] = await mainDataset.query({
        query: `
          UPDATE supplier_price SET
          description=@description, quantity=@quantity, unitPrice=@unitPrice, totalPrice=@totalPrice,leadTime=@leadTime, notes=@notes, date=@date, productCondition=@productCondition, requestID = @RequestID, currency = @currency
          WHERE supplierID = @supplierID AND partNumber=@partNumber AND UPPER(brand)=UPPER(@brand)
        `,
        params: {
          partNumber: supplier.partNumber,
          brand: supplier.brand.toUpperCase(),
          description: supplier.description,
          quantity: parseInt(supplier.quantity),
          unitPrice: parseFloat(supplier.unitPrice),
          totalPrice: parseFloat(supplier.totalPrice),
          notes: supplier.notes || "",
          date: setDate(),
          supplierID,
          productCondition: supplier.productCondition,
          RequestID,
          currency: supplier.currency,
          leadTime: supplier.leadTime,
        },
      });
    }

    let message = `The Prices for Material - ${supplier.description} have been received from a supplier`;

    if (!isEmpty(supplier.currency)) {
      sendNotification({
        userID: UserID,
        message: message,
        RFQID: supplier.RFQ_ID,
      });
    }
  }

  if (allMaterialsResponded) {
    const [supplierRequests] = await mainDataset.query({
      query: `
      UPDATE Supplier_Requests SET
      status = @status, respondDate=@date
      WHERE ID = @RequestID
    `,
      params: {
        RequestID: parseInt(RequestID),
        status: "RESPONDED",
        date: setDate(),
      },
    });
  }

  return apiResponse(ADD_SUCCESS, "Supplier Price", null, res);
});

const addSupplierPriceV2 = apiHandler(async (req, res) => {
  const { data, RequestID, supplierID, isIndividual } = req.body;
  const UserID = data[0].Created_By;
  let allMaterialsResponded = true;

  // Filter the data for relevant records
  let filteredData = data.filter(
    (supplier) =>
      supplier.status === "REQUESTED" && !isEmptyV2(supplier.unitPrice)
  );

  for (let i = 0; i < data.length; i++) {
    const request = data[i];

    if (isEmpty(isIndividual)) {
      if (!isEmpty(request.currency)) {
        request.status = "RESPONDED";
      }
    } else {
      if (!isEmpty(request.isIndividualPrice)) {
        request.status = "RESPONDED";
      }
      allMaterialsResponded = false;
      delete request.isIndividualPrice;
    }

    if (isEmpty(request.currency)) {
      allMaterialsResponded = false;
    } else {
      // Check if this is the last material being responded to
      const allOthersResponded = data.every(
        (material, index) => index === i || material.status === "RESPONDED"
      );
      if (allOthersResponded) {
        allMaterialsResponded = true;
      }
    }
  }

  const [saveSupplierRequests] = await mainDataset.query({
    query: `
      UPDATE Supplier_Requests SET
      materialRequested = @materialRequested
      WHERE ID = @RequestID
    `,
    params: {
      RequestID: parseInt(RequestID),
      materialRequested: JSON.stringify(data),
    },
  });

  // Prepare the DELETE query
  if (filteredData.length > 0) {
    let deleteConditions = filteredData.map((supplier, index) => {
      return `supplierID = @supplierID AND partNumber = @partNumber${index}`;
    });

    let deleteQuery = `
    DELETE FROM supplier_price
    WHERE ${deleteConditions.join(" OR ")}
  `;

    let deleteParams = {};
    filteredData.forEach((supplier, index) => {
      deleteParams[`supplierID`] = supplierID;
      deleteParams[`partNumber${index}`] = supplier.partNumber;
    });

    await mainDataset.query({
      query: deleteQuery,
      params: deleteParams,
    });
  }

  // Prepare data for bulk insertion
  if (filteredData.length > 0) {
    let bulkInsertValues = filteredData.map((supplier, index) => {
      return {
        partNumber: supplier.partNumber,
        brand: supplier.brand.toUpperCase(),
        description: supplier.description || "",
        quantity: parseInt(supplier.quantity),
        unitPrice: parseFloat(supplier.unitPrice),
        totalPrice: parseFloat(supplier.totalPrice),
        notes: supplier.notes || "",
        date: setDate(),
        supplierID,
        productCondition: supplier.productCondition,
        id: generateID().toString(),
        requestID: RequestID,
        currency: supplier.currency,
        leadTime: supplier.leadTime,
      };
    });

    let insertQuery = `
    INSERT INTO supplier_price
    (partNumber, brand, description, quantity, unitPrice, totalPrice, notes, date, supplierID, productCondition, ID, requestID, currency, leadTime)
    VALUES
  `;

    let insertParams = {};
    bulkInsertValues.forEach((value, index) => {
      let suffix = index === bulkInsertValues.length - 1 ? ";" : ",";
      insertQuery += `(@partNumber${index}, @brand${index}, @description${index}, @quantity${index}, @unitPrice${index}, @totalPrice${index}, @notes${index}, @date${index}, @supplierID${index}, @productCondition${index}, @id${index}, @requestID${index}, @currency${index}, @leadTime${index})${suffix}`;

      insertParams[`partNumber${index}`] = value.partNumber;
      insertParams[`brand${index}`] = value.brand;
      insertParams[`description${index}`] = value.description;
      insertParams[`quantity${index}`] = value.quantity;
      insertParams[`unitPrice${index}`] = value.unitPrice;
      insertParams[`totalPrice${index}`] = value.totalPrice;
      insertParams[`notes${index}`] = value.notes;
      insertParams[`date${index}`] = value.date;
      insertParams[`supplierID${index}`] = value.supplierID;
      insertParams[`productCondition${index}`] = value.productCondition;
      insertParams[`id${index}`] = value.id;
      insertParams[`requestID${index}`] = value.requestID;
      insertParams[`currency${index}`] = value.currency;
      insertParams[`leadTime${index}`] = value.leadTime;
    });

    await mainDataset.query({
      query: insertQuery,
      params: insertParams,
    });
  }

  // Collect part numbers for deletion and insertion in to_scraping table
  let partNumbersToDelete = [];
  let toScrapingInsertValues = [];

  filteredData.forEach((supplier) => {
    if (supplier.isAutomatedRFQ === true) {
      partNumbersToDelete.push(supplier.partNumber.trim());
      toScrapingInsertValues.push({
        Material_ID: supplier.materialID,
        Part_Number: supplier.partNumber.trim(),
        Brand: supplier.brand.trim().toUpperCase(),
      });
    }
  });

  // Bulk delete and insert in to_scraping table
  if (partNumbersToDelete.length > 0) {
    let deleteQuery = `
    DELETE FROM to_scraping
    WHERE Part_Number IN (${partNumbersToDelete
      .map((_, index) => `@partNumber${index}`)
      .join(", ")})
  `;

    let deleteParams = {};
    partNumbersToDelete.forEach((partNumber, index) => {
      deleteParams[`partNumber${index}`] = partNumber;
    });

    await automationDataset.query({
      query: deleteQuery,
      params: deleteParams,
    });
  }

  if (toScrapingInsertValues.length > 0) {
    let insertQuery = `
    INSERT INTO to_scraping (Material_ID, Part_Number, Brand)
    VALUES
  `;

    let insertParams = {};
    toScrapingInsertValues.forEach((value, index) => {
      let suffix = index === toScrapingInsertValues.length - 1 ? ";" : ",";
      insertQuery += `(@Material_ID${index}, @Part_Number${index}, @Brand${index})${suffix}`;

      insertParams[`Material_ID${index}`] = value.Material_ID;
      insertParams[`Part_Number${index}`] = value.Part_Number;
      insertParams[`Brand${index}`] = value.Brand;
    });

    await automationDataset.query({
      query: insertQuery,
      params: insertParams,
    });
  }

  if (filteredData.length > 0) {
    for (const material of filteredData) {
      await sourcingDataset.query({
        query: `
                UPDATE Centralized_Materials SET
                Status = "QUOTE_RECEIVED"
                WHERE MaterialID = @materialID AND RFQID = @rfqID
              `,
        params: {
          materialID: material.materialID,
          rfqID: material.RFQ_ID,
        },
      });
      await sourcingDataset.query({
        query: `
          UPDATE Centralized_Quotes SET
            Currency = @currency,
            QuoteDate = DATE(TIMESTAMP_MILLIS(@quoteDate)),
            Notes = @notes,
            Source = @source,
            quantity = @quantity,
            unitPrice = @unitPrice,
            totalPrice = @totalPrice,
            LeadTime = @leadTime
          WHERE SupplierID = @supplierID AND MaterialID = @materialID AND RFQID = @rfqID
        `,
        params: {
          currency: material.currency,
          quoteDate: Date.now(),
          notes: material.notes,
          source: "Email",
          quantity: material.quantity,
          unitPrice: parseFloat(material.unitPrice),
          totalPrice: parseFloat(material.totalPrice),
          leadTime: material.leadTime,
          supplierID: parseInt(supplierID),
          materialID: material.materialID,
          rfqID: material.RFQ_ID,
        },
      });
    }
  }

  // Send notifications after insertion
  for (const supplier of filteredData) {
    let message = `The Prices for Material - ${supplier.description} have been received from a supplier`;

    if (!isEmpty(supplier.currency)) {
      sendNotification({
        userID: UserID,
        message: message,
        RFQID: supplier.RFQ_ID,
      });
    }
  }

  if (allMaterialsResponded) {
    const [supplierRequests] = await mainDataset.query({
      query: `
      UPDATE Supplier_Requests SET
      status = @status, respondDate=@date
      WHERE ID = @RequestID

    `,
      params: {
        RequestID: parseInt(RequestID),
        status: "RESPONDED",
        date: setDate(),
      },
    });
  }

  return apiResponse(ADD_SUCCESS, "Supplier Price", null, res);
});

const requestSupplier = apiHandler(async (req, res) => {
  const {
    materialID,
    rfqID,
    partNumber,
    brand,
    quantity,
    suppliers,
    RFQ_ID,
    RFQ_Number,
    RFQ_Name,
    RFQ_Date,
    Delivery_Date,
    Deadline,
    Portal,
    URL,
    FirstName,
    LastName,
  } = req.body;

  // Initialize suppliers information array
  const suppliersInformation = [];

  // Fetch material information
  const [materialInformation] = await scrapedDataset.query({
    query: `
        SELECT Material_Description, showSuppliers
        FROM Material
        WHERE Material_ID = @materialID AND RFQ_ID = @rfqID
      `,
    params: { materialID: parseInt(materialID), rfqID: parseInt(rfqID) },
  });

  if (isEmpty(materialInformation)) {
    return apiError(CUSTOM_ERROR, "Material information not found", null, res);
  }

  const material = [
    {
      description: materialInformation[0]?.Material_Description,
      rfqID,
      materialID,
      partNumber,
      brand,
      quantity,
      status: "REQUESTED",
      unitPrice: "",
      totalPrice: "",
      productCondition: "",
      notes: "",
      currency: "",
      RFQ_ID,
      RFQ_Number,
      RFQ_Name,
      leadTime: "",
      RFQ_Date,
      Delivery_Date,
      Created_By: req.user.userId.toString(),
      Deadline,
      Portal,
      URL,
      name: `${FirstName} ${LastName}`,
    },
  ];

  // Loop through suppliers and fetch information
  for (const supplierID of suppliers) {
    const [existingSupplier] = await mainDataset.query({
      query: `
          SELECT Name, Email, SupplierID
          FROM Suppliers
          WHERE SupplierID = @supplierID
        `,
      params: { supplierID },
    });

    if (!existingSupplier || existingSupplier.length === 0) {
      return apiError(
        CUSTOM_ERROR,
        `Supplier with ID ${supplierID} not found`,
        null,
        res
      );
    }

    suppliersInformation.push(existingSupplier[0]);

    const [existingBrand] = await mainDataset.query({
      query: `
          SELECT * FROM Suppliers_Brand 
          WHERE SupplierID = @supplierID AND UPPER(Brand) = @brand
        `,
      params: { supplierID, brand: brand.toUpperCase() },
    });
  }

  // Handle Supplier_Requests
  for (const supplierInfo of suppliersInformation) {
    const [existingSupplierRequest] = await mainDataset.query({
      query: `
          SELECT * FROM Supplier_Requests 
          WHERE supplierID = @supplierID
        `,
      params: { supplierID: supplierInfo.SupplierID.toString() },
    });

    if (!existingSupplierRequest || existingSupplierRequest.length === 0) {
      const ID = generateID();

      for (const [key, value] of Object.entries(supplierInfo)) {
        if (isEmpty(value)) {
          return apiError(
            CUSTOM_ERROR,
            `Supplier does not have ${key}`,
            null,
            res
          );
        }
      }

      await mainDataset.query({
        query: `
            INSERT INTO Supplier_Requests 
            (ID, supplierName, supplierID, supplierEmail, materialRequested, status)
            VALUES (@ID, @supplierName, @supplierID, @supplierEmail, @materialRequested, @status)
          `,
        params: {
          ID,
          supplierName: supplierInfo.Name,
          supplierID: supplierInfo.SupplierID.toString(),
          supplierEmail: supplierInfo.Email,
          materialRequested: JSON.stringify(material),
          status: "PENDING",
        },
      });
    } else {
      const existingMaterialRequestArray = JSON.parse(
        existingSupplierRequest[0].materialRequested
      );

      const foundExistingMaterial = existingMaterialRequestArray.some(
        (existingMaterial) =>
          existingMaterial.partNumber === material[0].partNumber &&
          existingMaterial.brand === material[0].brand
      );

      if (foundExistingMaterial) {
        existingMaterialRequestArray.forEach((existingMaterial) => {
          if (
            existingMaterial.partNumber === material[0].partNumber &&
            existingMaterial.brand === material[0].brand
          ) {
            existingMaterial.status = "REQUESTED";
            // existingMaterial.productCondition = "";
            // existingMaterial.currency = "";
            // existingMaterial.unitPrice = "";
            // existingMaterial.totalPrice = "";
          }
        });
      } else {
        existingMaterialRequestArray.push(material[0]);
      }

      await mainDataset.query({
        query: `
            UPDATE Supplier_Requests 
            SET materialRequested = @materialRequested, respondDate = NULL, status = "PENDING"
            WHERE supplierID = @supplierID
          `,
        params: {
          materialRequested: JSON.stringify(existingMaterialRequestArray),
          supplierID: supplierInfo.SupplierID.toString(),
        },
      });
    }
  }

  // Update Material table with selected suppliers
  let selectedSuppliers = materialInformation?.showSuppliers
    ? JSON.parse(materialInformation.showSuppliers)
    : [];
  selectedSuppliers = selectedSuppliers.concat(suppliers);

  await scrapedDataset.query({
    query: `
        UPDATE Material 
        SET showSuppliers = @showSuppliers 
        WHERE Material_ID = @materialID
      `,
    params: {
      materialID: parseInt(materialID),
      showSuppliers: JSON.stringify(selectedSuppliers),
    },
  });

  return apiResponse(ADD_SUCCESS, "Suppliers Requests", null, res);
});

const fetchSuppliersRequests = apiHandler(async (req, res) => {
  const { ID } = req.params;

  const [existingSupplierRequests] = await mainDataset.query({
    query: `
      SELECT *
      FROM Supplier_Requests
      WHERE ID=@id
    `,
    params: {
      id: parseInt(ID),
    },
  });

  if (isEmpty(existingSupplierRequests)) {
    return apiError(NOT_FOUND, "Supplier Requests", null, res);
  }

  const SupplierRequests = {
    ID: existingSupplierRequests[0].ID,
    link: `${BASE_URL}/supplier-portal/${ID}`,
    supplierName: existingSupplierRequests[0].supplierName,
    supplierID: existingSupplierRequests[0].supplierID,
    supplierEmail: existingSupplierRequests[0].supplierEmail,
    materialRequested: JSON.parse(
      existingSupplierRequests[0].materialRequested
    ),
    status: existingSupplierRequests[0].status,
    requestDate: existingSupplierRequests[0].requestDate,
    respondDate: existingSupplierRequests[0].respondDate,
    leadTime: existingSupplierRequests[0].leadTime,
  };

  return apiResponse(FETCH, "Suppliers Requests", SupplierRequests, res);
});

const fetchAllSuppliersRequests = apiHandler(async (req, res) => {
  const [existingSupplierRequests] = await mainDataset.query({
    query: `
      SELECT *
      FROM Supplier_Requests
    `,
  });

  if (isEmpty(existingSupplierRequests)) {
    return apiError(NOT_FOUND, "Supplier Requests", null, res);
  }
  const allSupplierRequests = [];

  for (const supplier of existingSupplierRequests) {
    const SupplierRequests = {
      ID: supplier.ID,
      link: `${BASE_URL}/supplier-portal/${supplier.ID.toString()}`,
      supplierName: supplier.supplierName,
      supplierID: supplier.supplierID,
      supplierEmail: supplier.supplierEmail,
      materialRequested: JSON.parse(supplier.materialRequested),
      status: supplier.status,
      requestDate: supplier.requestDate,
      respondDate: supplier.respondDate,
      leadTime: supplier.leadTime,
    };
    allSupplierRequests.push(SupplierRequests);
  }

  return apiResponse(FETCH, "All Suppliers Requests", allSupplierRequests, res);
});

const saveSupplierPrice = apiHandler(async (req, res) => {
  const { data, RequestID, supplierID } = req.body;

  const [saveSupplierRequests] = await mainDataset.query({
    query: `
      UPDATE Supplier_Requests SET
      materialRequested = @materialRequested
      WHERE ID = @RequestID
    `,
    params: {
      RequestID: parseInt(RequestID),
      materialRequested: JSON.stringify(data),
    },
  });

  return apiResponse(SUCCESS, "Supplier Price", null, res);
});

const useSuppliers = apiHandler(async (req, res) => {
  const { Material_ID, showSuppliers } = req.body;
  const showSuppliersString = JSON.stringify(showSuppliers);

  await scrapedDataset.query({
    query: `
      UPDATE Material SET
      showSuppliers = @showSuppliersString
      WHERE Material_ID = @Material_ID
      `,
    params: {
      Material_ID,
      showSuppliersString,
    },
  });

  return apiResponse(UPDATE_SUCCESS, "Selected Suppliers", null, res);
});

const importSupplierPrice = apiHandler(async (req, res) => {
  const { data, SupplierID, Action } = req.body;

  const PartNumbers = data.map((material) => material.PartNumber);
  const queryPartNumbers = PartNumbers.map(
    (partNumber) => `'${partNumber}'`
  ).join(", ");

  const [brands] = await mainDataset.query({
    query: `
      SELECT manufacturer_name FROM partnumbers
      WHERE manufacturer_catalog_number IN (${queryPartNumbers})
    `,
  });

  const brandNames = brands.map((brand) =>
    brand.manufacturer_name.toUpperCase()
  );

  const [existingBrands] = await mainDataset.query({
    query: `
      SELECT Brand FROM Suppliers_Brand as sb
      WHERE SupplierID = @SupplierID
    `,
    params: {
      SupplierID: parseInt(SupplierID),
    },
  });

  const existingBrandNames = existingBrands.map((brand) =>
    brand.Brand.toUpperCase()
  );

  const brandsToInsert = brandNames.filter(
    (brandName) => !existingBrandNames.includes(brandName)
  );
  const bulkInsertValues = brandsToInsert
    .map((brandName) => `(@SupplierID, '${brandName}')`)
    .join(", ");

  if (!isEmpty(brandsToInsert)) {
    await mainDataset.query({
      query: `
        INSERT INTO Suppliers_Brand (SupplierID, Brand)
        VALUES ${bulkInsertValues}
      `,
      params: {
        SupplierID: parseInt(SupplierID),
      },
    });
  }

  const [existingSupplier] = await mainDataset.query({
    query: `
      SELECT * FROM Suppliers as s
      WHERE SupplierID = @SupplierID
    `,
    params: {
      SupplierID: parseInt(SupplierID),
    },
  });

  if (isEmpty(existingSupplier)) {
    return apiError(NOT_FOUND, "SUPPLIER", null, res);
  }

  if (Action.toUpperCase() === "REPLACE") {
    const [deleteExistingSupplierPrices] = await mainDataset.query({
      query: `
        DELETE FROM bulk_supplier_price
        WHERE supplierID = @SupplierID
      `,
      params: {
        SupplierID,
      },
    });

    for (const material of data) {
      material.ID = generateID();
      material.supplierID = SupplierID;
    }

    let supplierPriceInsertValues = data.map((material) => {
      return {
        PartNumber: material.PartNumber,
        Price: parseFloat(material.Price),
        date: setDate(),
        supplierID: material.supplierID.toString(),
        ID: material.ID.toString(),
        Currency: material.Currency,
      };
    });

    let supplierPriceQuery = `
        INSERT INTO bulk_supplier_price (PartNumber, Price, date, supplierID, ID, Currency)
        VALUES
    `;

    let supplierPriceParams = {};
    supplierPriceInsertValues.forEach((supplierPrice, index) => {
      let suffix = index === supplierPriceInsertValues.length - 1 ? ";" : ",";
      supplierPriceQuery += `(@PartNumber${index}, @Price${index}, @date${index}, @supplierID${index}, @ID${index}, @Currency${index})${suffix}`;

      supplierPriceParams[`PartNumber${index}`] = supplierPrice.PartNumber;
      supplierPriceParams[`Price${index}`] = supplierPrice.Price;
      supplierPriceParams[`date${index}`] = supplierPrice.date;
      supplierPriceParams[`supplierID${index}`] = supplierPrice.supplierID;
      supplierPriceParams[`ID${index}`] = supplierPrice.ID;
      supplierPriceParams[`Currency${index}`] = supplierPrice.Currency;
    });

    await mainDataset.query({
      query: supplierPriceQuery,
      params: supplierPriceParams,
    });
  }

  if (Action.toUpperCase() === "UPDATE") {
    const [existingSupplierPrices] = await mainDataset.query({
      query: `
        SELECT * FROM bulk_supplier_price
        WHERE supplierID = @SupplierID
      `,
      params: {
        SupplierID,
      },
    });

    let existingSupplierPricesArray = [];

    for (const existingSupplierPrice of existingSupplierPrices) {
      let supplierPrice = {
        PartNumber: existingSupplierPrice.PartNumber,
        Currency: existingSupplierPrice.Currency,
        Price: existingSupplierPrice.Price,
      };

      let supplierPriceString = JSON.stringify(supplierPrice);
      if (!existingSupplierPricesArray.includes(supplierPriceString)) {
        existingSupplierPricesArray.push(supplierPriceString);
      }
    }

    let newMaterial = [];
    let updateMaterial = [];
    function checkNewMaterial(material) {
      let existingSuppliers = existingSupplierPricesArray.map(
        (supplierPriceJson) => {
          let supplierPrice = JSON.parse(supplierPriceJson);
          let supplierIdentifier = `${supplierPrice.PartNumber}`;
          return supplierIdentifier;
        }
      );

      let materialIdentifier = `${material.PartNumber}`;

      if (!existingSuppliers.includes(materialIdentifier)) {
        newMaterial.push(material);
      } else {
        updateMaterial.push(material);
      }
    }

    for (const material of data) {
      checkNewMaterial(material);
    }

    let addMaterial = newMaterial;
    if (!isEmpty(updateMaterial)) {
      addMaterial = addMaterial.concat(updateMaterial);
      const conditions = updateMaterial.map((item) => ({
        PartNumber: item.PartNumber,
      }));
      const conditionStrings = conditions.map(
        (condition) =>
          `(${
            condition.PartNumber ? `PartNumber = '${condition.PartNumber}'` : ""
          })`
      );

      const whereClause = conditionStrings.join(" OR ");

      const query = `
      DELETE
      FROM bulk_supplier_price
      WHERE supplierID = @SupplierID AND (${whereClause})
      `;

      const [deleteSupplierPrices] = await mainDataset.query({
        query: query,
        params: {
          SupplierID,
        },
      });
    }

    if (!isEmpty(addMaterial)) {
      for (const material of addMaterial) {
        material.ID = generateID();
        material.supplierID = SupplierID;
      }

      let supplierPriceInsertValues = addMaterial.map((material) => {
        return {
          PartNumber: material.PartNumber,
          Price: parseFloat(material.Price),
          date: setDate(),
          supplierID: material.supplierID.toString(),
          ID: material.ID.toString(),
          Currency: material.Currency,
        };
      });

      let supplierPriceQuery = `
          INSERT INTO bulk_supplier_price (PartNumber, Price, date, supplierID, ID, Currency)
          VALUES
      `;

      let supplierPriceParams = {};
      supplierPriceInsertValues.forEach((supplierPrice, index) => {
        let suffix = index === supplierPriceInsertValues.length - 1 ? ";" : ",";
        supplierPriceQuery += `(@PartNumber${index}, @Price${index}, @date${index}, @supplierID${index}, @ID${index}, @Currency${index})${suffix}`;

        supplierPriceParams[`PartNumber${index}`] = supplierPrice.PartNumber;
        supplierPriceParams[`Price${index}`] = supplierPrice.Price;
        supplierPriceParams[`date${index}`] = supplierPrice.date;
        supplierPriceParams[`supplierID${index}`] = supplierPrice.supplierID;
        supplierPriceParams[`ID${index}`] = supplierPrice.ID;
        supplierPriceParams[`Currency${index}`] = supplierPrice.Currency;
      });

      await mainDataset.query({
        query: supplierPriceQuery,
        params: supplierPriceParams,
      });
    }
  }

  return apiResponse(SUCCESS, "Supplier Price", null, res);
});

const importSupplierPriceV2 = apiHandler(async (req, res) => {
  const { SupplierID, data, Action } = req.body;

  const errorEntries = [];
  const filteredData = [];
  data.forEach((entry, index) => {
    const { PartNumber, Price, Currency } = entry;
    const errorEntry = { entryNo: index + 1 };

    if (isEmpty(PartNumber)) {
      errorEntry["message"] = "Part Number is required";
      errorEntries.push(errorEntry);
    } else if (isEmpty(Price)) {
      errorEntry["message"] = "Price is required";
      errorEntries.push(errorEntry);
    } else if (isEmpty(Currency)) {
      errorEntry["message"] = "Currency is required";
      errorEntries.push(errorEntry);
    } else {
      filteredData.push(entry);
    }
  });

  const batchSize = 1500;
  const estimatedBatchTime = batchSize / 100;

  const [existingSupplier] = await mainDataset.query({
    query: `
      SELECT SupplierID FROM Suppliers as s
      WHERE SupplierID = @SupplierID
    `,
    params: {
      SupplierID: parseInt(SupplierID),
    },
  });
  if (isEmpty(existingSupplier)) {
    return apiError(NOT_FOUND, "SUPPLIER", null, res);
  }

  // Populate filteredData array with necessary fields for inserting into table and create batches
  for (const material of filteredData) {
    material.ID = generateID().toString();
    material.Date = setDate();
    material.supplierID = SupplierID;
    material.Price = parseFloat(material.Price);
  }
  const dataBatches = createBatches(filteredData, batchSize);

  if (Action.toUpperCase() === "REPLACE") {
    // Delete all entries of the given supplier
    await mainDataset.query({
      query: `
        DELETE FROM bulk_supplier_price
        WHERE supplierID = @SupplierID
      `,
      params: {
        SupplierID,
      },
    });
  } else if (Action.toUpperCase() === "UPDATE" && filteredData.length) {
    // Map out all unique part numbers
    let partNumbers = new Set(filteredData.map((entry) => entry.PartNumber));
    partNumbers = [...partNumbers];

    // Delete only the once present in the sheet
    await mainDataset.query({
      query: `
        DELETE FROM bulk_supplier_price
        WHERE supplierID = @SupplierID AND PartNumber IN UNNEST(@partNumbers);
      `,
      params: {
        SupplierID,
        partNumbers,
      },
    });
  }

  // Send response with errorEntries and estimatedImportTime
  const responseData = {
    totalEntries: filteredData.length,
    batchSize,
    totalBatches: dataBatches.length,
    estimatedTimeInSeconds: estimatedBatchTime * dataBatches.length,
    errorEntries: errorEntries.length ? errorEntries : [],
  };
  apiResponse(SUCCESS, "Supplier Price", responseData, res);

  console.log("Total Batches:", dataBatches.length);
  console.time("Total time for all batches");
  for (const [batchIndex, batch] of dataBatches.entries()) {
    console.log(`Batch No: ${batchIndex + 1} - Length: ${batch.length} `);
    console.time(`Time for Batch ${batchIndex + 1}`);
    let supplierPriceQuery = `
        INSERT INTO bulk_supplier_price (PartNumber, Price, Date, supplierID, ID, Currency)
        VALUES 
    `;
    const supplierPriceParams = {};

    batch.forEach((material, index) => {
      let suffix = index === batch.length - 1 ? ";" : ",";

      supplierPriceQuery += `
        (@PartNumber${index}, @Price${index}, @Date${index}, 
        @supplierID${index}, @ID${index}, @Currency${index})
        ${suffix}
      `;

      supplierPriceParams[`PartNumber${index}`] = material.PartNumber;
      supplierPriceParams[`Price${index}`] = material.Price;
      supplierPriceParams[`Date${index}`] = material.Date;
      supplierPriceParams[`supplierID${index}`] = material.supplierID;
      supplierPriceParams[`ID${index}`] = material.ID;
      supplierPriceParams[`Currency${index}`] = material.Currency;
    });

    await mainDataset.query({
      query: supplierPriceQuery,
      params: supplierPriceParams,
    });

    console.timeEnd(`Time for Batch ${batchIndex + 1}`);
  }
  console.timeEnd("Total time for all batches");
});

const addSupplier = apiHandler(async (req, res) => {
  const {
    Name,
    ContactName,
    ContactLastname,
    Email,
    Country,
    Web = "",
    Shipping,
    Notes = "",
  } = req.body;

  const date = getCurrentDateTime();

  // Check if the supplier already exists
  const [existingSupplier] = await mainDataset.query({
    query: `
      SELECT * FROM Suppliers
      WHERE UPPER(Name) = @Name OR Email = @Email
    `,
    params: {
      Name: Name.trim().toUpperCase(),
      Email: Email.trim(),
    },
  });

  if (!isEmpty(existingSupplier[0])) {
    return apiError(EXISTS, "Supplier Email or Name", null, res);
  }

  // Fetch approver information only if supplier does not exist
  const [approverInformation] = await mainDataset.query({
    query: `
      SELECT Email, FirstName FROM Users
      WHERE Approver = TRUE
    `,
  });

  if (isEmpty(approverInformation)) {
    return apiError(
      CUSTOM_ERROR,
      "Approver not Available, Please Contact Supervisor",
      null,
      res
    );
  }

  const SupplierID = generateID(5);

  // Create token data
  const tokenData = {
    SupplierID,
    Name,
    ContactName,
    ContactLastname,
    Email,
    Country,
    Web,
    Shipping,
    Notes,
    Date: date,
    Created_By: req.user.userId.toString(),
  };

  const token = jwt.sign(tokenData, JWT_SECRET);

  // Insert new supplier into the database
  await mainDataset.query({
    query: `
      INSERT INTO Suppliers
      (Name, ContactName, ContactLastname, Email, Country, Web, Shipping, Notes, SupplierID, Created_At, Updated_At, Created_By, Status, token)
      VALUES
      (@Name, @ContactName, @ContactLastname, @Email, @Country, @Web, @Shipping, @Notes, @SupplierID, @Created_At, @Updated_At, @Created_By, @Status, @token)
    `,
    params: {
      Name,
      ContactName,
      ContactLastname,
      Email,
      Country,
      Web,
      Shipping,
      Notes,
      SupplierID,
      Created_At: date,
      Updated_At: date,
      Created_By: req.user.userId.toString(),
      Status: "PENDING",
      token,
    },
  });

  const approverEmail = approverInformation[0].Email;
  const approverName = approverInformation[0].FirstName;

  const data = {
    SupplierID,
    Name,
    ContactName,
    ContactLastname,
    Email,
    Country,
    Web,
    Shipping,
    token,
    Notes,
    approverName,
    Created_At: new Date(date).toLocaleDateString(),
    Created_By: `${req.user.firstName} ${req.user.lastName}`,
  };

  // Send approval email to the approver
  const emailOptions = {
    to: approverEmail,
    subject: newSupplierTemplate.emailSubject(data),
    html: newSupplierTemplate.htmlTemplate(data),
  };

  await sendMail(emailOptions);

  return apiResponse(ADD_SUCCESS, "Supplier", null, res);
});

const getSingleSupplier = apiHandler(async (req, res) => {
  const { SupplierID } = req.params;

  const [existingSupplier] = await mainDataset.query({
    query: `
      SELECT CONCAT(u2.FirstName, ' ', u2.LastName) AS Updated_By_Name, CONCAT(u.FirstName, ' ', u.LastName) AS Created_By, s.* FROM Suppliers as s
      LEFT JOIN Users as u ON CAST(u.UserID as STRING) = CAST(s.Created_By as string)
      LEFT JOIN Users as u2 ON CAST(u2.UserID as STRING) = CAST(s.Updated_By as string)
      WHERE SupplierID = @SupplierID
    `,
    params: {
      SupplierID: parseInt(SupplierID),
    },
  });

  if (isEmpty(existingSupplier[0])) {
    return apiError(NOT_FOUND, "Supplier", null, res);
  }

  const [existingApprover] = await mainDataset.query({
    query: `
      SELECT CONCAT(u.FirstName, ' ', u.LastName) AS approverName, Email 
      FROM Users as u
      WHERE approver is true
    `,
  });

  existingSupplier[0].approverName = existingApprover[0].approverName;
  existingSupplier[0].approverEmail = existingApprover[0].Email;

  return apiResponse(FETCH, "Supplier", existingSupplier[0], res);
});

const editSupplier = apiHandler(async (req, res) => {
  const {
    SupplierID,
    Name,
    ContactName,
    ContactLastname,
    Email,
    Country,
    Web = "",
    Shipping,
    Notes = "",
  } = req.body;

  const date = getCurrentDateTime();

  const [oldSupplierInformation] = await mainDataset.query({
    query: `
      SELECT SupplierID, s.Name, ContactName, ContactLastname, s.Email, s.Country, Web, Shipping, Notes, s.Created_At, s.Updated_At, CONCAT(u.FirstName, ' ', u.LastName) AS Created_By 
      FROM Suppliers as s
      LEFT JOIN Users as u ON CAST(UserID as STRING) = CAST(Created_By as string)
      WHERE SupplierID = @SupplierID
    `,
    params: {
      SupplierID: parseInt(SupplierID),
    },
  });

  if (isEmpty(oldSupplierInformation[0])) {
    return apiError(NOT_FOUND, "Supplier", null, res);
  }

  // Check for an existing supplier with the same name or email, excluding the current one
  const [existingSupplier] = await mainDataset.query({
    query: `
      SELECT SupplierID
      FROM Suppliers
      WHERE (UPPER(Name) = @Name OR Email = @Email) AND SupplierID != @SupplierID
    `,
    params: {
      Name: Name.trim().toUpperCase(),
      Email,
      SupplierID: parseInt(SupplierID),
    },
  });

  if (!isEmpty(existingSupplier[0])) {
    return apiError(EXISTS, "Supplier Email or Name", null, res);
  }

  // Fetch approver information only when necessary
  const [approverInformation] = await mainDataset.query({
    query: `
      SELECT Email, FirstName
      FROM Users
      WHERE Approver = TRUE
    `,
  });

  if (isEmpty(approverInformation)) {
    return apiError(
      CUSTOM_ERROR,
      "Approver not Available, Please Contact Supervisor",
      null,
      res
    );
  }

  // Prepare old and new records
  const oldRecord = {
    ...oldSupplierInformation[0],
    Created_At: !isEmpty(oldSupplierInformation[0].Created_At)
      ? new Date(oldSupplierInformation[0].Created_At).toLocaleDateString()
      : null,
    Updated_At: !isEmpty(oldSupplierInformation[0].Updated_At)
      ? new Date(oldSupplierInformation[0].Updated_At).toLocaleDateString()
      : null,
  };

  const newRecord = {
    SupplierID,
    Name,
    ContactName,
    ContactLastname,
    Email,
    Country,
    Web,
    Shipping,
    Notes,
    Created_At: new Date(
      oldSupplierInformation[0].Created_At
    ).toLocaleDateString(),
    Updated_At: new Date(date).toLocaleDateString(),
    Updated_By: `${req.user.firstName} ${req.user.lastName}`,
  };

  const token = jwt.sign(oldRecord, JWT_SECRET);

  const logID = generateID().toString();

  // Update supplier details
  await mainDataset.query({
    query: `UPDATE Suppliers SET
      Name = @Name,
      ContactName = @ContactName,
      ContactLastname = @ContactLastname,
      Email = @Email,
      Country = @Country,
      Web = @Web,
      Shipping = @Shipping,
      Notes = @Notes,
      Updated_At = @Updated_At,
      Updated_By = @Updated_By,
      status = @status,
      token = @token,
      logID = @logID
      WHERE SupplierID = @SupplierID
    `,
    params: {
      SupplierID: parseInt(SupplierID),
      Name,
      ContactName,
      ContactLastname,
      Email,
      Country,
      Web,
      Shipping,
      Notes,
      Updated_At: date,
      Updated_By: req.user.userId.toString(),
      status: "PENDING",
      token,
      logID,
    },
  });

  // Log the update operation
  await mainDataset.query({
    query: `INSERT INTO logs 
      (type, typeID, operation, userID, typeData, date, logID)
      VALUES
      (@type, @typeID, @operation, @userID, @typeData, @date, @ID)
      `,
    params: {
      type: "SUPPLIER",
      typeID: SupplierID.toString(),
      operation: "UPDATE",
      userID: req.user.userId.toString(),
      typeData: JSON.stringify(oldSupplierInformation[0]),
      date,
      ID: logID,
    },
  });

  await mainDataset.query({
    query: `UPDATE Supplier_Requests SET
      supplierEmail = @Email
      WHERE supplierID = @SupplierID
    `,
    params: {
      SupplierID: SupplierID.toString(),
      Email,
    },
  });

  // Send approval email to the approver
  const approverEmail = approverInformation[0].Email.trim();
  const approverName = approverInformation[0].FirstName.trim();

  const emailData = {
    SupplierID,
    approverName,
    oldRecord,
    newRecord,
    token,
  };

  const emailOptions = {
    to: approverEmail,
    subject: updateSupplierTemplate.emailSubject(emailData),
    html: updateSupplierTemplate.htmlTemplate(emailData),
  };

  await sendMail(emailOptions);

  return apiResponse(UPDATE_SUCCESS, "Supplier", null, res);
});

const deleteSupplier = apiHandler(async (req, res) => {
  const { SupplierID } = req.params;

  const [deleteSupplier] = await mainDataset.query({
    query: `
      DELETE FROM Suppliers
      WHERE SupplierID = @SupplierID
    `,
    params: {
      SupplierID: parseInt(SupplierID),
    },
  });

  return apiResponse(DELETE_SUCCESS, "Supplier", null, res);
});

const fetchShipping = apiHandler(async (req, res) => {
  let [shipping] = await mainDataset.query({
    query: `SELECT DISTINCT Origin FROM Shipping_Rates`,
  });

  shipping = shipping.map((shipping) => shipping.Origin);

  return apiResponse(FETCH, "Shipping", shipping, res);
});

const mailSupplier = apiHandler(async (req, res) => {
  const supplierID = req.params.ID;

  const [existingSupplierRequests] = await mainDataset.query({
    query: `
      SELECT *
      FROM Supplier_Requests
      WHERE supplierID=@supplierID
    `,
    params: {
      supplierID,
    },
  });

  if (isEmpty(existingSupplierRequests)) {
    return apiResponse(NOT_FOUND, "Supplier Request", null, res);
  }

  const requestID = existingSupplierRequests[0]?.ID?.toString();

  const [updateSupplierRequest] = await mainDataset.query({
    query: `
      UPDATE Supplier_Requests SET
      status=@status, requestDate=@date
      WHERE ID=@requestID
    `,
    params: {
      status: "REQUESTED",
      requestID: parseInt(requestID),
      date: setDate(),
    },
  });

  const name = existingSupplierRequests[0]?.supplierName;
  const email = existingSupplierRequests[0]?.supplierEmail;
  const ID = existingSupplierRequests[0]?.ID?.toString();

  const link = `${BASE_URL}/supplier-portal/${ID}`;

  const data = {
    name,
    link,
  };

  let option = {
    to: email,
    subject: supplierMaterialRequestTemplate.emailSubject,
    html: supplierMaterialRequestTemplate.htmlTemplate(data),
  };

  try {
    await sendMail(option);
  } catch (error) {
    console.log("Error Occured While Sending Mail - ", error);
  }

  return apiResponse(SENT_SUCCESS, "Mail", null, res);
});

const updateSupplierStatus = apiHandler(async (req, res) => {
  const { ID: SupplierID, status, reason, token } = req.body;

  const [SupplierInformation] = await mainDataset.query({
    query: `SELECT Name, Created_By, status, logID FROM Suppliers WHERE SupplierID = @SupplierID AND token = @token`,
    params: {
      SupplierID: parseInt(SupplierID),
      token,
    },
  });

  if (isEmpty(SupplierInformation[0])) {
    return apiError(NOT_FOUND, "Supplier", null, res);
  }

  if (SupplierInformation[0].status !== "PENDING") {
    return apiError(
      CUSTOM_ERROR,
      `Can not be ${status}, Supplier has already been ${SupplierInformation[0].status}`,
      null,
      res
    );
  }

  let name = SupplierInformation[0].Name;
  let userId = SupplierInformation[0].Created_By;
  let logID = SupplierInformation[0].logID;

  if (status === "APPROVED") {
    const [approveSupplier] = await mainDataset.query({
      query: `UPDATE Suppliers SET
              Status = @status,
              lastUpdatedStatus = @status
              WHERE SupplierID = @SupplierID`,
      params: {
        SupplierID: parseInt(SupplierID),
        status,
      },
    });

    let message = `The Supplier - ${name} has been approved`;
    sendNotification({
      userID: userId,
      message: message,
    });

    return apiResponse(STATUS_SUCCESS, "Supplier", null, res);
  }

  if (!isEmpty(logID)) {
    const [oldSupplierInformation] = await mainDataset.query({
      query: `SELECT typeData FROM logs
              WHERE typeID = @typeID AND type = @type AND logID = @logID
              `,
      params: {
        typeID: SupplierID.toString(),
        type: "SUPPLIER",
        logID,
      },
    });

    let oldData = JSON.parse(oldSupplierInformation[0].typeData);

    const [rejectSupplier] = await mainDataset.query({
      query: `UPDATE Suppliers SET
              Status = "APPROVED",
              lastUpdatedStatus = @status,
              Reject_Reason = @reason,
              Name = @Name,
              ContactName = @ContactName,
              Email = @Email,
              Country = @Country,
              Web = @Web,
              Shipping = @Shipping,
              Notes = @Notes
              WHERE SupplierID = @SupplierID
              `,
      params: {
        SupplierID: parseInt(SupplierID),
        status,
        reason,
        Name: oldData.Name,
        ContactName: oldData.ContactName,
        Email: oldData.Email,
        Country: oldData.Country,
        Web: oldData.Web,
        Shipping: oldData.Shipping,
        Notes: oldData.Notes,
      },
    });
  }

  const [rejectSupplier] = await mainDataset.query({
    query: `UPDATE Suppliers SET
            Status = @status,
            lastUpdatedStatus = @status,
            Reject_Reason = @reason  
            WHERE SupplierID = @SupplierID
            `,
    params: {
      SupplierID: parseInt(SupplierID),
      status,
      reason,
    },
  });

  let message = `The Supplier - ${name} has been rejected for the following reason: ${reason}`;

  sendNotification({
    userID: userId,
    message: message,
  });

  return apiResponse(STATUS_SUCCESS, "Supplier", null, res);
});

const markUnavailable = apiHandler(async (req, res) => {
  const { data, materialID, partNumber } = req.body;

  const filteredMaterialRequested = data.materialRequested.filter(
    (material) => !(material.partNumber === partNumber)
  );

  const removeMaterial = data.materialRequested.filter(
    (material) => material.partNumber === partNumber
  );

  const [updateSupplierRequest] = await mainDataset.query({
    query: `UPDATE Supplier_Requests
    SET materialRequested = @materialRequested
    WHERE supplierID = @supplierID`,
    params: {
      materialRequested: JSON.stringify(filteredMaterialRequested),
      supplierID: data.supplierID,
    },
  });

  let message = `The Material - ${partNumber} is not available from supplier : ${data.supplierName}`;

  sendNotification({
    userID: removeMaterial[0].Created_By,
    message: message,
  });

  return apiResponse(
    CUSTOM_SUCCESS,
    "Material Marked Unavailable Successfully",
    removeMaterial,
    res
  );
});

const getEmailSupplier = apiHandler(async (req, res) => {
  let { page, limit, search, sort, filters } = req.body;

  limit = limit || Number.MAX_SAFE_INTEGER;
  let offset = ((page || 1) - 1) * (limit || Number.MAX_SAFE_INTEGER);

  let whereExpression = `WHERE sb.Brand IS NOT NULL`;
  let orderByExpression = ``;

  // whereExpression += `WHERE Status = 'APPROVED' OR Status IS NULL`;

  // whereExpression += !isEmpty(search)
  //   ? `WHERE ContactLastname LIKE '%${search}%' OR Email LIKE '%${search}%' OR Name LIKE '%${search}%' OR ContactName LIKE '%${search}%' `
  //   : ``;

  orderByExpression += !isEmpty(sort)
    ? ` ORDER BY ${sort.field} ${sort.order} `
    : ` ORDER BY Name ASC `;

  let filterBrand = "";
  if (!isEmpty(filters)) {
    filters.forEach((filter) => {
      if (filter.heading === "Email-Supplier") {
        let brandList = ""; // Initialize brandList outside the conditional block
        if (filter.brands && filter.brands.length > 0) {
          // Convert brands array to a string with quoted values
          brandList = filter.brands
            .map((brand) => `'${brand.toUpperCase()}'`)
            .join(", ");
        }

        // Only add the `IN` clause if brandList is not empty
        if (brandList) {
          filterBrand = `AND UPPER(sb.Brand) IN (${brandList})`;
          whereExpression += ` AND ${filter.field} = ${filter.data}`;
        } else {
          // Handle the case where no brands are provided
          whereExpression += ` AND ${filter.field} = ${filter.data}`;
        }
      }
    });
  }

  let fetchSupplierQuery = `SELECT 
    s.SupplierID, 
    s.Name, 
    s.Is_Email_Supplier, 
FROM Suppliers AS s
LEFT JOIN Suppliers_Brand AS sb ON s.SupplierID = sb.SupplierID ${filterBrand}
${whereExpression}
${orderByExpression}`;

  console.log("fetchSupplierQuery", fetchSupplierQuery);

  let [existingSuppliers] = await mainDataset.query({
    query: fetchSupplierQuery,
  });

  if (isEmpty(existingSuppliers)) {
    return apiError(NOT_FOUND, "Suppliers", null, res);
  }

  existingSuppliers = existingSuppliers.filter(
    (supplier) => !isEmpty(supplier.Name)
  );

  let existingSuppliersIds = [];
  let suppliers = [];

  for (const supplier of existingSuppliers) {
    if (!existingSuppliersIds.includes(supplier.SupplierID)) {
      suppliers.push(supplier);
      existingSuppliersIds.push(supplier.SupplierID);
    }
  }

  const count = suppliers.length;

  existingSuppliers = suppliers.splice(offset, limit);

  const data = {
    count: count,
    suppliers: existingSuppliers,
  };

  return apiResponse(FETCH, "Suppliers", data, res);
});

module.exports = {
  getSupplier,
  fetchSuppliers,
  addSupplierPrice,
  addSupplierPriceV2,
  requestSupplier,
  fetchSuppliersRequests,
  fetchAllSuppliersRequests,
  saveSupplierPrice,
  useSuppliers,
  importSupplierPrice,
  importSupplierPriceV2,
  addSupplier,
  getSingleSupplier,
  editSupplier,
  deleteSupplier,
  fetchShipping,
  mailSupplier,
  updateSupplierStatus,
  markUnavailable,
  getEmailSupplier,
};
