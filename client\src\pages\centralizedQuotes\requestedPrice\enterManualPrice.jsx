import React, { useEffect, useState } from 'react';
import { FormControlLabel, Checkbox, Typography } from '@mui/material';
import { styled } from '@mui/system';
import InputField from 'pages/component/inputField';
import SelectComponent from 'pages/component/selectComponent';
import { CurrencyList } from 'utils/constant';
import ErrorMessage from 'pages/component/errorMessage';
import { useSelector } from 'react-redux';
import { getApprovedSupplier } from 'utils/helper';
import DynamicAutocomplete from 'pages/component/autoComplete';

const EnterPriceManually = ({ manualPriceInfo, Brand }) => {
  const [manualPrice = {}, setManualPrice = () => {}, errors = {}] = manualPriceInfo || [];
  const supplierListData = useSelector((state) => state.offer?.supplierList || []);
  const [supplierList, setSupplierList] = useState([]);

  useEffect(() => {
    const updatedSupplierList = getApprovedSupplier(supplierListData)?.map((supplier) => ({
      ...supplier,
      label: supplier?.Name,
      id: supplier?.SupplierID,
      Status: supplier?.Status
    }));

    setSupplierList(updatedSupplierList);
  }, [supplierListData]);

  const handleInputChange = (name, value) => {
    setManualPrice((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const inputFields = [
    {
      type: 'autoSelect',
      name: 'supplier',
      placeholder: 'Select Supplier',
      options: supplierList || []
    },
    { type: 'number', name: 'qty', placeholder: 'Qty' },
    { type: 'number', name: 'price', placeholder: 'Price' },
    { type: 'select', name: 'currency', placeholder: 'Select Currency', options: CurrencyList }
  ];

  const renderInputField = ({ type, name, placeholder, label, checked, options }) => {
    const value = manualPrice[name] || (type === 'number' && manualPrice[name] === 0 ? 0 : '');

    switch (type) {
      case 'number':
        return (
          <InputField
            type="number"
            name={name}
            placeholder={placeholder}
            errors={errors}
            value={value}
            onChange={(e) => handleInputChange(name, name === 'qty' ? parseInt(e.target.value) : Number(e.target.value))}
            fullWidth
          />
        );
      case 'text':
        return (
          <InputField
            type="text"
            name={name}
            placeholder={placeholder}
            errors={errors}
            value={value}
            onChange={(e) => handleInputChange(name, e.target.value)}
            fullWidth
          />
        );
      case 'select':
        return (
          <>
            <SelectComponent
              value={value || ''}
              onSelectChange={(e) => {
                handleInputChange(name, e.target.value);
              }}
              fullWidth
              items={options}
              // label={placeholder}
              error={errors[name]}
            />
            {errors[name] && <ErrorMessage message={errors[name]} />}
          </>
        );
      case 'autoSelect':
        return (
          <>
            <DynamicAutocomplete
              options={supplierList}
              value={value || undefined}
              onChange={(e, newValue) => {
                handleInputChange(name, newValue);
              }}
              getOptionLabel={(option) => option.label}
            />
            {errors[name] && <ErrorMessage message={errors[name]} />}
          </>
        );

      default:
        return null;
    }
  };

  return (
    <div className="enter-manual-price-popup">
      <Typography variant="h5" color="secondary" mb={2}>
        Enter Price Manually
      </Typography>

      {inputFields?.map((field, index) => (
        <div key={index} classNam="enter-manual-inner-wrapper">
          <Typography variant="h6" color="secondary" mb={2} mt={1}>
            {field?.placeholder}
          </Typography>
          {renderInputField(field)}
        </div>
      ))}
    </div>
  );
};

export default EnterPriceManually;
