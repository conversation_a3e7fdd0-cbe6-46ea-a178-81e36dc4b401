import { getAllCentralizedRequestAction, sendBulkQuoteRequest } from "redux/reducers/centralizedReducer";
import { sendQuoteRequest } from "redux/reducers/offerReducer";
import { inviteExpiredSupplier } from "redux/reducers/supplierPortalReducer";
import { showAlert } from "utils/helper";
import { defaultFilterPayload } from "./constant";
import { createCentralizedFilterPayload } from "./header/filter";

const createRequestPayload = (updatedRequestedQuote, supplier, keyName) => {
  const materials = updatedRequestedQuote
    ?.filter(item => item[keyName])
    ?.map(item => ({

      rfqID: item?.RFQ_ID?.toString(),
      partNumber: item?.Material_Information?.partNumber || item.PartNumber,
      brand: item?.Material_Information?.brand || item.Brand,
      materialID: item?.Material_Information?.materialID || item.Material_ID,
      quantity: item?.Material_Information?.quantity || item.quantity,
      RFQ_Number: item?.Material_Information?.RFQ_Number || item.Material_Information.RFQ_Number,
      RFQ_Name: item?.Material_Information?.RFQ_Name || item.Material_Information.RFQ_Name,
      Material_Description: item?.Material_Information?.description || item.Material_Information.Material_Description,
      RFQ_Date: item?.Material_Information?.RFQ_Date?.value || item.Material_Information.RFQ_Date?.value,
      Delivery_Date: item?.Material_Information?.Delivery_Date?.value || item.Material_Information.Delivery_Date?.value,
      Deadline: item?.Material_Information?.Deadline?.value || item.Material_Information.Deadline?.value,
      Portal: item?.Material_Information?.Portal || item.Material_Information.Portal,
      URL: item?.Material_Information?.URL || item.Material_Information.URL,
      FirstName: item.firstName,
      LastName: item.lastName
    })
    );

  return {
    supplier: supplier?.SupplierID,
    materials: materials
  };
};

export const requestQuoteFunction = async (updatedRequestedQuote, dispatch, supplier, actionName, keyName, formData) => {

  const payload = createRequestPayload(updatedRequestedQuote, supplier, keyName);
  const isValid = validate(payload, dispatch)
  if (isValid) {
    const response = await dispatch(sendBulkQuoteRequest(payload));
    const success = response?.payload?.success || false;
    const { filterValues } = formData?.filters || {}
    let centralizedPayload = filterValues && filterValues?.length > 0 ? createCentralizedFilterPayload(filterValues) : defaultFilterPayload
    if (success && actionName !== 'sendNow') {
      dispatch(getAllCentralizedRequestAction(centralizedPayload));
    }

    let mailSentSuccess = false;
    if (success && actionName === 'sendNow') {
      mailSentSuccess = await sendMail(supplier?.SupplierID, dispatch);
      if (mailSentSuccess) {
        dispatch(getAllCentralizedRequestAction(centralizedPayload));
      }
    }

    return mailSentSuccess;
  }
};

export const sendMail = async (supplierId, dispatch) => {
  const sendMailPayload = { supplierId: supplierId, isRequestPage: true };
  const response = await dispatch(inviteExpiredSupplier(sendMailPayload));
  const success = response?.payload?.success || false;
  return success;
};

const validate = (request, dispatch) => {
  let isValid = true;

  if (!request?.materials || request?.materials.length === 0) {
    showAlert(dispatch, false, 'Please select at least one material.', true);
    isValid = false;
  }

  if (!request?.supplier) {
    showAlert(dispatch, false, 'Please select a supplier.', true);
    isValid = false;
  }

  return isValid;
};
