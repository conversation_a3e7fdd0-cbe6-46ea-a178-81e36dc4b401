require("dotenv").config({
  path: "./.env",
});

module.exports = {
  NODE_ENV: process.env.NODE_ENV,
  PORT: process.env.PORT,
  PROJECT_ID: process.env.PROJECT_ID,
  DATASET_ID_MAIN: process.env.DATASET_ID_MAIN,
  DATASET_ID_SCRAPE: process.env.DATASET_ID_SCRAPE,
  DATASET_ID_LANDINGZONE: process.env.DATASET_ID_LANDINGZONE,
  DATASET_ID_DASHBOARDS: process.env.DATASET_ID_DASHBOARDS,
  DATASET_ID_AUTOMATION: process.env.DATASET_ID_AUTOMATION,
  DATASET_ID_SOURCING: process.env.DATASET_ID_SOURCING,
  GCP_CRED: process.env.GOOGLE_APPLICATION_CREDENTIALS,
  JWT_SECRET: process.env.JWT_SECRET,
  MONDAY_TOKEN: process.env.MONDAY_TOKEN,
  CORS_ORIGIN: process.env.CORS_ORIGIN,
  CUSTOMS_TAX: 0.06,
  TAX_PERCENT: 19,
  HANDLING_COST: 150,
  AUSTRALIA_HANDLING_COST: 300,
  BASE_URL: process.env.BASE_URL,

  // SMTP
  SMTP_USER: process.env.SMTP_USER,
  SMTP_PASS: process.env.SMTP_PASS,
  SMTP_EMAIL: process.env.SMTP_EMAIL,
};
