import React, { useEffect, useState } from 'react';
import { get } from 'lodash';
import PropTypes from 'prop-types';
import RadioButtonsGroup from 'pages/component/radioBtn';

const FilterRadioButtons = ({ data, handleFilterChange }) => {
  const [filterData, setFilterData] = useState([]);

  useEffect(() => {
    let optionData = get(data, 'options', []);
    if (optionData && optionData?.length > 0) {
      setFilterData(optionData);
    }
  }, [data]);

  const createRadioOptions = (optionsData) => {
    return optionsData?.map((item) => ({
      label: item?.label,
      value: item?.value,
      select: item?.select
    }));
  };

  return (
    <>
      <div className="radio-buttons">
        <RadioButtonsGroup
          type="radio"
          name="radioSelect"
          onChange={(e) => {
            handleFilterChange(get(e, 'target.value'));
          }}
          value={get(data, 'data', '')}
          labels={createRadioOptions(filterData)}
        />
      </div>
    </>
  );
};

FilterRadioButtons.propTypes = {
  data: PropTypes.array.isRequired,
  handleFilterChange: PropTypes.func.isRequired
};

export default FilterRadioButtons;
