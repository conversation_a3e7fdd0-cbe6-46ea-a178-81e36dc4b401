import { Button } from '@mui/material';
import SelectComponent from 'pages/component/selectComponent';
import { addSupplierFilter, ARRAY_TYPE, FORMREQUEST, PRICETABLE, REQUESTEDPRICE } from '../constant';
import { getApprovedSupplier } from 'utils/helper';
import DynamicAutocomplete from 'pages/component/autoComplete';
import FilterComponent, { createCentralizedFilterPayload } from './filter';
import { useDispatch, useSelector } from 'react-redux';
import { requestQuoteFunction } from '../sendEmail';
import InputField from 'pages/component/inputField';
import CloseIcon from '@mui/icons-material/Close';
import { useState } from 'react';
import { ARRAY } from 'pages/availableRfq/materialFilter.js/component/constant';

const SupplierRequestPriceHeader = ({ formData, setFormData, supplierList, loading }) => {
  const [search, setSearch] = useState('');
  const dispatch = useDispatch();
  const offer = useSelector((state) => state.offer?.data);
  const handleChange = (e, newValue) => {
    const newFilter = {
      heading: 'supplierBrandList',
      data: newValue?.brands,
      notShowFilter: true,
      type: ARRAY_TYPE
    };

    setFormData({
      ...formData,
      requestedPrice: {
        ...formData?.requestedPrice,
        supplier: newValue
      },
      filters: {
        ...formData?.filters,
        page: 1,
        filterValues: addSupplierFilter(formData?.filters?.filterValues, newFilter, 'supplierBrandList')
      }
    });
  };

  const isDisabled = () => {
    return loading;
  };

  const handleInputChange = (key, value, isClose) => {
    if (key === 13) {
      let filterData = formData?.filters?.filterValues || [];

      // Check if an entry with heading: "search" already exists
      const searchIndex = filterData.findIndex((item) => item.field === 'search');

      if (searchIndex !== -1) {
        // Update the existing entry
        filterData[searchIndex].data = value;
      } else {
        // Add new entry if not found
        filterData.push({
          field: 'search',
          data: value,
          notShowFilter: true
        });
      }
      setFormData({
        ...formData,
        changeComponent: isClose ? REQUESTEDPRICE : formData?.changeComponent,
        filters: {
          ...formData?.filters,
          searchQuery: search,
          filterValues: filterData,
          page: 1
        }
      });
    }
  };

  const { supplier, tableData } = formData?.requestedPrice || {};
  const { searchQuery } = formData?.filters || {};
  return (
    <div className="header-container">
      <div className="search-container box">
        <div className="input-container searchWrapper">
          <InputField
            style={{ marginLeft: 1, flex: 1 }}
            onKeyDown={(e, key) => {
              handleInputChange(e?.keyCode, search);
            }}
            type="text"
            placeholder="Enter your search"
            value={search}
            onChange={(e) => {
              setSearch(e.target.value);
              handleInputChange(13, e?.target?.value);
            }}
            className="search-field"
          />
          <div className="closeIcon">
            <CloseIcon
              onClick={() => {
                setSearch('');
                handleInputChange(13, '');
              }}
            />
          </div>
        </div>
      </div>
      <div className="actions-container">
        <div className="buttons-container">
          <div className="auto-select">
            <DynamicAutocomplete
              options={getApprovedSupplier(supplierList)}
              value={supplier}
              onChange={handleChange}
              placeholder="Select Supplier"
              isNotRemoveButton={false}
              getOptionLabel={(option) => option.label}
            />
          </div>
          <div className="buttons-container">
            <Button
              variant="contained"
              size="small"
              onClick={() => {
                setSearch('');
                handleInputChange(13, '', true);
              }}
              disabled={isDisabled()}
            >
              Close
            </Button>
            <Button
              variant="contained"
              size="small"
              onClick={() => requestQuoteFunction(tableData, dispatch, supplier, '', 'select', formData)}
              disabled={isDisabled()}
            >
              Send Later
            </Button>
            <Button
              variant="contained"
              size="small"
              onClick={() => requestQuoteFunction(tableData, dispatch, supplier, 'sendNow', 'select', formData)}
              disabled={isDisabled()}
            >
              Send Now
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupplierRequestPriceHeader;
