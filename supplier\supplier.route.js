const {
  getSupplier,
  fetchSuppliers,
  addSupplierPrice,
  addSupplierPriceV2,
  requestSupplier,
  fetchSuppliersRequests,
  fetchAllSuppliersRequests,
  saveSupplierPrice,
  useSuppliers,
  importSupplierPrice,
  addSupplier,
  getSingleSupplier,
  editSupplier,
  deleteSupplier,
  fetchShipping,
  mailSupplier,
  importSupplierPriceV2,
  updateSupplierStatus,
  markUnavailable,
  getEmailSupplier,
} = require("./supplier.controller");
const {
  fetchSuppliersSchema,
  addSupplierPriceSchema,
  requestSupplierSchema,
  fetchSuppliersRequestsSchema,
  saveSupplierPriceSchema,
  useSupplierSchema,
  importSupplierPriceSchema,
  addSupplierSchema,
  getSingleSupplierSchema,
  editSupplierSchema,
  deleteSupplierSchema,
  getSupplierSchema,
  mailSupplierSchema,
  updateSupplierStatusSchema,
  markUnavailableSchema,
} = require("./supplier.validation");

const router = require("express").Router();
const { validate } = require("../middlewares/validation.middleware");

router.post("/get", validate(getSupplierSchema, "body"), getSupplier);

router.post("/fetch", validate(fetchSuppliersSchema, "body"), fetchSuppliers);

router.post(
  "/add-price",
  validate(addSupplierPriceSchema, "body"),
  addSupplierPriceV2
);

router.post(
  "/save-price",
  validate(saveSupplierPriceSchema, "body"),
  saveSupplierPrice
);

router.post(
  "/request",
  validate(requestSupplierSchema, "body"),
  requestSupplier
);

router.post(
  "/suppliers-requests/:ID",
  validate(fetchSuppliersRequestsSchema, "params"),
  fetchSuppliersRequests
);

router.get("/all-suppliers-requests", fetchAllSuppliersRequests);

router.post("/use-supplier", validate(useSupplierSchema, "body"), useSuppliers);

router.post(
  "/import-price",
  validate(importSupplierPriceSchema, "body"),
  importSupplierPriceV2
);

router.post("/add", validate(addSupplierSchema, "body"), addSupplier);

router.post(
  "/get/:SupplierID",
  validate(getSingleSupplierSchema, "params"),
  getSingleSupplier
);

router.post("/edit", validate(editSupplierSchema, "body"), editSupplier);

router.post(
  "/delete/:SupplierID",
  validate(deleteSupplierSchema, "params"),
  deleteSupplier
);

router.get("/shipping", fetchShipping);

router.post(
  "/follow-up/supplier-price/:ID",
  validate(mailSupplierSchema, "params"),
  mailSupplier
);

router.post(
  "/update-status",
  validate(updateSupplierStatusSchema, "body"),
  updateSupplierStatus
);

router.delete(
  "/markUnavailable",
  validate(markUnavailableSchema, "body"),
  markUnavailable
);

router.post("/email-supplier", validate(getSupplierSchema, "body"), getEmailSupplier);

module.exports = router;
