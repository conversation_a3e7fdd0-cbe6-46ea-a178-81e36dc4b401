import React from 'react';
import { Typography, Box, CardContent, Grid } from '@mui/material';
import { convertDateToStringFormat } from 'utils/helper';

const ClientDetailTable = React.memo(({ fields, clientData }) => {
  return (
    <Grid container spacing={2} className="gridContainer">
      {fields?.map((field) => {

        if (field.show && !clientData[field.show]) {
          return null;
        }

        return (
          <Grid item xs={12} sm={3} key={field.key} className="gridItem">
            <CardContent className="cardContent">
              {field.icon && (
                <Box className="iconBox" sx={{ color: 'primary.main' }}>
                  {field.icon}
                </Box>
              )}

              <Box sx={{ minHeight: '40px' }}>
                <Typography variant="body1" className="labelText">
                  {field.label}:
                </Typography>

                <Typography variant="body2" className="valueText">
                  {field.type === 'date'
                    ? clientData[field.key]
                      ? convertDateToStringFormat(clientData[field.key], false, false)
                      : 'N/A'
                    : field.type === 'link'
                      ? clientData[field.key] ? (
                        <a
                          href={clientData[field.key]}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="link"
                        >
                          Link
                        </a>
                      ) : 'N/A'
                      : clientData[field?.key] || 'N/A'}
                </Typography>
              </Box>
            </CardContent>
          </Grid>
        );
      })}
    </Grid>
  );
});

export default ClientDetailTable;
