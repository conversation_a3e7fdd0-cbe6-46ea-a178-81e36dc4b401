import { get } from 'lodash';
import ErrorMessage from './errorMessage.jsx';
import PropTypes from 'prop-types';
import { IconButton, InputAdornment, OutlinedInput } from '@mui/material';
import EyeOutlined from '@ant-design/icons/EyeOutlined';
import EyeInvisibleOutlined from '@ant-design/icons/EyeInvisibleOutlined';
import { useEffect, useState } from 'react';

const InputField = ({
  type,
  className,
  errors,
  id,
  placeholder,
  value,
  name,
  onBlur,
  onKeyDown,
  registerRef,
  onChange,
  handleBlur,
  disabled,
  style,
  errorMessage,
  autoComplete // New prop for autocomplete
}) => {
  const [showPassword, setShowPassword] = useState(false);
  
  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };
  
  useEffect(() => {
    const input = document.getElementById(id);
    if (input && input.value) {
      input.value = '';
    }
  }, [id]);

  const inputType = type === 'password' ? (showPassword ? 'text' : type) : type;
  
  return (
    <>
      <OutlinedInput
        type={inputType || 'text'}
        className={className}
        id={id} // Ensure id is used properly
        placeholder={placeholder}
        onBlur={handleBlur}
        value={value}
        onKeyDown={onKeyDown}
        name={name}
        style={{ ...style, borderRadius: '8px' }}
        {...registerRef}
        onChange={(e) => onChange(e, type)}
        disabled={disabled || false}
        autoComplete={autoComplete} 
        fullWidth
        endAdornment={
          type === 'password' && (
            <InputAdornment position="end">
              <IconButton
                aria-label="toggle password visibility"
                onClick={handleClickShowPassword}
                onMouseDown={handleMouseDownPassword}
                edge="end"
                color="secondary"
              >
                {showPassword ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              </IconButton>
            </InputAdornment>
          )
        }
        error={Boolean(get(errors, name)) || errorMessage}
      />
      {(errors || errorMessage) && <ErrorMessage message={get(errors, `${name}`, '') || errorMessage} />}
    </>
  );
};


InputField.propTypes = {
  type: PropTypes.string,
  className: PropTypes.string,
  errors: PropTypes.object,
  id: PropTypes.string,
  placeholder: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  name: PropTypes.string.isRequired,
  registerRef: PropTypes.object,
  onChange: PropTypes.func.isRequired,
  handleBlur: PropTypes.func,
  disabled: PropTypes.bool,
  style: PropTypes.object
};

export default InputField;
