import React from 'react';
import { Typo<PERSON>, TextField, Grid, Select, MenuItem, FormControl, InputLabel, Stack, Button } from '@mui/material';
import InputField from 'pages/component/inputField';
import { get } from 'lodash';
import TextAreaComponent from 'pages/component/textArea';
import SelectComponent from 'pages/component/selectComponent';

const renderFormControl = (option, onChange, value) => {
  return (
    <Typography variant="body1" className="view-only-text" color="secondary">
      {value || 'N/A'}
    </Typography>
  );
};

const OfferDetails = ({ onChange, handleNotesChange, offer, clientList, calculateOffer, loading }) => {
  const options = [
    {
      label: 'Offer Currency',
      values: [
        { label: 'USD', value: 'USD' },
        { label: 'EUR', value: 'EUR' },
        { label: 'GBP', value: 'GBP' },
        { label: 'CLP', value: 'CLP' },
        { label: 'AUD', value: 'AUD' }
      ],
      type: 'select',
      name: 'offerCurrency'
    },
    {
      label: 'Language',
      values: [{ label: 'Spanish', value: 'Spanish' }],
      type: 'select',
      name: 'Language'
    },

    { label: 'Client', values: clientList, type: 'select', name: 'clientId' },
    { label: 'Valid for', values: '', type: 'input', inputType: 'number', name: 'validFor' },
    { label: 'Payment Terms', values: '', type: 'input', inputType: 'text', name: 'paymentTerm' }
  ];

  return (
    <Stack>
      <Typography variant="h4" color="secondary">
        Offer Details
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={4}>
          {options?.map((option, index) => (
            <Grid container spacing={1} alignItems="center" key={index} ml={1}>
              <Grid item xs={4}>
                <Typography variant="body1">{option.label}:</Typography>
              </Grid>
              <Grid item xs={7}>
                {renderFormControl(option, onChange, offer[option?.name])}
              </Grid>
            </Grid>
          ))}
        </Grid>
        <Grid item xs={6} display="flex">
          <Typography variant="h5" mr={2}>
            Notes
          </Typography>
          <TextAreaComponent
            id="note"
            style={{ width: '100%' }}
            name="notes"
            value={offer?.notes || ''}
            multiline
            minRows={8}
            disabled
            variant="outlined"
            fullWidth
            onChange={(name, e) => {
              handleNotesChange(name, e.target.value);
            }}
          />
        </Grid>
      </Grid>
    </Stack>
  );
};

export default OfferDetails;
