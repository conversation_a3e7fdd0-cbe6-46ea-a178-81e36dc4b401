import * as React from 'react';
import PropTypes from 'prop-types';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import { Grid, ListItemAvatar, ListItemText, Skeleton, Typography } from '@mui/material';
import Image from 'pages/component/image';
import { get } from 'lodash';
import Loader from 'components/Loader';
import ConvertHtml from '../../../singlrRfq/convertHtmlContent';
const RfqModelSkeletonCard = ({ loading, handleClose, open }) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));
  return (
    <Dialog fullScreen={fullScreen} open={open} onClose={handleClose} aria-labelledby="responsive-dialog-title">
      <DialogTitle id="responsive-dialog-title" className="material-dialogbox">
        {' '}
        <ListItemAvatar className="material-icon">
          <Skeleton variant="rectangular" width={50} height={50} />
        </ListItemAvatar>
        <Grid item xs>
          <ListItemText sx={{ width: '390px' }} primary={<Skeleton variant="text" width="80%" />} />
        </Grid>
      </DialogTitle>
      <DialogContent sx={{ padding: '0px', paddingRight: 2 }}>
        <DialogContentText>
          {' '}
          <Grid justifyContent="center">
            <>
              <Skeleton variant="rectangular" width="100%" height={56} />
              <Skeleton variant="rectangular" width="100%" height={56} />
              <Skeleton variant="rectangular" width="100%" height={100} />
            </>
          </Grid>
        </DialogContentText>
      </DialogContent>
      <DialogActions sx={{ justifyContent: 'center' }}>
        <Button variant="contained" disabled={loading}></Button>
        <Button variant="contained" disabled={loading}></Button>
        <Button variant="contained" disabled={loading}></Button>
      </DialogActions>
    </Dialog>
  );
};

export default RfqModelSkeletonCard;
