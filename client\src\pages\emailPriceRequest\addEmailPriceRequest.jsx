import { Box, Typography, Button, Divider, Paper, Grid, IconButton } from '@mui/material';
import { isArray, isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getSupplierEmailList, getSupplierList, setSupplierListAction } from 'redux/reducers/offerReducer';
import { getAllRfqForConfirmedMaterials } from 'redux/reducers/RfqReducer';
import SelectComponent from 'pages/component/selectComponent';
import TableComponent from 'pages/component/table/table';
import Step2 from './step2';
import { useNavigate } from 'react-router';
import { emailPricesRequestsPageUrl } from 'utils/constant';
import { bulkEmailPriceRequestAction } from 'redux/reducers/centralizedReducer';
import { notification, showAlert } from 'utils/helper';
import Loader from 'components/Loader';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import ErrorMessage from 'pages/component/errorMessage';

const AddEmailPriceRequest = ({ handleCloseModal, handleCancel, isSingleRFQPage, rfqDetails, modalRef }) => {
  const [formValues, setFormValues] = useState({});
  const [step, setStep] = useState(1);
  const dispatch = useDispatch();
  const rfqData = useSelector((state) => state.rfq);
  const supplierData = useSelector((state) => state.offer);
  const emailRequestsData = useSelector((state) => state.requestedPrice);
  const navigate = useNavigate();

  useEffect(() => {
    if (rfqDetails) {
      setFormValues({
        ccEmail: rfqDetails?.Email,
        ...formValues,
        rfq: [rfqDetails?.RFQ_ID]
      });
    }
  }, [rfqDetails]);

  const formatOptions = (data, valueKey, labelKey) => {
    return isArray(data) ? data.map((item) => ({ ...item, label: item[labelKey], value: item[valueKey] })) : [];
  };

  const fields = [
    {
      type: 'select',
      label: 'Select RFQ',
      name: 'rfq',
      disabled: isSingleRFQPage,
      options: formatOptions(rfqData?.rfqForConfirmedMaterials?.data, 'RFQ_ID', 'RFQ_Name'),
      multiple: true
    },
    {
      type: 'select',
      label: 'Select Supplier',
      name: 'suppliers',
      multiple: true,
      options: formatOptions(supplierData?.supplierList || [], 'SupplierID', 'Name')
    }
  ];

  const handleSelectChange = (name, value) => {
    let supplierEmail = supplierData?.supplierList?.find((item) => item?.SupplierID === value)?.Email || 'N/A';

    setFormValues((prev) => ({
      ...prev,
      [name]: value,
      ...(name === 'suppliers' && { supplierEmail }),
      selectedMaterials:
        name === 'rfq'
          ? Object.keys(prev.selectedMaterials || {}).reduce((acc, rfqId) => {
              if (value.includes(Number(rfqId))) acc[rfqId] = prev.selectedMaterials[rfqId];
              return acc;
            }, {})
          : prev.selectedMaterials
    }));
  };

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    if (modalRef?.current) {
      modalRef.current.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [step]);

  const materialColumns = [
    { type: 'checkbox', name: 'selectedMaterial', title: 'Select', width: '100px' },
    { type: 'text', name: 'Material_ID', title: 'Material ID', width: '200px' },
    { type: 'text', name: 'Material_Description', title: 'Name', width: '200px' },
    { type: 'text', name: 'Part_Number', title: 'Part Number', width: '200px' },
    { type: 'text', name: 'brand', title: 'Brand', width: '200px' },
    { type: 'text', name: 'Quantity_Required', title: 'Quantity Required', width: '200px' },
    { type: 'text', name: 'Suggested_Suppliers', title: 'Suggested Suppliers (Email)', width: '200px' }
  ];

  const handleMaterialSelection = (rfqId, material) => {
    setFormValues((prev) => {
      const prevMaterials = prev.selectedMaterials || {};
      const rfqMaterials = prevMaterials[rfqId] || [];
      const allMaterials = isSingleRFQPage
        ? rfqDetails?.materials
        : rfqData?.rfqForConfirmedMaterials?.data?.find((rfq) => rfq.RFQ_ID === rfqId)?.materials || [];

      return {
        ...prev,
        selectedMaterials: {
          ...prevMaterials,
          [rfqId]:
            material === 'all'
              ? rfqMaterials.length === allMaterials.length
                ? []
                : allMaterials
              : rfqMaterials.some((item) => item.Material_ID === material.Material_ID)
                ? rfqMaterials.filter((item) => item.Material_ID !== material.Material_ID)
                : [...rfqMaterials, material]
        }
      };
    });
  };

  const renderMaterialTables = (rfqs) => {
    return rfqs?.map(
      (rfq) =>
        !isEmpty(rfq?.materials) && (
          <Paper
            key={rfq?.RFQ_ID}
            sx={{ p: 2, mb: 2, borderRadius: 2, boxShadow: '0 2px 5px rgba(0, 0, 0, 0.1), 0 -2px 5px rgba(0, 0, 0, 0.1)' }}
          >
            <Typography variant="h6" fontWeight={550} color="#26a93b" mb={1}>
              RFQ - {rfq?.RFQ_ID}
            </Typography>

            <TableComponent
              enablePagination
              headerValues={{ selectedMaterial: formValues?.selectedMaterials?.[rfq.RFQ_ID]?.length === rfq?.materials?.length }}
              columns={materialColumns}
              onChangeHeaderValues={() => handleMaterialSelection(rfq.RFQ_ID, 'all')}
              rows={rfq?.materials?.map((material) => ({
                ...material,
                selectedMaterial:
                  formValues?.selectedMaterials?.[rfq.RFQ_ID]?.some((item) => item?.Material_ID === material?.Material_ID) || false
              }))}
              title="Material Selection"
              onChangeRowValues={(value) => handleMaterialSelection(rfq.RFQ_ID, value)}
            />
          </Paper>
        )
    );
  };

  const renderSelectFields = () => (
    <Grid container spacing={2} maxWidth={'800px'}>
      {fields.map((field) =>
        field?.name === 'rfq' && isSingleRFQPage ? null : (
          <Grid item xs={12} md={6} key={field.name}>
            <Box>
              <Typography variant="subtitle1" color="secondary" mb={1}>
                {field.label}
              </Typography>
              <SelectComponent
                name={field.name}
                onSelectChange={(e) => {
                  if (formValues?.suppliers?.length >= 5) {
                    showAlert(dispatch, false, 'You can select maximum 5 suppliers', true);
                  } else {
                    handleSelectChange(field.name, e?.target?.value);
                  }
                }}
                value={formValues[field.name] || []}
                items={field.options}
                disabled={field.disabled}
                placeholder={field.label}
                multiple={field.multiple}
                style={{ color: isEmpty(formValues[field.name]) ? 'rgb(168, 166, 166)' : undefined }}
              />
              <ErrorMessage message={'Please Select Any Material To Proceed'} />
              {/* <Typography variant="p" mt={1} color='error'>
                Please Select Any Material To Proceed
              </Typography> */}
            </Box>
          </Grid>
        )
      )}
    </Grid>
  );

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleNext = async () => {
    if (step === 1) {
      let response = await dispatch(
        getSupplierEmailList({
          filters: [
            {
              heading: 'Email-Supplier',
              field: 's.Is_Email_Supplier',
              data: true,
              brands: rfqDetails?.materials?.map((material) => material?.brand)
            }
          ]
        })
      );

      if (isEmpty(response?.payload)) {
        dispatch(setSupplierListAction([]));
      }
      if (!isEmpty(formValues?.rfq)) {
        setStep(step + 1);
      } else {
        if (isEmpty(formValues?.rfq)) {
          showAlert(dispatch, false, 'Please select at least one RFQ', true);
        }
      }
    } else {
      if (!isEmpty(formValues?.suppliers)) {
        let selectedRFQ = Object.keys(formValues?.selectedMaterials || {})?.map((values) => {
          return {
            rfqID: values,
            materials: formValues?.selectedMaterials?.[values]?.map((material) => ({
              Material_Description: material?.Material_Description,
              partNumber: material?.Part_Number,
              brand: material?.brand,
              materialID: material?.Material_ID,
              quantity: material?.Quantity_Required
            }))
          };
        });
        let payload = {
          ccEmail: formValues?.ccEmail,
          suppliers: formValues?.suppliers || [],
          RFQs: isSingleRFQPage
            ? [
                {
                  rfqID: rfqDetails?.RFQ_ID,
                  materials: formValues?.selectedMaterials?.[rfqDetails?.RFQ_ID]?.map((material) => ({
                    Material_Description: material?.Material_Description,
                    partNumber: material?.Part_Number,
                    brand: material?.brand,
                    materialID: material?.Material_ID,
                    quantity: material?.Quantity_Required
                  }))
                }
              ]
            : selectedRFQ
        };
        let response = await dispatch(bulkEmailPriceRequestAction(payload));
        if (response?.payload?.success) {
          setFormValues({});
          setStep(1);
          if (isSingleRFQPage) {
            handleCloseModal();
          } else {
            navigate(emailPricesRequestsPageUrl);
          }
        }
      } else {
        if (isEmpty(formValues?.suppliers)) {
          showAlert(dispatch, false, 'Please select supplier', true);
        }
      }
    }
  };

  const isLoading = () => {
    return emailRequestsData?.loading || supplierData?.loading || rfqData?.loading;
  };

  return (
    <Box width="100%" display="flex" flexDirection="column" gap={3}>
      {isLoading() && <Loader />}
      <Box>
        <Box display={'flex'} gap={1}>
          {isSingleRFQPage && (
            <IconButton variant="contained" color="secondary" onClick={() => handleCancel()} className="email-request-icon-button">
              <ArrowBackIosIcon color="primary" className="icon" />
            </IconButton>
          )}
          <Typography variant="h5" color={'primary'} mb={1} mt={'3px'}>
            {isSingleRFQPage ? `RFQ - ${rfqDetails?.RFQ_Name}` : step === 2 ? 'Email Summary' : 'Email Price Request'}
          </Typography>
        </Box>
        {isSingleRFQPage && <Divider />}
      </Box>

      {step === 1 && (
        <>
          {!isEmpty(formValues?.rfq) && (
            <>
              <Typography variant="subtitle1" color="secondary" mt={2} mb={1}>
                RFQ & Material Selection
              </Typography>

              {renderMaterialTables(
                isSingleRFQPage
                  ? [rfqDetails]
                  : rfqData?.rfqForConfirmedMaterials?.data?.filter((rfq) => formValues?.rfq?.includes(rfq?.RFQ_ID))
              )}
            </>
          )}
        </>
      )}

      {step === 2 && <Step2 values={formValues} />}

      {!isEmpty(formValues?.rfq) && (
        <Box width="100%" display="flex" justifyContent={'space-between'} className="email-request-actions">
          {step === 2 && renderSelectFields()}
          <Box width="100%" display="flex" justifyContent="flex-end" mt={3} gap={2}>
            {step > 1 && (
              <Button variant="outlined" color="error" onClick={() => handleBack()} disabled={isLoading()}>
                Back
              </Button>
            )}
            <Button
              variant="contained"
              color="primary"
              onClick={() => handleNext()}
              disabled={isEmpty(formValues?.selectedMaterials) || isLoading()}
            >
              {step === 2 ? 'Submit' : 'Next'}
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default AddEmailPriceRequest;
