import { Button, FormControl, InputLabel, MenuItem, Select, Box } from '@mui/material';
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { assignRfqAction } from 'redux/reducers/RfqReducer';
import { getUserDetail } from 'utils/auth';
import { RESERVED } from 'utils/constant';
import { showAlert } from 'utils/helper';

const Assign = ({ currentPage, order, variant, selectOptions, loading, filters }) => {
  const [selectValue, setSelectedValue] = useState('');
  const [isAssigningRfq, setIsAssigningRfq] = useState(false);
  const dispatch = useDispatch();
  const assignRfq = () => {
    setIsAssigningRfq(true);
    const { RFQ_ID } = order;
    if (selectValue) {
      dispatch(
        assignRfqAction({
          rfqId: RFQ_ID?.toString(),
          status: RESERVED,
          userId: selectValue?.toString(),
          currentPage: currentPage,
          filters
        })
      );
      setIsAssigningRfq(false);
    } else {
      showAlert(dispatch, false, 'Kam is required', true);
    }
  };
  const handleSelect = (e) => {
    setSelectedValue(e.target.value);
  };
  const checkAssigned = () => {
    setIsAssigningRfq(true);
  };
  return (
    <>
      <Box className="assign-wrapper" display="flex" alignItems="flex-end">
        {isAssigningRfq && (
          <FormControl sx={{ mr: 1 }} size="small">
            <InputLabel id="kam-label">Select Kam</InputLabel>
            <Select
              labelId="kam-label"
              id="kam"
              defaultValue=""
              label="Kam"
              sx={{ minWidth: '150px' }}
              onChange={handleSelect}
              name="kam"
              value={selectValue || ''}
            >
              {selectOptions?.map((item) => (
                <MenuItem key={item.value} value={item.value}>
                  {item.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}
        <Button
          variant={variant}
          size="small"
          className="material-request-btn small-btn"
          onClick={() => (!isAssigningRfq ? checkAssigned() : assignRfq())}
          sx={{ alignSelf: 'flex-end' }}
          disabled={loading}
        >
          Assign
        </Button>
      </Box>
    </>
  );
};

export default Assign;
