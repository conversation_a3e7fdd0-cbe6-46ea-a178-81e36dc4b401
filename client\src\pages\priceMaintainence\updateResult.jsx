import React from 'react';
import { Card, CardContent, Typography, Grid } from '@mui/material';
import TableComponent from 'pages/component/table/table';

const BatchDetailsCard = ({ details }) => {
  // Convert estimated time in seconds to hours and minutes
  const estimatedTimeInSeconds = details?.estimatedTimeInSeconds || 0;
  const estimatedTimeInHours = Math.floor(estimatedTimeInSeconds / 3600); // Get whole hours
  const remainingMinutes = Math.floor((estimatedTimeInSeconds % 3600) / 60); // Get remaining minutes after hours
  const remainingSeconds = estimatedTimeInSeconds % 60; // Get remaining seconds for times under a minute

  // Create an array of label-value pairs
  const detailItems = [
    { label: 'Total Entries', value: details?.totalEntries?.length > 0 ? details.totalEntries : 'None' },
    { label: 'Error Entries', value: details?.errorEntries?.length > 0 ? details?.errorEntries?.length : 'None' }
  ];

  // Table headers for error entries
  const headers = [
    {
      name: 'entryNo',
      type: 'text',
      title: 'Entry No',
      sortingactive: true,
      minWidth: '170px'
    },
    {
      name: 'message',
      type: 'text',
      title: 'Message',
      sortingactive: true
    }
  ];

  return (
    <Card variant="outlined" sx={{ maxWidth: 600, margin: 'auto', padding: 2 }}>
      <CardContent>
        <Typography variant="h5" gutterBottom color="secondary">
          File Uploading Details
        </Typography>
        <Grid container spacing={2}>
          {detailItems?.map((item, index) => (
            <React.Fragment key={index}>
              <Grid item xs={6}>
                <Typography variant="caption" color="secondary">
                  {item.label}:
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body1">{item.value}</Typography>
              </Grid>
            </React.Fragment>
          ))}
        </Grid>

        {details?.estimatedTimeInSeconds ? (
          <Typography variant="body1" gutterBottom sx={{ marginTop: 2 }}>
            This will take approximately{' '}
            {estimatedTimeInHours > 0
              ? `${estimatedTimeInHours} hr${estimatedTimeInHours > 1 ? 's' : ''} ${remainingMinutes} min`
              : remainingMinutes > 0
                ? `${remainingMinutes} min`
                : `${remainingSeconds} sec`}{' '}
            to complete.
          </Typography>
        ) : null}

        {details?.errorEntries?.length > 0 && (
          <>
            <TableComponent
              columns={headers}
              maxHeight="100%"
              rows={details.errorEntries}
              tableHeading="Error Entries"
              enablePagination={true}
              defaultPage={5}
              actionBtns={[]}
              placeActionButtonsIn="header"
            />
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default BatchDetailsCard;
