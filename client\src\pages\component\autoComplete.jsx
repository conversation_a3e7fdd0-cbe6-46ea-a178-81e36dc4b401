import React from 'react';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import CircularProgress from '@mui/material/CircularProgress';

function DynamicAutocomplete({
  isNotRemoveButton,
  options,
  label,
  placeholder,
  padding,
  isLoading,
  onChange,
  value,
  getOptionLabel,
  margin
}) {
  // Set the value to null if it's undefined
  const selectedValue = value === undefined ? null : value;

  return (
    <Autocomplete
      options={options}
      value={selectedValue} // Use selectedValue to handle undefined case
      onChange={onChange}
      loading={isLoading}
      disableClearable={isNotRemoveButton}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          placeholder={placeholder}
          variant="outlined"
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {isLoading ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            ),
            sx: {
              borderRadius: '8px'
            }
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: '8px',
              margin: margin,
              padding: padding,
              '& .MuiAutocomplete-input': {
                width: '100%',
                height: '7px'
              }
            }
          }}
        />
      )}
      renderOption={(props, option) => <li {...props}>{getOptionLabel(option)}</li>}
    />
  );
}

export default DynamicAutocomplete;
