const jwt = require("jsonwebtoken");
const {
  isEmpty,
  generateID,
  getCurrentDateTime,
} = require("../utils/misc.util");
const { apiError, apiResponse, apiHandler } = require("../utils/api.util");
const {
  LOGIN,
  INVALID,
  NOT_FOUND,
  FETCH,
  STATUS_SUCCESS,
  ADD_SUCCESS,
  EXISTS,
  UNAUTHORIZED,
  FORBIDDEN,
  UPDATE_SUCCESS,
  NOT_MATCH,
  SENT_SUCCESS,
  DELETE_SUCCESS,
} = require("../utils/message.util");
const { JWT_SECRET, BASE_URL } = require("../constants");
const { mainDataset } = require("../db");
const { sendMail } = require("../utils/email.util");

const getKamUsers = apiHandler(async (req, res) => {
  const [kamUsers] = await mainDataset.query({
    query: `
      SELECT UserID, FirstName, LastName
      FROM Users
      WHERE Role = @role
    `,
    params: {
      role: "KAM",
    },
  });
  if (isEmpty(kamUsers)) {
    return apiError(NOT_FOUND, "Users", null, res);
  }

  return apiResponse(FETCH, "KAM Users", kamUsers, res);
});

const loginUser = apiHandler(async (req, res) => {
  const { email, password } = req.body;

  const [rows] = await mainDataset.query({
    query: `SELECT * FROM Users WHERE Email = @email`,
    params: { email },
  });
  const existingUser = rows[0];
  if (isEmpty(existingUser) || existingUser.Password !== password) {
    return apiError(INVALID, "Credentials", null, res);
  }

  const tokenData = {
    userId: existingUser.UserID,
    firstName: existingUser.FirstName,
    lastName: existingUser.LastName,
    email,
    role: existingUser.Role,
    isProductManager: existingUser.IsProductManager,
  };
  const token = jwt.sign(tokenData, JWT_SECRET, { expiresIn: "1d" });
  const data = {
    ...tokenData,
    token,
  };
  return apiResponse(LOGIN, null, data, res);
});

const fetchAllNotifications = apiHandler(async (req, res) => {
  let { UserID, page, limit } = req.body;

  limit = limit || 10;
  let offset = ((page || 1) - 1) * (limit || 10);

  const [AllNotifications] = await mainDataset.query({
    query: `
      SELECT *
      FROM notifications
      WHERE userID = @UserID
    `,
    params: {
      UserID,
    },
  });
  if (isEmpty(AllNotifications)) {
    return apiError(NOT_FOUND, "Notifications", null, res);
  }

  let messages = JSON.parse(AllNotifications[0].messages);
  let count = messages.length;

  let sortedMessages = messages.sort((a, b) => {
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    return dateB - dateA;
  });

  let selectedMessages = sortedMessages.splice(offset, limit);

  let notifications = {
    UserID: AllNotifications[0].userID,
    read: AllNotifications[0].read,
    count: count,
    messages: selectedMessages,
  };

  return apiResponse(FETCH, "Notifications", notifications, res);
});

const readNotifications = apiHandler(async (req, res) => {
  const { UserID } = req.body;

  const [AllNotifications] = await mainDataset.query({
    query: `
      SELECT *
      FROM notifications
      WHERE userID = @UserID
    `,
    params: {
      UserID,
    },
  });

  if (isEmpty(AllNotifications)) {
    return apiError(NOT_FOUND, "Notifications", null, res);
  }

  let messages = JSON.parse(AllNotifications[0].messages);

  let updatedMessages = [];
  for (const notification of messages) {
    notification.read = true;

    updatedMessages.push(notification);
  }

  const [readNotifications] = await mainDataset.query({
    query: `
      UPDATE notifications SET
      messages = @messages, read = @read
      WHERE userID = @UserID
    `,
    params: {
      UserID,
      read: true,
      messages: JSON.stringify(updatedMessages),
    },
  });

  return apiResponse(STATUS_SUCCESS, "Notifications", null, res);
});

const getAllUsers = apiHandler(async (req, res) => {
  if (req.user.role.trim().toUpperCase() !== "SUPERVISOR") {
    return apiError(FORBIDDEN, "User", null, res);
  }

  let [existingUsers] = await mainDataset.query({
    query: `
      SELECT UserID, FirstName, LastName, Email, Role, Date, Postal_Code, cn.Country, s.State,
      FROM Users AS u
      LEFT JOIN Countries AS cn ON cn.CountryId = u.Country
      LEFT JOIN States AS s ON s.StateId = u.Country
      WHERE isSystemReserved IS NULL OR isSystemReserved = false
      ORDER BY FirstName
    `,
  });

  if (isEmpty(existingUsers)) {
    return apiError(NOT_FOUND, "Users", null, res);
  }

  existingUsers = existingUsers.sort(
    (a, b) => new Date(b.Date) - new Date(a.Date)
  );

  return apiResponse(FETCH, "Users", existingUsers, res);
});

const addUser = apiHandler(async (req, res) => {
  const { FirstName, LastName, Role, Email, Password, Postal_Code="", Country, State  } = req.body;

  if (req.user.role.trim().toUpperCase() !== "SUPERVISOR") {
    return apiError(FORBIDDEN, "User", null, res);
  }

  const [existingUser] = await mainDataset.query({
    query: `
      SELECT * FROM Users
      WHERE UPPER(TRIM(Email)) = UPPER(TRIM(@Email))
    `,
    params: {
      Email,
    },
  });

  if (!isEmpty(existingUser[0])) {
    return apiError(EXISTS, "Email", null, res);
  }

  const UserID = generateID();

  const [addUser] = await mainDataset.query({
    query: `
      INSERT INTO Users
      (FirstName, LastName, Role, Email, Password, Date, UserID, Postal_Code, Country, State)
      VALUES
      (@FirstName, @LastName, @Role, @Email, @Password, @Date, @UserID, @Postal_Code, @Country, @State)
    `,
    params: {
      FirstName,
      LastName,
      Role,
      Email,
      Password,
      Date: getCurrentDateTime(),
      UserID,
      Postal_Code,
      Country, 
      State
    },
  });

  return apiResponse(ADD_SUCCESS, "User", null, res);
});

const getSingleUser = apiHandler(async (req, res) => {
  const UserID = req.params.ID;

  const [existingUser] = await mainDataset.query({
    query: `
      SELECT UserID, FirstName, LastName, Email, Role, Date, Postal_Code, u.Country AS CountryId, u.State As StateId,  cn.Country, s.State, FROM Users AS u
      LEFT JOIN Countries AS cn ON cn.CountryId = u.Country
      LEFT JOIN States AS s ON s.StateId = u.Country
      WHERE UserID = @UserID
    `,
    params: {
      UserID: parseInt(UserID),
    },
  });

  if (isEmpty(existingUser[0])) {
    return apiError(NOT_FOUND, "User", null, res);
  }

  return apiResponse(FETCH, "User", existingUser[0], res);
});

const editUser = apiHandler(async (req, res) => {
  const { FirstName, LastName, Role, Email, UserID, Postal_Code="", Country, State } = req.body;

  const [existingUser] = await mainDataset.query({
    query: `
      SELECT * FROM Users
      WHERE UPPER(TRIM(Email)) = UPPER(TRIM(@Email))  AND UserID != @UserID
    `,
    params: {
      Email,
      UserID: parseInt(UserID),
    },
  });

  if (!isEmpty(existingUser[0])) {
    return apiError(EXISTS, "Email", null, res);
  }

  const [updateUser] = await mainDataset.query({
    query: `UPDATE Users SET
      FirstName = @FirstName,
      LastName = @LastName,
      Role = @Role,
      Email = @Email,
      Date = @Date,
      Postal_Code = @Postal_Code,
      Country = @Country,
      State = @State
      WHERE UserID = @UserID

    `,
    params: {
      UserID: parseInt(UserID),
      FirstName,
      LastName,
      Role: isEmpty(Role) ? "KAM" : Role,
      Email,
      Date: getCurrentDateTime(),
      Postal_Code,
      Country,
      State
    },
  });

  return apiResponse(UPDATE_SUCCESS, "User", null, res);
});

const updatePassword = apiHandler(async (req, res) => {
  const { oldPassword, newPassword } = req.body;

  const UserID = req.user.userId;

  const [existingUser] = await mainDataset.query({
    query: `
      SELECT * FROM Users
      WHERE UserID = @UserID AND Password = @oldPassword
    `,
    params: {
      UserID: parseInt(UserID),
      oldPassword,
    },
  });

  if (isEmpty(existingUser[0])) {
    return apiError(INVALID, "Old Password", null, res);
  }

  const [updatePassword] = await mainDataset.query({
    query: `
      UPDATE Users SET
      Password = @newPassword
      WHERE UserID = @UserID AND Password = @oldPassword
    `,
    params: {
      UserID: parseInt(UserID),
      oldPassword,
      newPassword,
    },
  });

  return apiResponse(UPDATE_SUCCESS, "Password", null, res);
});

const sendPasswordLink = apiHandler(async (req, res) => {
  const { Email } = req.body;

  const [existingUser] = await mainDataset.query({
    query: `
      SELECT * FROM Users
      WHERE UPPER(TRIM(Email)) = UPPER(TRIM(@Email))
    `,
    params: {
      Email,
    },
  });

  if (isEmpty(existingUser[0])) {
    return apiError(NOT_FOUND, "Email", null, res);
  }

  const name = existingUser[0]?.FirstName;
  const email = existingUser[0]?.Email?.toString().trim();
  const ID = existingUser[0]?.UserID?.toString().trim();

  const tokenData = {
    ID,
    name,
    email,
  };

  const token = jwt.sign(tokenData, JWT_SECRET, { expiresIn: "20m" });
  const link = `${BASE_URL}/change-password/${token.toString()}`;

  let html = ``;

  let option = {
    to: email,
    subject: "Update Password",
    html: html,
  };

  try {
    await sendMail(option);
  } catch (error) {
    console.log("Error Occured While Sending Mail - ", error);
  }

  const [updateToken] = await mainDataset.query({
    query: `
      UPDATE Users SET
      token = @token
      WHERE UPPER(TRIM(Email)) = UPPER(TRIM(@Email))
    `,
    params: {
      Email,
      token: token.toString(),
    },
  });

  return apiResponse(SENT_SUCCESS, "Update Password Link", null, res);
});

const recoverPassword = apiHandler(async (req, res) => {
  const { newPassword, token } = req.body;

  const [existingUser] = await mainDataset.query({
    query: `
      SELECT * FROM Users
      WHERE token = @token
    `,
    params: {
      token,
    },
  });

  if (isEmpty(existingUser[0])) {
    return apiError(INVALID, "Token", null, res);
  }

  const decodedToken = jwt.verify(token, JWT_SECRET);

  const { ID } = decodedToken;

  const [updatePassword] = await mainDataset.query({
    query: `UPDATE Users SET
    Password = @newPassword,
    token = null
    WHERE UserID = @ID
    `,
    params: {
      ID: parseInt(ID),
      newPassword,
    },
  });

  return apiResponse(UPDATE_SUCCESS, "Password", null, res);
});

const deleteUser = apiHandler(async (req, res) => {
  const UserID = req.params.ID;

  const [deleteUser] = await mainDataset.query({
    query: `
      DELETE FROM Users
      WHERE UserID = @UserID
    `,
    params: {
      UserID: parseInt(UserID),
    },
  });

  return apiResponse(DELETE_SUCCESS, "User", null, res);
});

module.exports = {
  getKamUsers,
  loginUser,
  fetchAllNotifications,
  readNotifications,
  getAllUsers,
  addUser,
  getSingleUser,
  editUser,
  updatePassword,
  sendPasswordLink,
  recoverPassword,
  deleteUser,
};
