import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import TableComponent from "pages/component/table/table";
import { getAllCentralizedRequestAction, usePriceQuoteRequest } from "redux/reducers/centralizedReducer";
import PriceTableHeader from "../header/priceTableHeader";
import Loader from "components/Loader";
import { priceTablePageUrl } from "utils/constant";
import AlertDialog from "pages/component/dialogbox";
import UseQuote from "./UseQuote";
import { filterFunction } from "pages/component/table/tableSearch";
import { defaultFilterPayload } from "../constant";
import TableApiPaginationComponent from "pages/component/table/tableApiPagination";
import { validate, validateNested } from "pages/component/validation";
import '../centralized.css'
const PriceTable = () => {
  const { supplierIds } = useParams();
  const dispatch = useDispatch();
  const centralizedQuoting = useSelector((state) => state.requestedPrice || []);
  const [requestedPriceData, setRequestedPriceData] = useState([]);
  const [useQuoteInfo, setUseQuoteInfo] = useState({});
  const [open, setOpen] = useState(false);
  const [searchVaue, setSearchValue] = useState('');
  const [filteredData, setFilteredData] = useState([]);
  const [filters, setFilters] = useState([]);
  const hasSelectedBrands = requestedPriceData?.some((brand) => brand?.select);
  const profileData = useSelector((state) => state.profile.data);
  const [page, setPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [errors, setErrors] = useState(0)
  const [limit, setLimit] = useState(10)

  useEffect(() => {
    const payload = {
      limit: limit || 10,
      page: page || 1,
      filterData: filters && filters?.length > 0 ? filters : [{
        heading: "IDs",
        data: supplierIds || []
      }]
    }

    dispatch(getAllCentralizedRequestAction(payload));
  }, [supplierIds, page, limit, filters])

  useEffect(() => {
    const { filteredCentralizedRequests, count } = centralizedQuoting?.data || {}
    let filteredData = searchVaue ? filterFunction({ search: searchVaue }, filteredCentralizedRequests) : filteredCentralizedRequests
    setRequestedPriceData(filteredData || [])
    setTotalCount(searchVaue ? filteredData?.length : count || 0)

  }, [centralizedQuoting?.data?.filteredCentralizedRequests]);

  const getBrandSuppliers = (brandName) => {
    return requestedPriceData
      ?.filter((brand) => brand.Brand === brandName && (hasSelectedBrands ? brand?.select : true))
      ?.map((brand) => ({
        brand: brand?.Brand,
        partNumber: brand?.PartNumber,
        quantity: brand?.Material_Information?.quantity,
        rfq_id: brand?.RFQ_ID,
        material_id: brand?.Material_ID,
        suppliers: brand?.filteredExistingRequests?.map((request) => ({
          supplierName: request?.supplierName,
          price: request?.unitPrice,
          totalPrice: request?.totalPrice,
          currency: request?.currency,
          recommended: request?.recommended,
          isRecommended: request?.recommended ? 'success' : 'primary',
          recomendedTitle: request?.recommended ? 'Used' : 'Use',
          isPriceAvailable: !request?.unitPrice,
          supplierID: request?.supplierID,
          date: request?.respondDate || '',
          unitPrice: request?.unitPrice
        })),
      }));
  };

  const getTableRows = () => {
    const processedBrands = new Set();
    return requestedPriceData
      ?.filter((brand) => (hasSelectedBrands ? brand?.select : true))
      ?.flatMap((brand) => {
        if (!processedBrands.has(brand.Brand)) {
          processedBrands.add(brand.Brand);
          return getBrandSuppliers(brand.Brand) || [];
        }
        return [];
      });
  };

  const handleOpen = () => {
    setOpen(true);
  }

  const handleClose = () => {
    setOpen(false);
  }

  const isLoading = () => centralizedQuoting?.loading || centralizedQuoting?.status === 'loading';

  const headers = [
    { name: 'brand', type: 'text', title: 'Brand', sortingactive: true },
    { name: 'partNumber', type: 'text', title: 'Part Number', sortingactive: true },
    { name: 'quantity', type: 'text', title: 'Qty', sortingactive: true },
    { name: 'supplierName', keyName: 'suppliers', type: 'array', title: 'Suppliers', sortingactive: false },
    { name: 'currency', keyName: 'suppliers', type: 'array', title: 'Currency', sortingactive: false },
    { name: 'price', keyName: 'suppliers', type: 'array', title: 'Price', sortingactive: false },
    { name: 'date', keyName: 'suppliers', type: 'array', arrayType: 'date', title: 'Date', sortingactive: false },
    {
      arrayType: 'actions', keyName: 'suppliers', type: 'array', title: 'Actions', sortingactive: false,
      actionBtns: [{
        dynamicDisableBtn: 'isPriceAvailable',
        disabled: isLoading(),
        title: 'recomendedTitle',
        isDynamicBtnColor: 'isRecommended',
        onClick: async (row, data) => {
          if (!data.recommended) {

            setUseQuoteInfo({
              supplierID: data.supplierID,
              partNumber: row.partNumber,
              rfqId: row.rfq_id,
              materialId: row.material_id,
              unitPrice: Number(data.unitPrice),
              currency: data.currency,
              weight: null,
              quantity: null,
              leadTime: null,
              isTax: false,
              UserID: profileData.userId.toString(),
              firstName: profileData.firstName,
              lastName: profileData.lastName

            })
            handleOpen();
          } else {

          }
        },
      }],
    },
  ];

  const handleConfirm = async () => {
    const rules = {
      weight: { required: true, label: 'Weight', isNumber: true },
      quantity: { required: true, label: 'Quantity', isNumber: true},
      leadTime: { required: true, label: 'Lead Time',isNumber: true },
    };
    const validationErrors = validate(useQuoteInfo, rules);
    setErrors(validationErrors);

    if (Object?.keys(validationErrors)?.length === 0) {
      const response = await dispatch(usePriceQuoteRequest({
        ...useQuoteInfo,
        weight: Number(useQuoteInfo?.weight)
      }));
      const { success } = response?.payload || {}
      if (success) {
        const payload = {
          limit: limit || 10,
          page: page || 1,
          filterData: [{
            heading: "IDs",
            data: supplierIds || []
          }]
        }
        handleClose()
        dispatch(getAllCentralizedRequestAction(payload))

      }
    }
  }

  const handleSearch = (filteredData, search) => {

    const payload = [
      {
        heading: "search",
        data: search
      }, {
        heading: "IDs",
        data: supplierIds || []
      }]
    setFilters(payload)
    setSearchValue(search || '');
    setPage(1)
  };

  useEffect(() => {
    if (requestedPriceData) {
      setFilteredData(getTableRows())
    }
  }, [requestedPriceData])

  const onPageChange = (page) => {
    setPage(page + 1)

  }
  const onPageLimitChange = (limit) => {
    setLimit(limit)

  }
  const { filteredCentralizedRequests } = centralizedQuoting?.data || {}
  return (
    <>
      {isLoading() && <Loader />}
      <TableApiPaginationComponent
        columns={headers}
        rows={filteredData}
        title="Price Table"
        pageNo={page}
        titleLink="dashboard"
        enablePagination={true}
        enableSearch={true}
        totalCount={totalCount}
        allRows={filteredCentralizedRequests || []}
        isBackendPagination={true}
        onPageChange={(page) => onPageChange(page)}
        onPageLimitChange={(page) => onPageLimitChange(page)}
        handleSearch={(filteredData, search) => handleSearch(filteredData, search)}
      />
      <AlertDialog
        confirm={handleConfirm}
        cancel={handleClose}
        showCard={true}
        borderRadius="20px"
        showCancelBtn={true}
        showConfirmBtn={true}
        disableConfirmBtn={isLoading()}
        disableCancelBtn={isLoading()}
        Component={
          <UseQuote
            quoteInfoState={[useQuoteInfo, setUseQuoteInfo, errors]}
          />
        }
        open={open}
      />
    </>
  );
};

export default PriceTable;
