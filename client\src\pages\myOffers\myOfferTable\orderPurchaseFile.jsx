import React, { useState } from 'react';
import PropTypes from 'prop-types';
import InputField from 'pages/component/inputField';
import FileUploadInput from 'pages/component/uploadFileInput';
import { Typography } from '@mui/material';
import ErrorMessage from 'pages/component/errorMessage';

const OrderPurchaseFile = ({ orderInfo }) => {
  const [orderFile, setOrderFile, errors, setFileName, fileName] = orderInfo || {};

  const handleInputChange = (name, value) => {
    setOrderFile((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const inputFields = [
    { type: 'text', name: 'OC_Number', placeholder: 'Order Number' },
    { type: 'file', name: 'OC_File', placeholder: 'Order Purchase File' },
    { type: 'number', name: 'Amount_CLP', placeholder: 'Amount CLP' },
    { type: 'number', name: 'Amount_USD', placeholder: 'Amount USD' }
  ];

  const renderInputField = ({ type, name, placeholder, label, checked }) => {
    const value = orderFile[name] || (type === 'number' && orderFile[name] === 0 ? 0 : '');
    switch (type) {
      case 'number':
        return (
          <InputField
            type="number"
            name={name}
            placeholder={placeholder}
            errors={errors}
            value={value}
            onChange={(e) => handleInputChange(name, name === 'qty' ? parseInt(e.target.value) : Number(e.target.value))}
            fullWidth
          />
        ); 
      case 'text':
        return (
          <InputField
            type="text"
            name={name}
            placeholder={placeholder}
            errors={errors}
            value={value}
            onChange={(e) => handleInputChange(name, e.target.value)}
            fullWidth
          />
        );
      case 'file':
        return (
          <>
            <FileUploadInput
              onlyShowInput={true}
              label="Upload Documents"
              fileName={fileName}
              onFileSelect={(files, fileName) => {
                setFileName(fileName);
                handleInputChange(name, files, fileName);
              }}
              error={errors[name]}
            />

            {errors[name] && <ErrorMessage message={errors[name]} />}
          </>
        );
      default:
        return null;
    }
  };

  return (
    <div className="use_quote_popup">
      <Typography variant="h5" color="secondary" mb={2} mt={1} sx={{ textAlign: 'center' }}>
        Order Purchase
      </Typography>
      {inputFields.map((field, index) => (
        <div key={index} classNam="order-purchase-inner-wrapper">
          <Typography variant="h6" color="secondary" mt={3}>
            {field?.placeholder}
          </Typography>
          {renderInputField(field)}
        </div>
      ))}
    </div>
  );
};

OrderPurchaseFile.propTypes = {
  orderInfo: PropTypes.shape({
    orderFile: PropTypes.object.isRequired,
    setOrderFile: PropTypes.func.isRequired,
    errors: PropTypes.object
  })
};

OrderPurchaseFile.defaultProps = {
  orderInfo: {
    orderFile: {},
    setOrderFile: () => {},
    errors: {}
  }
};

export default OrderPurchaseFile;
