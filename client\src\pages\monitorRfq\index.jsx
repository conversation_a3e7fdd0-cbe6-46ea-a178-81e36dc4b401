import { get } from "lodash"
import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { getAllSuppliersRequestedQuotes, inviteExpiredSupplier } from "redux/reducers/supplierPortalReducer"
import MaterialRequestedCard from "./materialRquestedCard"
import TableComponent from "pages/component/table/table"
import ActionButton from "pages/component/actionButton"
import { useNavigate } from "react-router"
import { singleRequestedQuotePageUrl } from "utils/constant"
import './style.css'
import Loader from "components/Loader"
import { EXPIREDQUOTE, REQUESTEDQUOTE, RESPONDEDDQUOTE, tabsData } from "./constant"
import { getItemFromLocalStorage } from "utils/helper"

const MonitorQuotes = () => {
  const requestedQuoteList = useSelector((state) => state.requestedQuote);
  const [requestedQuotes, setRequestedQuote] = useState([]);
  const [filteredRequestedQuotes, setFilteredRequestedQuote] = useState([]);
  const [allHeaders, setAllHeaders] = useState([])
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const isProductManager = getItemFromLocalStorage('isProductManager');

  const isLoading = () => {
    const { loading, status } = requestedQuoteList || {};
    return status === 'loading' || loading;
  };
  useEffect(() => {
    const data = get(requestedQuoteList, 'allSupplierQuotes', [])?.map((supplier) => ({
      ...supplier,
      isRequested: supplier?.status !== RESPONDEDDQUOTE,
      requestDate: supplier?.requestDate?.value
    }))
    setRequestedQuote(data || [])
    setFilteredRequestedQuote(data || [])

  }, [get(requestedQuoteList, 'allSupplierQuotes')])

  const headers = [
    {
      name: 'supplierID',
      type: 'text',
      title: 'Supplier ID',
      sortingactive: true
    },
    {
      name: 'requestDate',
      type: 'date',
      title: 'Request Date',
      sortingactive: true,
    },
    {
      name: 'supplierName',
      type: 'text',
      title: 'Supplier Name',
      sortingactive: true,
      minWidth: '170px'
    },
    {
      name: 'supplierEmail',
      type: 'text',
      title: 'Supplier Email',
      defaultValue: '0',
      sortingactive: true,
      minWidth: '170px'
    },
    {
      name: 'status',
      type: 'badge',
      title: 'Status',
      defaultValue: '0',
      sortingactive: true,
      className: 'status-badge'
    },
    {
      name: 'actions',
      btnType: 'view',
      type: 'actions',
      title: 'Action',
      sortingactive: false,
      component: ActionButton,
      disableBtn: isLoading() || false,
      buttonOnClick: (type, id, materialId, index, data) => {
        if (type === 'view') {
          navigate(`${singleRequestedQuotePageUrl}/${data?.ID}`)
        }
      }
    },
    {
      name: 'actions',
      btnType: 'update',
      type: 'actions',
      buttonTitle: 'Send Now',
      title: 'Mail',
      minWidth: '170px',
      showButton: 'isRequested',
      sortingactive: false,
      component: ActionButton,
      disableBtn: isLoading() || false,
      buttonOnClick: (type, id, materialId, index, data) => {
        dispatch(inviteExpiredSupplier({ supplierId: data?.supplierID }))
      },
    },
  ];

  useEffect(() => {
    if (isProductManager){
      setAllHeaders([...headers, {
        name: 'actions',
        btnType: 'logPrice',
        type: 'actions',
        minWidth: '170px',
        title: 'Log Prices',
        sortingactive: false,
        component: ActionButton,
        disableBtn: isLoading() || false,
        buttonOnClick: (type, id, materialId, index, data) => {
            window.open(data.link);
        }
      }])
    } else {
    setAllHeaders([...headers])
    }
  }, [])

  useEffect(() => {
    dispatch(getAllSuppliersRequestedQuotes())
  }, [])

  const handleSearch = (filteredData) => {
    setFilteredRequestedQuote(filteredData || [])
  }


  return (
    <>
      {isLoading() && <Loader />}
      <TableComponent
        columns={allHeaders}
        enableSearch={true}
        allRows={requestedQuotes}
        handleSearch={handleSearch}
        isBadgeFilter={true}
        badgeFilterData={tabsData || []}
        maxHeight="100%"
        rows={filteredRequestedQuotes || []}
        enablePagination={true}
      />
    </>)
}
export default MonitorQuotes