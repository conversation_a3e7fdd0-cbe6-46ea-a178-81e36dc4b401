import { marginValidationMessage } from 'utils/validationMessage';

const isValidString = (value) => typeof value === 'string' && value.trim() !== '';
const isValidNumber = (value) => typeof value === 'number' && value >= 0;

export const validateOffer = (offer) => {
  if (!isValidString(offer.rfqId)) {
    return { isValid: false, message: 'RFQ ID is required.' };
  }
  if (!isValidString(offer.offerCurrency)) {
    return { isValid: false, message: 'Offer currency is required.' };
  }
  if (!isValidString(offer?.clientId)) {
    return { isValid: false, message: 'Client is required.' };
  }
  if (!isValidNumber(offer.validFor)) {
    return { isValid: false, message: 'Valid For must be a non-negative number.' };
  }
  if (!isValidString(offer.paymentTerms)) {
    return { isValid: false, message: 'Payment terms are required.' };
  }
  if (!isValidString(offer.notes)) {
    return { isValid: false, message: 'Notes are required.' };
  }
  if (!offer.materials || !Array.isArray(offer.materials) || offer.materials.length === 0) {
    return { isValid: false, message: 'At least one material is required.' };
  }

  for (let material of offer.materials) {
    if (!isValidString(material.materialId)) {
      return { isValid: false, message: 'Material ID is required.' };
    }

    const { supplier } = material;
    if (!supplier) {
      return { isValid: false, message: 'Supplier data is required.' };
    }
    if (!supplier?.margin) {
      return { isValid: false, message: 'Margin is required.' };
    }
    if (supplier?.margin <= 0) {
      return { isValid: false, message: marginValidationMessage };
    }
  }

  return { isValid: true, message: '' };
};
