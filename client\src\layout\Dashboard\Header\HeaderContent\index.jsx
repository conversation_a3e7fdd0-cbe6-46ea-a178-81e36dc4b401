import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';
import Profile from './Profile';
import MobileSection from './MobileSection';
import '../header.css';
import Notification from './Notification';

// ==============================|| HEADER - CONTENT ||============================== //

export default function HeaderContent() {
  const downLG = useMediaQuery((theme) => theme.breakpoints.down('lg'));
  return (
    <>
      <Box className="app-bar">
        <Notification />
        {!downLG && <Profile />}
        {downLG && <MobileSection />}
      </Box>
    </>
  );
}
