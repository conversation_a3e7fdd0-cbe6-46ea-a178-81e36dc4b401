import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { getRequest, post } from 'utils/axios';
import {
  addClientApiUrl,
  clientPageUrl,
  deleteClientApiUrl,
  fetchShippingApiUrl,
  singleClientApiUrl,
  updateClientApiUrl,
  updateClientApprovalApiUrl
} from 'utils/constant';
import { notification, showAlert } from 'utils/helper';
import { getClientList, removeClientFromList } from './offerReducer';

export const getSingleClient = createAsyncThunk('clientMaintenanceSlice/fetch', async (clientId, { rejectWithValue, dispatch }) => {
  try {
    dispatch(clientMaintenanceSlice.actions.setLoading(true));
    const response = await post(singleClientApiUrl(clientId), {}, true, dispatch);
    const { error, success, message, data } = get(response, 'data', {});
    if (response) {
      dispatch(clientMaintenanceSlice.actions.setLoading(false));
      if (success) {
        dispatch(clientMaintenanceSlice.actions.setSingleClientData(data || []));
        return data || [];
      } else {
        dispatch(notification(false, message, true));
        return rejectWithValue(message || 'Failed to fetch RFQ data');
      }
    } else {
      dispatch(clientMaintenanceSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(clientMaintenanceSlice.actions.setLoading(false));
    return rejectWithValue('An error occurred while fetching RFQ data');
  }
});
export const getShipping = createAsyncThunk('clientMaintenanceSlice/fetch', async (clientId, { rejectWithValue, dispatch }) => {
  try {
    const response = await getRequest(fetchShippingApiUrl, {}, true, dispatch);
    const { error, success, message, data } = get(response, 'data', {});

    if (success) {
      dispatch(clientMaintenanceSlice.actions.setShippingData(data || []));
      return data || [];
    } else {
      dispatch(notification(false, message, true));
      return rejectWithValue(message || 'Failed to fetch RFQ data');
    }
  } catch (error) {
    return rejectWithValue('An error occurred while fetching RFQ data');
  }
});

export const updateClientMaintenanace = createAsyncThunk(updateClientApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  const { navigate, ...rest } = data || {};
  const payload = { ...rest };
  const { setLoading, setSingleClientData } = clientMaintenanceSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await post(updateClientApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        dispatch(setSingleClientData({}));
        navigate(clientPageUrl);
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});
export const addClientMaintenanace = createAsyncThunk(addClientApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  const { navigate, ...rest } = data || {};
  const payload = { ...rest };
  const { setLoading, setSingleClientData } = clientMaintenanceSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await post(addClientApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        navigate(clientPageUrl);
        dispatch(setSingleClientData({}));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

export const updateClientApprovalStatus = createAsyncThunk(updateClientApprovalApiUrl, async (payload, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  const { setLoading } = clientMaintenanceSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await post(updateClientApprovalApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(getSingleClient(payload?.ID));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});
export const deleteClientAction = createAsyncThunk('deleteClientAction/fetch', async (clientId, { rejectWithValue, dispatch }) => {
  const { setLoading } = clientMaintenanceSlice.actions || {};
  try {
    dispatch(setLoading(true));

    const response = await post(deleteClientApiUrl(clientId), clientId, true, dispatch);

    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        dispatch(notification(true, message, false));
        dispatch(removeClientFromList(clientId));
        return { success: true, data };
      }

      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

const clientMaintenanceSlice = createSlice({
  name: 'clientMaintenace',
  initialState: {
    data: [],
    allClientQuotes: [],
    clientData: [],
    singleClient: {},
    status: 'idle',
    error: null,
    loading: false,
    allShipping: []
  },
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setSingleClientData: (state, action) => {
      state.singleClient = action.payload;
    },
    setShippingData: (state, action) => {
      state.allShipping = action.payload;
    }
  }
});
export const { setLoading, setSingleClientData } = clientMaintenanceSlice.actions;
export default clientMaintenanceSlice.reducer;
