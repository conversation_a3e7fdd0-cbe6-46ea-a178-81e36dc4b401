import React, { useEffect, useState } from 'react';
import { Select, MenuItem, TextField, Button, Modal, Box } from '@mui/material';
import ErrorMessage from 'pages/component/errorMessage';
import { capitalize, isArray } from 'lodash';
import SelectComponent from 'pages/component/selectComponent';
import DynamicAutocomplete from 'pages/component/autoComplete';

const SelectWithAddNew = ({ portalNameList, onSelectChange, label, selectedOption, error, name }) => {
  const [openModal, setOpenModal] = useState(false);
  const [newOptionError, setNewOptionError] = useState(false);
  const [newOption, setNewOption] = useState('');
  const [options, setOptions] = useState([]);
  useEffect(() => {
    setOptions([...portalNameList]);
  }, [portalNameList]);

  const handleChange = (event, value) => {
    if (value === 'Add New') {
      setOpenModal(true);
    } else {
      onSelectChange({ label: value, value: value?.value });
    }
  };

  const handleNewOptionSave = () => {
    if (newOption) {
      setNewOptionError(false);
      const updatedOptions = [...options];
      updatedOptions.splice(updatedOptions.length - 1, 0, { label: newOption, value: newOption });
      setOptions(updatedOptions);
      setOpenModal(false);
      setNewOption('');
      onSelectChange({ label: newOption, value: newOption });
    } else {
      setNewOptionError(true);
    }
  };

  const handleClose = () => {
    setOpenModal(false);
    setNewOption('');
    setNewOptionError(false);
  };

  const getValue = () => {
    if (selectedOption) {
      return isArray(options)
        ? options?.find((option) => option?.value === selectedOption?.value) || { value: '', label: '' }
        : { value: '', label: '' };
    }
    return { value: '', label: '' };
  };

  return (
    <Box>
      <DynamicAutocomplete
        value={getValue() || null}
        onChange={handleChange}
        getOptionLabel={(option) => option.label || ''}
        error={error}
        fullWidth
        options={options}
        label=""
        placeholder="Select Option"
      />

      <Modal open={openModal} onClose={() => handleClose()} aria-labelledby="modal-title" aria-describedby="modal-description">
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: 400,
            bgcolor: 'background.paper',
            boxShadow: 24,
            p: 4
          }}
        >
          <TextField
            label={label}
            value={newOption}
            onChange={(e) => setNewOption(e.target.value)}
            fullWidth
            variant="outlined"
            margin="normal"
          />
          {newOptionError && <ErrorMessage message={`${capitalize(name)} is required`} />}
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 4 }}>
            <Button variant="contained" onClick={handleNewOptionSave}>
              Save
            </Button>
            <Button variant="contained" color="error" onClick={handleClose}>
              Cancel
            </Button>
          </div>
        </Box>
      </Modal>
    </Box>
  );
};

export default SelectWithAddNew;
