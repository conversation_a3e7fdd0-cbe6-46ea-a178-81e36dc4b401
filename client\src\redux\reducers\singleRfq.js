import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { patchRequest, post } from 'utils/axios';
import {
  addMaterialApiUrl,
  confirmMaterialApiUrl,
  loginApiUrl,
  materialUpdateApiUrl,
  rfqListPageUrl,
  searchMaterialApiUrl,
  singleRfqApiUrl,
  updateRfqApiUrl
} from 'utils/constant';
import { ALERT_SUCCESS } from './alertReducer';
import { notification } from 'utils/helper';

// Asynchronous thunk action to fetch profile data
export const getSingleRfq = createAsyncThunk('singleRfq/fetch', async (rfqId, { rejectWithValue, dispatch }) => {
  try {
    const response = await post(singleRfqApiUrl(rfqId), {}, true, dispatch);
    const { error, success, message, data } = get(response, 'data', {});

    if (success) {
      return data || [];
    } else {
      dispatch(notification(false, message, true));
      return rejectWithValue(message || 'Failed to fetch RFQ data');
    }
  } catch (error) {
    return rejectWithValue('An error occurred while fetching RFQ data');
  }
});

export const confirmMaterialAction = createAsyncThunk(confirmMaterialApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  try {
    dispatch(singleRfq.actions.setLoading(true));
    const response = await patchRequest(confirmMaterialApiUrl, data, true, dispatch);

    if (response) {
      dispatch(singleRfq.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(singleRfq.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(singleRfq.actions.setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

export const addMaterialAction = createAsyncThunk(addMaterialApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  try {
    dispatch(singleRfq.actions.setLoading(true));
    const response = await post(addMaterialApiUrl, data, true, dispatch);

    if (response) {
      dispatch(singleRfq.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(singleRfq.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(singleRfq.actions.setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

export const updateMaterialAction = createAsyncThunk(materialUpdateApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  try {
    dispatch(singleRfq.actions.setLoading(true));
    const response = await patchRequest(materialUpdateApiUrl, data, true, dispatch);
    if (response) {
      dispatch(singleRfq.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(singleRfq.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(singleRfq.actions.setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

export const updateRfqAction = createAsyncThunk(updateRfqApiUrl, async (data, thunkAPI) => {
  const { navigate, redirectTo, ...rest } = data || {};
  let payload = { ...rest };
  const { dispatch, rejectWithValue } = thunkAPI;
  try {
    dispatch(singleRfq.actions.setLoading(true));
    const response = await patchRequest(updateRfqApiUrl, payload, true, dispatch);

    if (response) {
      dispatch(singleRfq.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        navigate(redirectTo);
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(singleRfq.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(singleRfq.actions.setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

export const searchMaterialAction = createAsyncThunk(searchMaterialApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  try {
    dispatch(singleRfq.actions.setLoading(true));
    const response = await post(searchMaterialApiUrl, data, true, dispatch);

    if (response) {
      dispatch(singleRfq.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(singleRfq.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(singleRfq.actions.setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

const singleRfq = createSlice({
  name: 'singleRfq',
  initialState: {
    data: {},
    similarMaterials: [],
    singlePartNumber: {},
    status: 'idle',
    error: null
  },
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setSimilarPartNumber: (state, action) => {
      state.similarMaterials = action.payload;
    },
    setSinglePartNumber: (state, action) => {
      state.singlePartNumber = action.payload;
    },
    setSingleRfqAction: (state, action) => {
      state.data = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSingleRfq.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getSingleRfq.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload;
      })
      .addCase(getSingleRfq.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      });
  }
});
export const { setLoading, setSimilarPartNumber, setSinglePartNumber, setSingleRfqAction } = singleRfq.actions;
export default singleRfq.reducer;
