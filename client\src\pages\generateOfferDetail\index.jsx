import Grid from '@mui/material/Grid';
// import OrdersTable from './OrdersTable';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { get } from 'lodash';
import './generateOffer.css';
import OfferDetails from './offerDetails';
import MaterialTable from './generateOfferMaterial';
import OfferDetailTotal from './total';
import { calculateOfferApiUrl, myOfferPageUrl, offerPageUrl } from 'utils/constant';
import { useParams } from 'react-router';
import { calculateQuoteAction, generateOfferAction, getClientList, getSupplierList } from 'redux/reducers/offerReducer';
import { validateOffer } from './generateOfferValidation';
import { compareTwoArrayOfObjects, notification, showAlert } from 'utils/helper';
import Loader from 'components/Loader';
import { post } from 'utils/axios';
import Overview from '../singlrRfq/overView';
import { marginValidationMessage, quantityValidationMessage, unitpriceValidationMessage } from 'utils/validationMessage';
import { getMySingleOfferDetail } from 'redux/reducers/myOffersReducer';
import { Button } from '@mui/material';

// ==============================|| DASHBOARD - DEFAULT ||============================== //

export default function GenerateOffer() {
  const singleOffer = useSelector((state) => state.myOffer);
  const [offer, setOffer] = useState({});
  const [previousOffer, setPerviousOffer] = useState({});
  const [clientList, setClientList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showGenerateOfferBtn, setShowGenerateOfferBtn] = useState(false);
  const offerDetail = useSelector((state) => state.offer);
  const rfqId = useParams()?.id;
  const offerId = useParams()?.offerId;
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getSupplierList());
    dispatch(getClientList());
  }, []);

  useEffect(() => {
    if (offerId) {
      dispatch(getMySingleOfferDetail(offerId));
    }
  }, [offerId]);

  useEffect(() => {
    if (singleOffer?.singleOfferDetail && offerId) {
      let selectedOfferCopy = { ...get(singleOffer, 'singleOfferDetail', {}) };
      if (selectedOfferCopy?.Quotes && Array.isArray(selectedOfferCopy?.Quotes)) {
        let materials = selectedOfferCopy?.Quotes?.map((quote) => {
          const {
            rfqId,
            materialId,
            isTax,
            partNumber,
            weight,
            leadTime,
            quantity,
            unitOfferPrice,
            brand,
            Quantity_Required,
            materialDescription,
            totalCost,
            date,
            quoteId,
            kam,
            supplierId,
            unitPrice,
            shippingCost,
            taxCost,
            unitCurrency,
            margin,
            totalCostAfterMargin,
            offerCurrency,
            unitOfferPriceWithHandlingCost,
            unitOfferPriceHandlingCost,
            exchangedOfferCost,
            exchangedOfferCostWithHandlingCost,
            exchangeOfferHandlingCost,
            tempUnitOfferPriceWithHandlingCost,
            tempUnitOfferPrice,
            ...rest
          } = quote || {};

          return {
            RFQ_ID: rfqId,
            Material_ID: materialId,
            Part_Number: partNumber,
            Quantity_Required: Quantity_Required || '',
            Material_Description: materialDescription,
            brand: brand,
            suppliers: [
              {
                unitOfferPrice: unitOfferPriceWithHandlingCost || tempUnitOfferPriceWithHandlingCost,
                unitOfferPriceCost: unitOfferPrice || tempUnitOfferPrice,
                unitOfferPriceHandlingCost: unitOfferPriceHandlingCost,
                weight: weight,
                leadTime: leadTime,
                isTax: isTax || false,
                date: date,
                id: quoteId,
                kam: kam,
                supplier: supplierId,
                supplierId: supplierId,
                unitPrice: unitPrice,
                sh: shippingCost,
                tax: taxCost,
                quantity: quantity,
                offer: exchangedOfferCostWithHandlingCost || totalCostAfterMargin,
                exchangedOfferCost: exchangedOfferCost,
                exchangeOfferHandlingCost: exchangeOfferHandlingCost,
                totalCost: totalCost,
                currency: unitCurrency,
                margin: margin,
                totalCostAfterMargin: totalCostAfterMargin,
                offerCurrency: offerCurrency,
                ...rest
              }
            ]
          };
        });
        selectedOfferCopy.materials = materials;
      }

      const { ClientID, Notes, OfferCurrency, ValidFor, FirstName, LastName, SubTotal, GrandTotal, Tax, PaymentTerms } =
        selectedOfferCopy || {};
      let totalDetail = {
        subTotal: SubTotal,
        taxCost: Tax,
        grandTotal: GrandTotal
      };
      if (selectedOfferCopy) {
        selectedOfferCopy = {
          ...selectedOfferCopy,
          paymentTerm: PaymentTerms,
          kam: `${FirstName || ''} ${LastName || ''}`.trim(),
          totalDetail: totalDetail,
          clientId: ClientID,
          offerCurrency: OfferCurrency,
          validFor: ValidFor,
          notes: Notes
        };
        setShowGenerateOfferBtn(true);
        setOffer({ ...selectedOfferCopy, language: selectedOfferCopy?.Language });
        setPerviousOffer({ ...selectedOfferCopy, language: selectedOfferCopy?.Language });
      }
    } else {
      let selectedOfferCopy = { ...get(offerDetail, 'selectedOffer', {}) };

      if (selectedOfferCopy?.materials && Array.isArray(selectedOfferCopy?.materials)) {
        selectedOfferCopy.materials = selectedOfferCopy?.materials?.map((material) => {
          if (material.suppliers && Array.isArray(material.suppliers)) {
            const updatedSuppliers = material?.suppliers?.map((supplier) => ({
              ...supplier,
              offer: supplier?.totalCost,
              unitOfferPrice: supplier?.unitPrice,
              weight: supplier?.weight
            }));
            return { ...material, suppliers: updatedSuppliers };
          }
          return material;
        });
      }
      if (selectedOfferCopy) {
        setOffer({ ...selectedOfferCopy, clientId: selectedOfferCopy?.ClientID });
        setPerviousOffer({ ...selectedOfferCopy, clientId: selectedOfferCopy?.ClientID });
      }
    }
  }, [offerDetail?.selectedOffer, singleOffer?.singleOfferDetail]);

  useEffect(() => {
    const formattedClientList = get(offerDetail, 'clientList', [])?.map((client) => ({
      label: client.Name,
      value: client.ClientID,
      Status: client.Status
    }));
    if (formattedClientList && formattedClientList?.length > 0) {
      setClientList(formattedClientList);
    }
  }, [offerDetail?.clientList]);

  const transformData = (data) => {
    const transformed = {
      rfqId: data?.RFQ_ID?.toString(),
      offerCurrency: data?.offerCurrency,
      // company:data?.company,
      validFor: Number(data?.validFor) || 0,
      paymentTerms: data?.paymentTerm,
      notes: data?.notes,
      language: data?.language,
      clientId: data?.clientId,
      materials: data.materials.map((material) => ({
        brand: material?.brand,

        partNumber: material?.Part_Number,
        materialDescription: material?.Material_Description,
        materialId: material.Material_ID.toString(),
        Quantity_Required: Number(material?.Quantity_Required) || 0,
        supplier: material?.suppliers?.map((supplier) => ({
          supplierId: supplier?.supplierId?.toString(),
          quoteId: supplier?.id?.toString(),
          weight: supplier?.weight,
          unitPrice: Number(supplier.unitPrice) || 0,
          shippingCost: parseFloat(supplier.sh) || 0,
          tax: supplier?.tax || 0,
          isTax: supplier?.isTax || false,
          quantity: Number(supplier.quantity) || 0,
          totalCost: Number(supplier.totalCost) || 0,
          unitCurrency: supplier.currency,
          margin: Number(supplier.margin),
          offeredPrice: supplier?.totalCost,
          totalOfferPrice: supplier?.offer,
          leadTime: Number(supplier?.leadTime),
          exchangedOfferCostWithHandlingCost: supplier?.offer,
          exchangedOfferCost: supplier?.exchangedOfferCost,
          exchangeOfferHandlingCost: supplier?.exchangeOfferHandlingCost,
          unitOfferPrice: supplier?.unitOfferPriceCost,
          unitOfferPriceHandlingCost: supplier?.unitOfferPriceHandlingCost,
          unitOfferPriceWithHandlingCost: supplier?.unitOfferPrice,
          notes: supplier?.notes
        }))[0] // Assuming you need only the first supplier
      }))
    };

    return transformed;
  };

  const generateOffer = () => {
    let payload = transformData(offer);

    const validationResult = validateOffer(payload);
    if (!validationResult.isValid) {
      dispatch(notification(false, validationResult?.message, true));
      return;
    }

    dispatch(generateOfferAction(payload));
  };

  const calculateOffer = async () => {
    try {
      if (!offer?.offerCurrency) {
        showAlert(dispatch, false, 'Offer currency is required', true);
        return;
      }
      if (!offer?.clientId) {
        showAlert(dispatch, false, 'Client is required', true);
        return;
      }

      setLoading(true);

      let quotes = [];
      let validationErrors = {
        invalidMargin: false,
        invalidQuantity: false,
        invalidUnitPrice: false
      };

      const validateAndPushQuote = (material) => {
        const supplier = get(material, 'suppliers[0]', {});
        const { id, currency, margin, weight, quantity, isTax, unitPrice, supplierId, leadTime, totalCost } = supplier;

        if (margin == null || margin <= 0) {
          validationErrors.invalidMargin = true;
        }
        if (quantity == null || quantity <= 0) {
          validationErrors.invalidQuantity = true;
        }
        if (unitPrice == null || unitPrice <= 0) {
          validationErrors.invalidUnitPrice = true;
        }

        const quote = {
          quoteId: id?.toString(),
          currency,
          unitPrice,
          supplierId: supplierId?.toString(),
          weight,
          isTax,
          totalCost,
          quantity: Number(quantity),
          margin: Number(margin)
        };

        quotes.push(quote);
      };

      offer?.materials?.forEach(validateAndPushQuote);

      if (!validationErrors.invalidMargin && !validationErrors.invalidQuantity && !validationErrors.invalidUnitPrice) {
        const payload = {
          offerCurrency: offer?.offerCurrency,
          clientId: offer?.clientId,
          quotes
        };

        const response = await post(calculateOfferApiUrl, payload, true, dispatch);

        if (response) {
          setLoading(false);
          const { error, success, message, data } = get(response, 'data', {});

          if (success) {
            showAlert(dispatch, success, message, true);
            setShowGenerateOfferBtn(true);
            const { grandTotal, subTotal, taxCost, handlingCost, tax } = data || {};

            const updatedMaterials = offer?.materials?.map((material, index) => {
              const quotesData = data?.quotes || [];
              const {
                totalCost,
                unitOfferPriceWithHandlingCost,
                exchangedOfferCost,
                unitOfferPrice,
                exchangedOfferCostWithHandlingCost,
                exchangeOfferHandlingCost,
                unitOfferPriceHandlingCost
              } = quotesData[index] || {};
              const supplier = get(material, 'suppliers[0]', {});
              const updatedSupplier = {
                ...supplier,
                offer: exchangedOfferCostWithHandlingCost,
                exchangedOfferCost: exchangedOfferCost,
                exchangeOfferHandlingCost: exchangeOfferHandlingCost,
                offerCurrency: offer?.offerCurrency,
                unitOfferPriceCost: unitOfferPrice,
                unitOfferPriceHandlingCost: unitOfferPriceHandlingCost,
                unitOfferPrice: unitOfferPriceWithHandlingCost,
                totalCost: totalCost
              };
              return { ...material, suppliers: [updatedSupplier] };
            });

            setOffer({
              ...offer,
              totalDetail: { grandTotal, subTotal, taxCost, handlingCost, tax },
              materials: updatedMaterials
            });
            setPerviousOffer({
              ...previousOffer,
              totalDetail: { grandTotal, subTotal, taxCost, handlingCost, tax },
              materials: updatedMaterials
            });

            const offerDetailTotalElement = document.getElementById('offerDetailTotal');
            if (offerDetailTotalElement) {
              offerDetailTotalElement.scrollIntoView({ behavior: 'smooth' });
            }
          } else {
            const errorMessage = message || response?.message || 'Something went wrong';
            showAlert(dispatch, success, errorMessage, true);
          }
        } else {
          setLoading(false);
        }
      } else {
        setLoading(false);
        if (validationErrors.invalidMargin) showAlert(dispatch, false, marginValidationMessage, true);
        if (validationErrors.invalidQuantity) showAlert(dispatch, false, quantityValidationMessage, true);
        if (validationErrors.invalidUnitPrice) showAlert(dispatch, false, unitpriceValidationMessage, true);
      }
    } catch (error) {
      setLoading(false);
    }
  };

  const buttonsConfig = [
    {
      type: 'close',
      label: 'Close',
      link: offerId ? myOfferPageUrl : `${offerPageUrl}/${rfqId}` // your link
    }
  ];

  const handleChange = (e, material, supplier) => {
    const { value, name } = get(e, 'target', {}) || {};
    setShowGenerateOfferBtn(false);
    const materialIndex = offer?.materials?.findIndex((mat) => mat?.Material_ID === material?.Material_ID);
    if (materialIndex !== -1) {
      const updatedMaterials = [...offer.materials];
      const updatedMaterial = { ...updatedMaterials[materialIndex] };
      const supplierIndex = updatedMaterial.suppliers.findIndex((sup) => sup.id === supplier.id);
      if (supplierIndex !== -1) {
        const updatedSuppliers = [...updatedMaterial.suppliers];
        const updatedSupplier = { ...updatedSuppliers[supplierIndex] };

        updatedSupplier[name] = value;
        if (name === 'quantity') {
          updatedSupplier.isMaterialUpdated = true;
        }
        updatedSuppliers[supplierIndex] = updatedSupplier;
        updatedMaterial.suppliers = updatedSuppliers;
        updatedMaterials[materialIndex] = updatedMaterial;
        const updatedOffer = { ...offer, materials: updatedMaterials };
        setOffer(updatedOffer);
      }
    }
  };

  const handleOfferDetail = (e) => {
    setOffer((prevOffer) => ({
      ...prevOffer,
      [e.target.name]: e.target.value
    }));
  };

  const handleNotesChange = (name, value) => {
    setOffer((prevOffer) => ({
      ...prevOffer,
      [name]: value
    }));
  };

  const checkShowGenerateBtn = () => {
    const hasValidMargin = offer?.materials?.every((material) =>
      material?.suppliers?.every((supplier) => supplier.margin && supplier.margin > 0 && !supplier?.isMaterialUpdated)
    );
    return hasValidMargin && showGenerateOfferBtn;
  };
  const checkIsDisableUpdateMaterial = () => {
    const allMaterialUpdated = offer?.materials?.every((material) =>
      material?.suppliers?.every((supplier) => !supplier?.isMaterialUpdated)
    );
    return !allMaterialUpdated || isLoading();
  };

  const isLoading = () => {
    return singleOffer?.loading || get(offerDetail, 'loading', false) || loading;
  };

  const updateMaterial = async (material, supplier) => {
    const { supplierId, currency, id, leadTime, unitPrice, quantity, isTax, weight, notes } = supplier || {};
    let initialPayload = {
      materialId: material?.Material_ID?.toString(),
      rfqId: rfqId,
      quoteId: id?.toString(),
      isEdit: true,
      leadTime: leadTime,
      supplierId: supplierId?.toString(),
      unitPrice: Number(unitPrice),
      currency: currency?.toString(),
      weight: Number(weight),
      quantity: Number(quantity),
      isTax: isTax,
      isOffered: true,
      notes,
      isGeneratedOfferPage: true
    };
    const response = await dispatch(calculateQuoteAction(initialPayload));
    const { success, data: updatedQuotes } = response?.payload || {};
    if (success) {
      setOffer((prevResponse) => {
        const updatedMaterials = prevResponse?.materials?.map((previousMaterial) => {
          if (previousMaterial.Material_ID === material?.Material_ID) {
            const updatedSuppliers = previousMaterial?.suppliers?.map((previousSupplier) => {
              return previousSupplier?.id === supplier?.id
                ? {
                    ...supplier,
                    isMaterialUpdated: false,
                    totalCost: updatedQuotes?.totalCost
                  }
                : previousSupplier;
            });
            return { ...previousMaterial, suppliers: updatedSuppliers };
          }
          return previousMaterial;
        });

        return { ...prevResponse, materials: updatedMaterials };
      });
    }
  };

  return (
    <Grid container spacing={2}>
      {isLoading() && <Loader />}
      <Grid item xs={12}>
        <Overview rfqDetails={offer} buttonsConfig={buttonsConfig} />
      </Grid>

      <Grid item xs={12}>
        <OfferDetails
          onChange={handleOfferDetail}
          handleNotesChange={(name, value) => handleNotesChange(name, value)}
          offer={offer}
          clientList={clientList}
          calculateOffer={calculateOffer}
          loading={get(offerDetail, 'loading', false) || loading}
        />
      </Grid>

      <Grid item xs={12}>
        <MaterialTable
          supplierList={get(offerDetail, 'supplierList', [])?.map((supplier) => ({ label: supplier?.Name, value: supplier?.SupplierID }))}
          materials={get(offer, 'materials', [])}
          offer={offer}
          onChange={(e, material, supplier) => handleChange(e, material, supplier)}
          calculateOffer={calculateOffer}
          loading={isLoading()}
          updateMaterial={(material, supplier) => updateMaterial(material, supplier)}
          disableUpdateMaterial={checkIsDisableUpdateMaterial()}
        />
      </Grid>

      <Grid item xs={12} id="offerDetailTotal">
        <OfferDetailTotal
          showGenerateOfferBtn={
            !checkShowGenerateBtn() || !compareTwoArrayOfObjects(previousOffer?.materials, offer?.materials, 'quantity')
          }
          generateOffer={generateOffer}
          totalDetail={get(offer, 'totalDetail', {})}
          loading={get(offerDetail, 'loading', false) || loading}
          calculateOffer={calculateOffer}
          disableUpdateMaterial={checkIsDisableUpdateMaterial()}
        />
      </Grid>
    </Grid>
  );
}
