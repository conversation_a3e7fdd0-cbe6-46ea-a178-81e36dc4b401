import React, { useEffect } from 'react';
import { Typo<PERSON>, Button, Container, Box, Paper, Grid, Divider } from '@mui/material';
import { useParams } from 'react-router-dom';
import { CheckCircleOutline } from '@mui/icons-material';
import Loader from 'components/Loader';
import { Person, LocationOn, Business, CalendarToday, AccountCircle } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { getSingleClient, updateClientApprovalStatus } from 'redux/reducers/clientReducer';
import ClientDetailTable from './clientDetail';
import './clientApproval.css';
import ThankyouMessage from '../thankyou';
import { APPROVED, REJECTED } from 'utils/constant';
import Header from '../header';

const ClientApprovalPage = () => {
  const { id: clientID, token } = useParams();
  const clientData = useSelector((state) => state.client);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getSingleClient(clientID));
  }, [clientID]);

  const handleApprove = async () => {
    dispatch(updateClientApprovalStatus({ ID: clientID, token: token, status: APPROVED }))
  };

  const fields = [
    { label: 'Name', key: 'Name', icon: <Person /> },
    { label: 'Address', key: 'Address', icon: <LocationOn /> },
    { label: 'Municipality', key: 'Municipality', icon: <LocationOn /> },
    { label: 'Business Activity', key: 'BusinessActivity', icon: <Business /> },
    { label: 'Updated Date', key: 'Updated_At', type: 'date', icon: <CalendarToday />, show: 'Updated_By_Name' },
    { label: 'Created By', key: 'Created_By', icon: <AccountCircle /> },
    { label: 'Updated By', key: 'Updated_By_Name', icon: <AccountCircle />, show: 'Updated_By_Name' },
    { label: 'Created Date', key: 'Created_At', type: 'date', icon: <CalendarToday /> },
  ];
  const { loading, singleClient } = clientData || {}
  const { Status, approverName, lastUpdatedStatus } = singleClient || {}

  if (Status === REJECTED || Status === APPROVED) {
    return <ThankyouMessage status={lastUpdatedStatus} name="Client" />
  }

  return (<>
    <Header approverName={approverName || ''} />
    <Container maxWidth="md">
      {loading && <Loader />}
      <Box textAlign="center">
        <CheckCircleOutline sx={{ fontSize: 80, color: 'green' }} />

        <Typography variant="h3" gutterBottom color="primary" sx={{ fontWeight: 'bold' }}>
          Approve Client
        </Typography>

        <Divider sx={{ my: 2, bgcolor: 'primary.main', height: 2 }} />

        <Typography variant="body1" gutterBottom color="textSecondary" sx={{ mb: 1 }}>
          You are about to approve this client . Review the details below .
        </Typography>

        {singleClient && (
          <div style={{ marginBottom: '20px' }}>
            <Typography variant="h5" gutterBottom color="secondary" sx={{ mb: 3 }}>
              Client Details
            </Typography>
            <Grid container spacing={2} justifyContent="center">
              <ClientDetailTable fields={fields} clientData={singleClient} />
            </Grid>
          </div>
        )}

        <Button
          variant="contained"
          color="success"
          onClick={handleApprove}
          disabled={loading}
        >
          Confirm Approval
        </Button>
      </Box>
    </Container>
  </>
  );
};

export default ClientApprovalPage;
