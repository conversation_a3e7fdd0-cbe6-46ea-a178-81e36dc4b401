import { Card, CardContent, Typography, Stack, Divider, Box, Avatar } from '@mui/material';
import { AccessTime, Email, PushPin } from '@mui/icons-material';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import { getEmailResponsesByRFQIDAction } from 'redux/reducers/centralizedReducer';
import Loader from 'components/Loader';
import { formatRelativeTime } from 'utils/helper';
import { isArray } from 'lodash';

const EmailResponse = () => {
  const dispatch = useDispatch();
  const { id: RFQID } = useParams();
  const emailResponseData = useSelector((state) => state?.requestedPrice || []);

  useEffect(() => {
    dispatch(getEmailResponsesByRFQIDAction(RFQID));
  }, [RFQID]);

  const sortedResponses =
    isArray(emailResponseData?.emailResponses) &&
    emailResponseData?.emailResponses?.slice()?.sort((a, b) => new Date(b?.timestamp) - new Date(a?.timestamp));

  const getInitials = (name) => {
    if (!name) return '';
    const words = name?.split(' ');
    return words.length > 1 ? words?.[0]?.[0] + words?.[1]?.[0] : words?.[0]?.[0];
  };

  const renderEmailDetails = (name, emailAddress) => (
    <Stack direction="row" spacing={2} alignItems="center">
      <Avatar className="email-avatar">{getInitials(name)}</Avatar>
      <Box>
        <Typography variant="subtitle1" className="email-name">
          {name}
        </Typography>
        <Stack direction="row" spacing={1} alignItems="center">
          <Email fontSize="small" className="email-response-icon" />
          <Typography variant="body2" className="email-address">
            {emailAddress?.replace('>', '')}
          </Typography>
        </Stack>
      </Box>
    </Stack>
  );

  return (
    <Stack spacing={3} className="email-response-container" maxWidth={sortedResponses?.length > 0 ? 1000 : '100%'}>
      {emailResponseData?.loading && <Loader />}
      <Typography variant="h4" className="email-response-title">
        📩 Email Responses
      </Typography>

      {sortedResponses?.length > 0 ? (
        sortedResponses.map(({ messageId, from, subject, timestamp, reply }) => {
          const [name, emailAddress] = from?.split(' <') || [];

          return (
            <Card key={messageId} variant="outlined" className="email-response-card">
              <CardContent>
                {renderEmailDetails(name, emailAddress)}

                <Stack direction="row" justifyContent="space-between" alignItems="center" mt={2}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <PushPin className="email-response-icon" fontSize="small" />
                    <Typography variant="h6" className="email-subject">
                      {subject}
                    </Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <AccessTime fontSize="small" className="email-response-icon" />
                    <Typography variant="body2" className="email-time">
                      {formatRelativeTime(timestamp)}
                    </Typography>
                  </Stack>
                </Stack>

                <Divider className="email-divider" />

                <Box>
                  <Typography variant="subtitle1" className="email-reply-title">
                    Reply
                  </Typography>
                  <Typography variant="body1" className="email-reply-text">
                    {reply}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          );
        })
      ) : (
        <Typography variant="h6" className="email-no-response">
          No email responses found.
        </Typography>
      )}
    </Stack>
  );
};

export default EmailResponse;
