const { BigQuery } = require("@google-cloud/bigquery");
const { Storage } = require("@google-cloud/storage");
const { google } = require("googleapis");
const {
  PROJECT_ID,
  GCP_CRED,
  DATASET_ID_MAIN,
  DATASET_ID_SCRAPE,
  DATASET_ID_LANDINGZONE,
  DATASET_ID_DASHBOARDS,
  DATASET_ID_AUTOMATION,
  SMTP_EMAIL,
  DATASET_ID_SOURCING,
} = require("../constants");

const bigQueryClient = new BigQuery({
  projectId: PROJECT_ID,
  keyFilename: GCP_CRED,
});

const storageClient = new Storage({
  projectId: PROJECT_ID,
  keyFilename: GCP_CRED,
});

const mainDataset = bigQueryClient.dataset(DATASET_ID_MAIN);
const scrapedDataset = bigQueryClient.dataset(DATASET_ID_SCRAPE);
const landingZoneDataset = bigQueryClient.dataset(DATASET_ID_LANDINGZONE);
const dashboardsDataset = bigQueryClient.dataset(DATASET_ID_DASHBOARDS);
const automationDataset = bigQueryClient.dataset(DATASET_ID_AUTOMATION);
const sourcingDataset = bigQueryClient.dataset(DATASET_ID_SOURCING);

const connectDB = async () => {
  const query = `SELECT RFQ_ID FROM RFQ LIMIT 1`;
  try {
    const [rows] = await scrapedDataset.query(query);
    if (rows.length > 0) {
      console.log("BigQuery Connected");
    }
  } catch (error) {
    console.error(`BigQuery Error: ${error}`);
    process.exit(1);
  }
};

const authenticateGmail = async () => {
  const auth = new google.auth.JWT({
    keyFile: GCP_CRED,
    scopes: ["https://www.googleapis.com/auth/gmail.readonly"],
    subject: SMTP_EMAIL,
  });

  return google.gmail({ version: "v1", auth });
};

module.exports = {
  connectDB,
  mainDataset,
  bigQueryClient,
  scrapedDataset,
  landingZoneDataset,
  dashboardsDataset,
  automationDataset,
  storageClient,
  authenticateGmail,
  sourcingDataset,
};
