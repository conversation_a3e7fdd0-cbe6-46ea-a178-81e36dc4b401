/* ImportFile.css */

.container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 100px);
    background-color: #f5f5f5;
    padding: 24px;
    border-radius: 8px;
}

.inner-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 70px;
}

.main-container {
    height: calc(100vh - 100px);
    background-color: #f5f5f5;
    padding: 24px;
    border-radius: 8px;
}

.header {
    margin-bottom: 24px;
    text-align: center;
    font-weight: bold;
}

.import-button {
    padding: 16px;
    font-size: 1rem;
    transition: transform 0.3s ease-in-out;
}

.import-button:hover {
    transform: scale(1.1);
}

.auto-complete {
    min-width: 300px;
    background-color: white;
    border-radius: 8px;
}

.update-btn {
    margin-left: 20px;
}