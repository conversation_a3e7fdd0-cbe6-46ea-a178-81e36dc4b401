/* eslint-disable no-unused-vars */
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { getUserDetail } from 'utils/auth';
import { getRequest, patchRequest, post } from 'utils/axios';
import {
  addRfqApiUrl,
  assignStatusApiUrl,
  automatedRfqApiUrl,
  discardRfqApiUrl,
  getKamApiUrl,
  getPortalListApiUrl,
  releaseRfqApiUrl,
  reserveRfqPageUrl,
  rfqApiUrl,
  rfqListPageUrl,
  triggerAutomationApiUrl,
  RFQPERPAGELIMIT,
  automatedRfqPageUrl,
  ReservedSingleRfqPageUrl,
  offerPageUrl,
  rfqConfirmedMaterialApiUrl,
  COUNTRY_MANAGER
} from 'utils/constant';
import { notification } from 'utils/helper';

export const getAllRfq = createAsyncThunk(rfqApiUrl, async (data, { dispatch }) => {
  try {
    const response = await post(rfqApiUrl, data, true, dispatch);
    if (response) {
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        return data;
      }
      if (!success) {
        return [];
      }
    }
  } catch (error) {
    return [];
  }
});

export const getAllRfqForConfirmedMaterials = createAsyncThunk(rfqConfirmedMaterialApiUrl, async (data, { dispatch }) => {
  try {
    const response = await post(rfqConfirmedMaterialApiUrl, data, true, dispatch);
    if (response) {
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        return data;
      }
      if (!success) {
        return [];
      }
    }
  } catch (error) {
    return [];
  }
});

export const getAutomatedRfq = createAsyncThunk(automatedRfqApiUrl, async (data, { dispatch }) => {
  const { setLoading, setAutomatedRfq } = rfqSlice.actions || {};

  try {
    dispatch(setLoading(true));
    const response = await post(automatedRfqApiUrl, data, true, dispatch);
    if (response) {
      dispatch(setLoading(false));

      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(setAutomatedRfq(data));
        return data;
      }
      if (!success) {
        dispatch(setAutomatedRfq([]));
        return [];
      }
    } else {
      dispatch(setAutomatedRfq([]));
      dispatch(setLoading(false));
      return [];
    }
  } catch (error) {
    dispatch(setAutomatedRfq([]));
    dispatch(setLoading(false));
    return [];
  }
});

export const triggerAutomationAction = createAsyncThunk(triggerAutomationApiUrl, async (data, { dispatch }) => {
  const { setLoading, setAutomatedRfq } = rfqSlice.actions || {};

  try {
    dispatch(setLoading(true));
    const response = await getRequest(triggerAutomationApiUrl, data, true, dispatch);

    if (response) {
      dispatch(setLoading(false));

      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        return {
          data,
          success
        };
      }

      if (!success) {
        dispatch(setLoading(false));
        return { data: {}, success: false };
      }
    } else {
      dispatch(setLoading(false));
      return { data: {}, success: false };
    }
  } catch (error) {
    dispatch(setLoading(false));
    return { data: {}, success: false };
  }
});

export const getAllKamAction = createAsyncThunk(getKamApiUrl, async (data, { dispatch }) => {
  try {
    const response = await getRequest(getKamApiUrl, data, true, dispatch);
    if (response) {
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(rfqSlice.actions.setKamList(data));
        return data;
      }
      if (!success) {
        return [];
      }
    }
  } catch (error) {
    return [];
  }
});

export const getAllPortalAction = createAsyncThunk(getPortalListApiUrl, async (data, { dispatch }) => {
  try {
    const response = await getRequest(getPortalListApiUrl, data, true, dispatch);
    if (response) {
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(rfqSlice.actions.setPortalList(data));
        return data;
      }
      if (!success) {
        return [];
      }
    }
  } catch (error) {
    return [];
  }
});

export const assignRfqAction = createAsyncThunk(assignStatusApiUrl, async (data, { dispatch }) => {
  try {
    dispatch(rfqSlice.actions.setLoading(true));
    const { currentPage, filters, pageUrl, ...rest } = data;
    const payload = currentPage || pageUrl ? { ...rest } : data;

    const response = await patchRequest(assignStatusApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(rfqSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        if (pageUrl !== ReservedSingleRfqPageUrl && pageUrl !== offerPageUrl) {
          if (pageUrl === automatedRfqPageUrl) {
            dispatch(getAutomatedRfq({ limit: 5, page: currentPage || 1, filterData: filters }));
          } else {
            dispatch(getAllRfq({ limit: RFQPERPAGELIMIT, page: currentPage || 1, filterData: filters }));
          }
        }
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, message };
      }
    } else {
      dispatch(rfqSlice.actions.setLoading(false));
    }
  } catch (error) {
    // dispatch(notification(false, error, true));
    dispatch(rfqSlice.actions.setLoading(false));
    return { success: false, message: error?.message };
  }
});

export const discardRfqAction = createAsyncThunk('discard/patch', async (data, { rejectWithValue, dispatch }) => {
  try {
    dispatch(rfqSlice.actions.setLoading(true));
    const { currentPage, filters, ...rest } = data;
    const payload = currentPage ? { ...rest } : data;
    const response = await patchRequest(discardRfqApiUrl(data?.rfqId), '', true, dispatch);
    if (response) {
      dispatch(rfqSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        dispatch(getAllRfq({ limit: RFQPERPAGELIMIT, page: currentPage || 1, filterData: filters }));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, message };
      }
    } else {
      dispatch(rfqSlice.actions.setLoading(false));
    }
  } catch (error) {
    // dispatch(notification(false, error, true));
    dispatch(rfqSlice.actions.setLoading(false));
    return { success: false, message: error?.message };
  }
});

export const releaseRfqAction = createAsyncThunk(releaseRfqApiUrl, async (data, { dispatch }) => {
  try {
    dispatch(rfqSlice.actions.setLoading(true));
    const { currentPage, ...rest } = data;
    const payload = currentPage ? { ...rest } : data;
    const response = await patchRequest(releaseRfqApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(rfqSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        dispatch(getAllRfq({ limit: RFQPERPAGELIMIT, page: currentPage || 1 }));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, message };
      }
    } else {
      dispatch(rfqSlice.actions.setLoading(false));
    }
  } catch (error) {
    // dispatch(notification(false, error, true));
    dispatch(rfqSlice.actions.setLoading(false));
    return { success: false, message: error?.message };
  }
});

export const addRfqAction = createAsyncThunk(addRfqApiUrl, async (data, { dispatch }) => {
  const { navigate, redirectTo, ...rest } = data || {};
  let payload = { ...rest };
  try {
    dispatch(rfqSlice.actions.setLoading(true));
    const response = await post(addRfqApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(rfqSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        let user=getUserDetail()
        dispatch(notification(success, message, error));
        if(user?.role === COUNTRY_MANAGER) {
          navigate(rfqListPageUrl);
        }else{
          navigate(reserveRfqPageUrl);
        }
        
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, message };
      }
    } else {
      dispatch(rfqSlice.actions.setLoading(false));
    }
  } catch (error) {
    // dispatch(notification(false, error, true));
    dispatch(rfqSlice.actions.setLoading(false));
    return { success: false, message: error?.message };
  }
});

const rfqSlice = createSlice({
  name: 'rfq',
  initialState: {
    data: [],
    status: 'idle',
    loading: false,
    error: null,
    rfqForConfirmedMaterials:[],
    kamList: []
  },
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setKamList: (state, action) => {
      state.kamList = action.payload;
    },
    setPortalList: (state, action) => {
      state.portalList = action.payload;
    },
    setAutomatedRfq: (state, action) => {
      state.automatedRFQ = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllRfq.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getAllRfq.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload;
      })
      .addCase(getAllRfq.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })
      .addCase(getAllRfqForConfirmedMaterials.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getAllRfqForConfirmedMaterials.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.rfqForConfirmedMaterials = action.payload;
      })
      .addCase(getAllRfqForConfirmedMaterials.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      });
  }
});
export const { setLoading, setKamList } = rfqSlice.actions;
export default rfqSlice.reducer;
