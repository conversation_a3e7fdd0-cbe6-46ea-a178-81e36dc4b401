import React from 'react';
import { Typography, Tooltip } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';


const ViewOfferAction = ({ rowData, iconContent, name }) => {
  const { isOffered } = rowData || {}

  const getValueByName = (supplier, name, item) => {
    return supplier[name] || '-';
  };


  const RenderInfo = ({ iconContent, supplier, }) => {
    return (
      <div style={{ padding: '10px' }}>
        {iconContent?.map((icon) => (
          <div className='tooltip-wrapper'>
            <Typography variant="body1">{icon?.label}</Typography>
            <Typography variant="caption" sx={{ textAlign: 'center' }}>
              {getValueByName(supplier, icon?.value)}
            </Typography>
          </div>
        ))}
      </div>
    );
  };
  if (!rowData?.offer) return '-'; 

  return (
    <div style={{ display: 'flex', gap: '10px' }}>
      <Typography
        variant="body1"
        sx={{
          overflowWrap: 'break-word',
          wordWrap: 'break-word',
        }}
        color={isOffered&&'secondary'}
      >
        {rowData[name]}
      </Typography>
      <Tooltip
        title={<RenderInfo iconContent={iconContent} supplier={rowData} />}
        placement="top"
      >
        <div>
          <InfoIcon fontSize='16px' className='info-icon' />
        </div>
      </Tooltip>
    </div>
  );
};

export default ViewOfferAction
