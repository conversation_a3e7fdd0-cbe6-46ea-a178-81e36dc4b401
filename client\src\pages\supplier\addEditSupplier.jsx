import React, { useEffect, useState } from 'react';
import { Button, Grid, Typography, Box } from '@mui/material';
import ErrorMessage from 'pages/component/errorMessage';
import MainCard from 'components/MainCard';
import InputField from 'pages/component/inputField';
import SelectComponent from 'pages/component/selectComponent';
import TextAreaComponent from 'pages/component/textArea';
import { supplierUrl } from 'utils/constant';
import { useDispatch, useSelector } from 'react-redux';
import countriesList from './countriesList';
import {
  addSupplierMaintenanace,
  getShipping,
  getSingleSupplier,
  setSingleSupplierData,
  updateSupplierMaintenanace
} from 'redux/reducers/supplierMaintenance';
import Loader from 'components/Loader';
import { validate } from 'pages/component/validation';
import { useNavigate, useParams } from 'react-router';
import { Link } from 'react-router-dom';
import DynamicAutocomplete from 'pages/component/autoComplete';

// Define styles
const styles = {
  formContainer: {
    padding: '16px'
  },
  card: {
    margin: '16px 0'
  },
  buttonGroup: {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '10px'
  },
  inputField: {
    width: '100%'
  },
  textarea: {
    width: '100%',
    padding: '4px'
  },
  errorText: {
    color: 'red',
    marginTop: '4px'
  }
};

const initialState = {
  name: '',
  contactName: '',
  contactLastName: '',
  email: '',
  country: '',
  website: '',
  shipping: '',
  notes: ''
};

const AddEditSupplier = () => {
  const [countries, setCountries] = useState([]);
  const [shipping, setShipping] = useState([]);
  const supplierData = useSelector((state) => state.supplier);
  const [supplierDetails, setSupplierDetails] = useState(initialState);
  const [errors, setErrors] = useState({});
  const supplierId = useParams()?.id;
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const fieldConfigurations = [
    { type: 'text', name: 'name', placeholder: 'Enter supplier name', label: 'Supplier Name' },
    { type: 'text', name: 'contactName', placeholder: "Enter contact's first name", label: 'Contact First Name' },
    { type: 'text', name: 'contactLastName', placeholder: "Enter contact's last name", label: 'Contact Last Name' },
    { type: 'email', name: 'email', placeholder: "Enter contact's email address", label: 'Contact Email' },
    { type: 'autocomplete', name: 'country', placeholder: 'Select country', label: 'Select country', items: countries },
    { type: 'text', name: 'website', placeholder: "Enter supplier's website URL", label: 'Website' },
    { type: 'autocomplete', name: 'shipping', placeholder: 'Select shipping', label: 'Shipping Address', items: shipping },
    { type: 'textarea', name: 'notes', placeholder: 'Enter any additional notes', label: 'Notes', rows: 2 }
  ];

  useEffect(() => {
    if (supplierId) {
      dispatch(getSingleSupplier(supplierId));
    }
    dispatch(getShipping());
  }, [supplierId, dispatch]);

  useEffect(() => {
    setCountries(countriesList?.map((country) => ({ label: country?.name, value: country?.name })));
  }, []);

  useEffect(() => {
    setShipping(supplierData?.allShipping?.map((shipping) => ({ label: shipping, value: shipping })));
  }, [supplierData?.allShipping]);

  useEffect(() => {
    if (supplierId && supplierData?.singleSupplier) {
      const {
        Name: name,
        ContactName: contactName,
        ContactLastname: contactLastName,
        Country: country,
        Email: email,
        Notes: notes,
        Shipping: shipping,
        SupplierID: id,
        Web: website
      } = supplierData.singleSupplier || {};

      setSupplierDetails({
        name,
        contactName,
        contactLastName,
        email,
        notes,
        shipping,
        id,
        website,
        country
      });
    } else {
      setSupplierDetails(initialState);
    }
  }, [supplierData?.singleSupplier]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setSupplierDetails({ ...supplierDetails, [name]: value });
    setErrors({ ...errors, [name]: '' });
  };

  const handleSelectChange = (name, value) => {
    setSupplierDetails({ ...supplierDetails, [name]: value });
    setErrors({ ...errors, [name]: '' });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const rules = {
      name: { required: true, label: 'Name' },
      contactName: { required: true, label: 'Contact First Name' },
      contactLastName: { required: true, label: 'Contact Last Name' },
      email: { required: true, label: 'Email', type: 'email' },
      country: { required: true, label: 'Country' },
      website: { type: 'url', label: 'Website' },
      shipping: { required: true, label: 'Shipping' }
    };
    const validation = validate(supplierDetails, rules);
    setErrors(validation);

    if (!Object.keys(validation).length) {
      const { name, contactName, contactLastName, country, email, notes, id, website, shipping } = supplierDetails || {};
      const payload = {
        Name: name,
        ContactName: contactName,
        ContactLastname: contactLastName,
        Country: country,
        Email: email,
        Notes: notes || '',
        Shipping: shipping,
        Web: website || '',
        navigate: navigate
      };
      if (id) {
        dispatch(updateSupplierMaintenanace({ ...payload, SupplierID: id }));
      } else {
        dispatch(addSupplierMaintenanace(payload));
      }
    }
  };

  const handleAutocompleteChange = (name) => (event, newValue) => {
    const value = newValue ? newValue.value : '';
    handleSelectChange(name, value);
  };

  const renderField = (fieldConfig) => {
    const { type, name, placeholder, label, value, rows, items } = fieldConfig || {};
    switch (fieldConfig.type) {
      case 'text':
      case 'email':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="subtitle1" gutterBottom>
              {label}
            </Typography>
            <InputField
              type={type}
              name={name}
              placeholder={placeholder}
              value={supplierDetails[name] || ''}
              onChange={handleChange}
              errors={errors}
              style={styles.inputField}
              fullWidth
            />
          </Grid>
        );

      case 'select':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="subtitle1" gutterBottom>
              {label}
            </Typography>
            <SelectComponent
              name={name}
              onSelectChange={(e) => handleSelectChange(name, e?.target?.value)}
              value={supplierDetails[name] || 'placeholder'}
              items={items}
              placeholder={placeholder}
              error={errors[name]}
              style={{ color: !supplierDetails[name] && 'rgb(168, 166, 166)' }}
            />
            {errors[name] && <ErrorMessage message={errors[name]} style={styles.errorText} />}
          </Grid>
        );

      case 'autocomplete':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="subtitle1" gutterBottom>
              {label}
            </Typography>
            <DynamicAutocomplete
              options={items || []}
              // label={label}
              placeholder={placeholder}
              value={items?.find((item) => item.value === supplierDetails[name]) || null}
              onChange={handleAutocompleteChange(name)}
              getOptionLabel={(option) => option?.label || ''}
              isLoading={name === 'country' ? supplierData?.loading : name === 'state' ? supplierData?.loading : false}
            />
            {errors[name] && <ErrorMessage message={errors[name]} style={styles.errorText} />}
          </Grid>
        );

      case 'textarea':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="subtitle1" gutterBottom>
              {label}
            </Typography>
            <TextAreaComponent
              name={name}
              placeholder={placeholder}
              value={supplierDetails[name] || ''}
              onChange={(name, e) => handleSelectChange(name, e?.target?.value)}
              minRows={rows}
              className="name-text-field"
              style={styles.textarea}
              error={errors[name]}
            />
            {errors[name] && <ErrorMessage message={errors[name]} style={styles.errorText} />}
          </Grid>
        );

      default:
        return null;
    }
  };

  const cancel = () => {
    dispatch(setSingleSupplierData({}));
    navigate(supplierUrl);
  };

  const { loading } = supplierData || {};
  return (
    <Grid>
      {loading && <Loader />}
      <Box component="form" onSubmit={handleSubmit} style={styles.formContainer}>
        <MainCard className="maincard-boder" boxShadow={true} style={styles.card}>
          <Typography variant="h4" component="h1" gutterBottom color="secondary">
            {supplierId ? 'Update Supplier' : 'New Supplier'}
          </Typography>

          <Grid container spacing={3} mt={1}>
            {fieldConfigurations.map((config) => renderField(config))}
          </Grid>
        </MainCard>

        <MainCard sx={{ mt: 3 }} className="maincard-boder" boxShadow={true} style={styles.card}>
          <Grid item style={styles.buttonGroup}>
            <Button variant="outlined" color="secondary" onClick={() => cancel()}>
              Cancel
            </Button>
            <Button variant="contained" color="primary" type="submit" disabled={loading}>
              Save
            </Button>
          </Grid>
        </MainCard>
      </Box>
    </Grid>
  );
};

export default AddEditSupplier;
