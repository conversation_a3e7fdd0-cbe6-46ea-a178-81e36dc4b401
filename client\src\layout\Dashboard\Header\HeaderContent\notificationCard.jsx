import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, CardContent, Typo<PERSON>, <PERSON>, Badge, Button } from '@mui/material';
import AvatarComponent from 'pages/component/avatar';
import { getDateWithTime } from 'utils/helper';
import { useNavigate } from 'react-router';
import { ReservedSingleRfqPageUrl, reserveRfqPageUrl } from 'utils/constant';

const NotificationCard = ({ title, message, date, onClick, sender, read, RFQID }) => {
  const [showMore, setShowMore] = useState(false);
  const navigate = useNavigate();
  const maxLength = 200;

  const handleShowMoreClick = (e) => {
    e.stopPropagation();
    setShowMore(!showMore);
  }; 

  const handleOpenRFQ = () => {
    navigate(`${ReservedSingleRfqPageUrl}/${RFQID}`);
  } 

  const truncatedTitle = title.length > maxLength ? title.slice(0, maxLength) + '...' : title;

  return (
    <Card style={styles.card}>
      <CardContent style={styles.cardContent}> 
        <div style={styles.titleCard}>
          <Typography variant="h6" component="div" gutterBottom style={styles.title}>
            {showMore ? title : truncatedTitle}
          </Typography> 

          {RFQID && <Link component="button" variant="body2" onClick={handleOpenRFQ}>
            <Button>Open RFQ</Button>
          </Link>}
          <div style={styles.showMoreBtn}>
            {title.length > maxLength && (
              <Link
                component="button"
                variant="body2"
                onClick={handleShowMoreClick}
                style={styles.showMoreLink}

              >
                {showMore ? 'Show less' : 'Show more'}
              </Link>
            )}
          </div>
        </div>
        <div style={styles.contentCard}>
          <AvatarComponent src={sender || ''} alt={sender || ''} size='sm' />
          <div style={styles.innerCard} >
            <div>
              <Typography variant="body2" color="textSecondary" style={styles.message}>
                {sender}
              </Typography>
              <Typography variant="caption" color="textSecondary" display="block" gutterBottom style={styles.date}>
                {getDateWithTime(date)}
              </Typography>
            </div>
            <div>
              {!read && <Badge variant='dot' color='primary' />}
            </div>

          </div>

        </div>
      </CardContent>
    </Card>
  );
};

export default NotificationCard;

const styles = {
  card: {
    margin: '10px',
    marginBottom: '16px',
    boxShadow: '0 3px 8px rgba(0, 0, 0, 0.1)',
    borderRadius: '12px',
    transition: 'transform 0.2s, box-shadow 0.2s',
    cursor: 'pointer',
    backgroundColor: '#ffffff',
  },
  cardContent: {
    padding: 0,
  },
  titleCard: {
    backgroundColor: '#f5f5f5',
    padding: '16px',
    borderBottom: '1px solid #e0e0e0',
  },
  title: {
    color: '#333333',
    fontWeight: 'bold',
  },
  contentCard: {
    backgroundColor: '#fafafa',
    width: '100%',
    padding: '12px 16px',
    display: 'flex',
    gap: '12px',
  },
  message: {
    color: '#555555',
  },
  date: {
    color: '#888888',
  },
  showMoreLink: {
    marginLeft: '8px',
    cursor: 'pointer',
    textDecoration: 'none',
  },
  showMoreBtn: {
    display: 'flex',
    justifyContent: 'center'
  },
  innerCard: {
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between'
  }
};
