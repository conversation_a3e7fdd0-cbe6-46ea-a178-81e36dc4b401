const constants = require("../constants");
const nodemailer = require("nodemailer");
/*
option = {
  to : "email", 
  subject : "subetct text",
  html : "html body"
}
*/
const sendMail = async (options) => {
  let transporter = nodemailer.createTransport({
    host: "mail.smtp2go.com",
    port: 2525,
    secure: false,
    auth: {
      user: constants.SMTP_USER,
      pass: constants.SMTP_PASS,
    },
  });

  let mailOptions = {
    from: constants.SMTP_EMAIL,
    to: options.to,
    cc: options.cc || undefined,
    subject: options.subject,
    html: options.html,
  };

  try {
    let info = await transporter.sendMail(mailOptions);
    return info;
  } catch (err) {
    console.log("Error Occurred While Sending Mail - ", err);
  }
};

module.exports = {
  sendMail,
};
