/* eslint-disable no-empty */
/* eslint-disable prettier/prettier */
/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import {
  Box,
  Alert,
  But<PERSON>,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  Typography,
  Stack
} from '@mui/material';
import TableComponent from 'pages/component/table/table';
import MyOfferBadge from './myOfferBadge';
import ActionButton from 'pages/component/actionButton';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import pdflogo from '../../../assets/images/users/pdflogo.png';
import { getMySingleOfferDetail, saveOrderPurchaseAction, updateOfferStatusAction } from 'redux/reducers/myOffersReducer';
import { useDispatch, useSelector } from 'react-redux';
import PDFGenerator from './generatePdf';
import { isCountryManager, showAlert } from 'utils/helper';
import { convertDateToStringFormat } from 'utils/helper';
import PDFList from './pdfList';
import { useNavigate } from 'react-router';
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import { updateGenerateOfferPageUrl, viewOfferPageUrl } from 'utils/constant';
import SpanishTermsAndConditions from './spanishTerms&conditions';
import TableApiPaginationComponent from 'pages/component/table/tableApiPagination';
import AlertDialog from 'pages/component/dialogbox';
import OrderPurChaseFile from './orderPurchaseFile';
import { validate } from 'pages/component/validation';
import { get } from 'lodash';
import SelectComponent from 'pages/component/selectComponent';
import { sortingTypes } from './constant';
import { duplicateOffer } from 'redux/reducers/offerReducer';
import { LoadingButton } from '@mui/lab';
import DatePickerComponent from 'pages/component/dateField';
import DynamicLegalTerms from './DynamicLegalTerms';
import EnglishTermsAndConditions from './englistTerms&Condition';
import { getLegalTerms } from 'redux/reducers/settingReducer';

const OrderInitialValues = {
  OC_Number: '',
  OC_File: '',
  Amount_CLP: 0,
  Amount_USD: 0
};

const MyOfferTable = ({
  filters,
  applyFilterOnEnter,
  handlFilterChange,
  rows,
  page,
  handleSearch,
  onPageChange,
  onPageLimitChange,
  handleSort,
  totalCount,
  allRows,
  loading
}) => {
  const pdfRef = useRef();
  const termsRef = useRef();
  const dispatch = useDispatch();
  const singleOffer = useSelector((state) => state.myOffer);
  const offerData = useSelector((state) => state.offer);
  const [currentOfferId, setCurrentOfferId] = useState('');
  const [currentRfqId, setCurrentRfqId] = useState('');
  const [duplicateOfferData, setDuplicateOfferData] = useState({});
  const [selectedOffer, setSelectedOffer] = useState({});
  const [open, setOpen] = useState(false);
  const [isOfferDuplicateOpen, setOfferDuplicateOpen] = useState(false);
  const [orderDetail, setOrderDetail] = useState(OrderInitialValues);
  const [fileName, setFileName] = useState('');
  const [pdfDetail, setPdfDetail] = useState({});
  const [errors, setErrors] = useState({});
  const [pendingPdfId, setPendingPdfId] = useState(null);
  const navigate = useNavigate();

  const closeDuplicateModal = () => {
    setCurrentOfferId('');
    setCurrentRfqId('');
    setOfferDuplicateOpen(false);
  };
  const openDuplicateModal = () => setOfferDuplicateOpen(true);

  useEffect(() => {
    if (pendingPdfId && pdfDetail?.id === pendingPdfId) {
      setTimeout(() => {
        generatePdf(pdfDetail?.id);
        setPendingPdfId(null);
      }, 100);
    }
  }, [pdfDetail, pendingPdfId]);

  const headers = [
    {
      name: 'rfqId',
      type: 'RFQ Id',
      title: 'RFQ ID',
      sortingactive: true
    },
    {
      name: 'name',
      type: 'text',
      title: 'Name',
      sortingactive: true
    },
    {
      name: 'portal',
      type: 'text',
      title: 'Portal',
      sortingactive: true
    },
    {
      name: 'number',
      type: 'text',
      title: 'Number',
      sortingactive: true
    },
    {
      name: 'date',
      type: 'date',
      title: 'Date',
      sortingactive: true
    },
    {
      name: 'deliver',
      type: 'date',
      title: 'Deliver',
      sortingactive: true
    },
    {
      name: 'client',
      type: 'text',
      title: 'Client',
      sortingactive: true
    },
    {
      name: 'totalOffer',
      type: 'price',
      title: 'Total Offer',
      sortingactive: true
    },
    {
      name: 'kam',
      type: 'text',
      title: 'User',
      sortingactive: true
    },
    {
      name: 'url',
      type: 'link',
      title: 'Url',
      sortingactive: true
    },
    {
      name: 'pdf',
      btnType: 'pdf',
      type: 'actions',
      title: 'Files',
      sortingactive: false,
      component: PDFList,
      buttonOnClick: (type, id) => {
        startGeneratingPdf(id);
      }
    },
    {
      name: 'status',
      type: 'actions',
      title: 'Status',
      sortingactive: false,
      disableBtn: loading || false,
      minWidth: '150px',
      component: MyOfferBadge,
      buttonOnClick: (data, id) => {
        if (data?.badgeContent === 'Won') {
          let singleOfferDetail = rows?.find((r) => r?.id === id);
          setSelectedOffer({ ...singleOfferDetail });
          setOpen(true);
        } else {
          updateStatus(data, id);
        }
      }
    },
    {
      name: 'actions',
      btnType: 'multipleButton',
      type: 'actions',
      title: 'Actions',
      sortingactive: false,
      component: ActionButton,
      multipleButtons: [
        {
          type: 'icon',
          icon: <RemoveRedEyeOutlinedIcon fontSize="16px" />,
          buttonOnClick: (type, rowData) => {
            navigate(`${viewOfferPageUrl}/${rowData?.rfqId}/${rowData?.offerId}`);
          },
          color: 'primary',
          // isVisible: (rowData) => {
          //   return !isCountryManager();
          // },
          tooltip: 'View'
        },
        {
          type: 'icon',
          icon: <EditOutlinedIcon fontSize="16px" />,
          isVisible: (rowData) => {
            return !isCountryManager();
          },
          buttonOnClick: (type, rowData) => {
            navigate(`${updateGenerateOfferPageUrl}/${rowData?.rfqId}/${rowData?.offerId}`);
          },
          color: 'secondary',
          tooltip: 'Edit'
        },
        {
          type: 'icon',
          icon: <ContentCopyIcon fontSize="16px" />,
          isVisible: (rowData) => {
            if (rowData && get(rowData, 'status') === 'VOID') {
              return false;
            } else {
              return !isCountryManager();
            }
          },
          buttonOnClick: (type, rowData) => {
            // navigate(`${viewOfferPageUrl}/${rowData?.rfqId}/${rowData?.offerId}`);
            if (get(rowData, 'id')) {
              setCurrentOfferId(get(rowData, 'id').toString());
            }
            if (get(rowData, 'rfqId')) {
              setCurrentRfqId(get(rowData, 'rfqId').toString());
            }
            openDuplicateModal();
          },
          color: 'primary',
          tooltip: 'Copy'
        }
      ]
    }
  ];

  const updateStatus = (data, id) => {
    const { badgeContent, offerId } = data || {};
    const row = rows.find((r) => r?.id === id);
    if (row?.offerId) {
      let payload = {
        rfqId: row?.rfqId?.toString(),
        offerId: row?.offerId?.toString() || '',
        status: badgeContent,
        filters: filters
      };
      dispatch(updateOfferStatusAction(payload));
    } else {
      showAlert(dispatch, false, 'offer Id is required', true);
    }
  };

  const generatePdf = async (id) => {
    const row = rows.find((r) => r.id === id);

    const offerElement = pdfRef.current;
    const termsElement = termsRef.current;

    try {
      offerElement.style.position = 'absolute';
      offerElement.style.left = '-9999px';
      offerElement.style.visibility = 'visible';

      const offerCanvas = await html2canvas(offerElement);
      const offerImgData = offerCanvas.toDataURL('image/png');
      const pdf = new jsPDF({
        format: 'a4',
        unit: 'mm'
      });

      const img = new Image();
      img.src = pdflogo;

      img.onload = async () => {
        const topPadding = 10;
        const rightPadding = 10;
        const leftPadding = 5;

        pdf.addImage(img, 'PNG', leftPadding, topPadding, 30, 30, undefined, 'FAST');

        const offerWidth = offerCanvas.width;
        const offerHeight = offerCanvas.height;
        const offerRatio = offerWidth / offerHeight;
        const pdfWidth = 210 - rightPadding - leftPadding;
        const pdfHeight = pdfWidth / offerRatio;

        const pageHeight = 297;
        let heightLeft = pdfHeight;
        let position = topPadding;

        // Add offer details to PDF
        pdf.addImage(offerImgData, 'PNG', leftPadding, position, pdfWidth, pdfHeight, undefined, 'FAST');
        heightLeft -= pageHeight - position;

        // Add more pages for offer details if needed
        while (heightLeft > 0) {
          pdf.addPage();
          position = 0;
          heightLeft -= pageHeight;
          pdf.addImage(offerImgData, 'PNG', leftPadding, position, pdfWidth, pdfHeight, undefined, 'FAST');
        }

        termsElement.style.position = 'absolute';
        termsElement.style.left = '-9999px';
        termsElement.style.visibility = 'visible';

        const termsCanvas = await html2canvas(termsElement);
        const termsImgData = termsCanvas.toDataURL('image/png');
        const termsWidth = termsCanvas.width;
        const termsHeight = termsCanvas.height;
        const termsRatio = termsWidth / termsHeight;
        const termsPdfHeight = pdfWidth / termsRatio;

        pdf.addPage();
        position = topPadding;
        pdf.addImage(termsImgData, 'PNG', leftPadding, position, pdfWidth, termsPdfHeight, undefined, 'FAST');

        let termsHeightLeft = termsPdfHeight - (pageHeight - position);

        while (termsHeightLeft > 0) {
          pdf.addPage();
          position = 0;
          pdf.addImage(termsImgData, 'PNG', leftPadding, position, pdfWidth, termsPdfHeight, undefined, 'FAST');
          termsHeightLeft -= pageHeight;
        }

        pdf.save(`${row?.rfqId || 'Offer'}.pdf`);

        offerElement.style.position = '';
        offerElement.style.left = '';
        offerElement.style.visibility = 'hidden';

        termsElement.style.position = '';
        termsElement.style.left = '';
        termsElement.style.visibility = 'hidden';
      };
    } catch (error) {
      showAlert(dispatch, false, 'Error generating PDF', true);
      console.error('Error generating PDF:', error);
    }
  };

  const startGeneratingPdf = async (id) => {
    const row = rows.find((r) => r.id === id);
    const response = await dispatch(getMySingleOfferDetail(row?.offerId));
    const { success, data } = response?.payload || {};
    if (success) {
      let languageSuccess = true;
      if (data?.Language) {
        const lang = data?.Language === 'English' ? 'EN' : 'ES';
        let response = await dispatch(getLegalTerms(lang));
        languageSuccess = response?.payload?.success || false;
      }
      if (languageSuccess) {
        setPdfDetail({ ...data, id: data?.OfferID });
        setPendingPdfId(data?.OfferID);
      }
    } else {
      alert('PDF generation failed');
    }
  };

  const handleClose = () => {
    setOpen(false);
    setOrderDetail(OrderInitialValues);
    setErrors({});
    setFileName('');
  };

  const SaveOrderPurchase = async () => {
    let rules = {
      OC_Number: { required: true, label: 'Order Number' },
      OC_File: { required: true, label: 'Order Purchase File' },
      Amount_CLP: { required: true, label: 'Amount CLP', number: true },
      Amount_USD: { required: true, label: 'Amount USD', number: true }
    };
    const validationErrors = validate(orderDetail, rules);

    if (Object?.keys(validationErrors)?.length === 0) {
      let payload = {
        ...orderDetail,
        OC_File: orderDetail?.OC_File[0],
        status: 'Won',
        offerId: selectedOffer?.id,
        rfqId: selectedOffer?.rfqId,
        Client: selectedOffer?.client?.toString(),
        filters: filters
      };

      let response = await dispatch(saveOrderPurchaseAction(payload));

      if (get(response, 'payload.success')) {
        handleClose();
      }
    } else {
      setErrors({ ...validationErrors });
    }
  };
  const handleDuplicateOffer = async () => {
    if (duplicateOfferData?.expirationDate) {
      if (currentRfqId && currentOfferId) {
        let payload = {
          rfqId: currentRfqId,
          offerId: currentOfferId,
          expirationDate: convertDateToStringFormat(new Date(duplicateOfferData?.expirationDate || null), true) || ''
        };
        dispatch(duplicateOffer(payload));
      }
    } else {
      showAlert(dispatch, false, 'Please select expiration date', true);
    }
  };

  const handleChangeDuplicateOffer = (name, value) => {
    setDuplicateOfferData({ ...duplicateOfferData, [name]: value });
  };

  const buttons = [
    { label: 'Cancel', onClick: () => handleClose(), variant: 'outlined', color: 'error', disabled: loading },
    { label: 'Submit', onClick: () => SaveOrderPurchase(), variant: 'contained', color: 'primary', disabled: loading }
  ];
  return (
    <div>
      <AlertDialog
        content=""
        onAgree={() => {}}
        cancel={() => handleClose()}
        Component={
          <>
            <div style={{ minWidth: '400px' }}>
              <OrderPurChaseFile orderInfo={[orderDetail, setOrderDetail, errors, setFileName, fileName]} />
            </div>
          </>
        }
        open={open}
        buttons={buttons}
        borderRadius="20px"
      />
      <Dialog
        scroll="paper"
        onClose={closeDuplicateModal}
        open={isOfferDuplicateOpen}
        fullWidth
        maxWidth="md"
        persist
        PaperProps={{
          sx: {
            width: '100%',
            maxWidth: '700px !important'
          }
        }}
        // className={className}
      >
        <DialogTitle sx={{ mt: 1, display: 'flex', direction: 'row', justifyContent: 'space-between' }}>
          <Typography variant="h5" sx={{ fontWeight: 700, color: '#3c4555' }}>
            Duplicate Offer
          </Typography>
        </DialogTitle>
        <DialogContent dividers={true}>
          <>
            {get(offerData, 'duplicateResponseData') && get(offerData, 'duplicateResponseData.offerId') === currentOfferId ? (
              <Stack>
                <Typography>
                  <strong>RFQ Id:</strong> {get(offerData, 'duplicateResponseData.data.rfqId')}
                </Typography>
                <Typography>{get(offerData, 'duplicateResponseData.message')}</Typography>
              </Stack>
            ) : (
              <>
                <Alert icon={false} color="warning">
                  Warning: The current offer will be updated to Void
                </Alert>
                <Box ml={2}>
                  <Typography variant="body1" color={'secondary'}>
                    Expiration Date
                  </Typography>
                  <DatePickerComponent
                    startDateHandleChange={(date) => handleChangeDuplicateOffer('expirationDate', date)}
                    startDate={duplicateOfferData?.expirationDate}
                    fullWidth
                    disablePastDate={true}
                  />
                </Box>
              </>
            )}
          </>
        </DialogContent>
        <DialogActions sx={{ mt: 2, p: 2, display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
          <Button type="button" onClick={closeDuplicateModal} color="error" variant="outlined">
            {get(offerData, 'duplicateResponseData') && get(offerData, 'duplicateResponseData.offerId') === currentOfferId
              ? 'Close'
              : 'Cancel'}
          </Button>
          <LoadingButton
            disabled={get(offerData, 'duplicateResponseData') && get(offerData, 'duplicateResponseData.offerId') === currentOfferId}
            loading={offerData?.isDuplicateOfferLoading}
            variant="contained"
            onClick={handleDuplicateOffer}
          >
            OK
          </LoadingButton>
        </DialogActions>
      </Dialog>
      <Card>
        <Divider />
        <CardContent className="myoffer-card-content">
          <Box minWidth="250px" position="absolute" top="30px" right="25px">
            <SelectComponent
              value={filters?.find((filter) => filter?.type === 'sort')?.data}
              onChange={(e) => handlFilterChange(e)}
              items={sortingTypes}
            />
          </Box>

          <TableApiPaginationComponent
            columns={headers}
            rows={rows}
            title="MyOfferTable"
            titleLink="dashboard"
            generatePdf={(id) => startGeneratingPdf(id)}
            pageNo={page}
            enablePagination={true}
            enableSearch={true}
            totalCount={totalCount}
            allRows={allRows || []}
            isBackendPagination={true}
            maxHeight={'100%'}
            applyFilterOnKeyDown={applyFilterOnEnter}
            onPageChange={(page) => onPageChange(page)}
            handleSort={handleSort}
            onPageLimitChange={(page) => onPageLimitChange(page)}
            handleSearch={(filteredData, search) => handleSearch(filteredData, search)}
          />
        </CardContent>
      </Card>
      <div ref={pdfRef} className="pdf-content" key={pdfDetail?.RFQ_ID + pdfDetail?.Language}>
        <PDFGenerator singleOffer={pdfDetail} />
      </div>
      <div ref={termsRef} className="pdf-content" key={pdfDetail?.RFQ_ID + pdfDetail?.Language + 'terms'}>
        <DynamicLegalTerms language={pdfDetail?.Language} />
      </div>
    </div>
  );
};

export default MyOfferTable;
