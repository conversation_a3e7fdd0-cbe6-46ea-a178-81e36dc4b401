import DatePickerComponent from 'pages/component/dateField';
import PropTypes from 'prop-types';
const DateFilter = ({ data, handleFilterChange,classname,label }) => {
  return (
    <>
      <div className='filter-input'>
        <DatePickerComponent startDateHandleChange={handleFilterChange} classname={classname} startDate={data?.data} label={label} />
      </div>
    </>
  );
};
DateFilter.propTypes = {
  data: PropTypes.object.isRequired,
  handleFilterChange: PropTypes.func.isRequired,
  classname: PropTypes.func,
  
};
export default DateFilter;
