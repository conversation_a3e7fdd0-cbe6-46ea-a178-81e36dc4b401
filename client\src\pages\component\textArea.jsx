import React from 'react';
import { TextareaAutosize } from '@mui/material';
import PropTypes from 'prop-types';
const TextAreaComponent = ({ value, error, style, label, name, onChange, className, type, fullWidth, placeholder, stylename, minRows, ...other }) => {
  const borderColor = error ? '#ff4d4f' : '';
  const styles = { ...style, color: '#7b7b7bff', borderColor: borderColor }
  return (
    <TextareaAutosize
      label={label}
      placeholder={placeholder}
      className={`${className} textarea`}
      value={value}
      onChange={(e) => onChange(name, e)}
      minRows={minRows}
      {...other}
      style={styles}
    />
  );
};


TextAreaComponent.propTypes = {
  value: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  otherClass: PropTypes.string,
  type: PropTypes.string,
  name: PropTypes.string,
  fullWidth: PropTypes.bool,
  placeholder: PropTypes.string,
  other: PropTypes.object // assuming other is an object
};
export default TextAreaComponent