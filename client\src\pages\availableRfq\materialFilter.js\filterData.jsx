export const reservedStatus = {
  heading: 'Irrelevant',
  type: 'text',
  field: 'r.CurrentStatus',
  data: 'IRRELEVANT'
};

export const filterData = [
  {
    heading: 'Client',
    type: 'text',
    field: 'r.Company_Name',

    data: ''
  },
  // {
  //   heading: 'RFQ Date',
  //   type: 'date',
  //   field: 'RFQ_Date',

  //   data: null
  // },
  // {
  //   heading: 'Delivery Date',
  //   type: 'date',
  //   field: 'Delivery_Date',

  //   data: null
  // },
  // {
  //   heading: 'Deadline Date',
  //   type: 'date',
  //   field: 'Deadline',

  //   data: null
  // },
  {
    heading: 'Search',
    type: 'text',
    field: 'r.Company_Name',
    data: ''
  },

  {
    heading: "RFQ ID",
    type: "text",
    field: "r.RFQ_ID",
    data: ""
  },
  {
    heading: 'Options',
    type: 'array',
    showClearBtn: true,
    field: 'rs.CurrentStatus',
    data: [
      {
        label: 'Not Relevant',
        value: 'IRRELEVANT',
        select: false
      }
    ]
  },
  {
    heading: 'Priority',
    type: 'array',
    payloadHeading: 'isPartNumberAvailable',
    field: 'rc.Part_Number_Available',
    showClearBtn: false,
    data: [
      {
        label: 'Match',
        value: 'isPartNumberAvailable',
        select: false
      },
    ]
  },

  {
    heading: '',
    type: 'array',
    payloadHeading: 'isHotBrand',
    field: 'rc.Hot_Brand',
    showClearBtn: false,
    data: [
      {
        label: 'Is Hot Brand',
        value: 'isHotBrand',
        select: false
      },
    ]
  },

  {
    heading: '',
    payloadHeading: 'isPartNumberDetected',
    type: 'radio',
    field: 'rc.Part_Number_Detected',
    data: 'noFilter',

    options: [
      {
        label: 'Display all RFQ',
        value: 'noFilter',
        select: false
      },
      {
        label: 'Display only RFQs with part number detected.',
        value: 'isPartNumberDetected',
        select: false
      },
      {
        label: 'Display only RFQs with part number not detected.',
        value: 'isNotPartNumberDetected',
        select: false
      },
    ]
  },
  {
    heading: 'Dates',
    payloadHeading: 'Options',
    type: 'radio',
    field: 'r.deadline',
    data: 'noFilter',

    options: [
      {
        label: 'All',
        value: 'noFilter',
        select: false
      },
      {
        label: 'Today',
        value: 'today',
        select: false
      },
      {
        label: 'In the next 3 days',
        value: 'next3days',
        select: false
      },
      {
        label: 'In the next 7 days',
        value: 'next7days',
        select: false
      }
    ]
  }
];
export const ReserveRFQFilterData = [
  {
    heading: 'Client',
    type: 'text',
    field: 'r.Company_Name',

    data: ''
  },
  {
    heading: 'Search',
    type: 'text',
    field: 'r.Company_Name',
    data: ''
  },
  {
    heading: "RFQ ID",
    type: "text",
    field: "r.RFQ_ID",
    data: ""
  },
  {
    heading: 'Options',
    type: 'array',
    field: 'rs.CurrentStatus',
    data: [
      {
        label: 'Not Relevant',
        value: 'IRRELEVANT',
        select: false
      }
    ]
  },

  {
    heading: 'Dates',
    type: 'radio',
    field: 'r.deadline',
    data: 'noFilter',
    payloadHeading: 'Options',

    options: [
      {
        label: 'All',
        value: 'noFilter',
        select: false
      },
      {
        label: 'Today',
        value: 'today',
        select: false
      },
      {
        label: 'In the next 3 days',
        value: 'next3days',
        select: false
      },
      {
        label: 'In the next 7 days',
        value: 'next7days',
        select: false
      }
    ]
  },
  {
    heading: 'Priority',
    type: 'array',
    payloadHeading: 'isPartNumberAvailable',
    field: 'rc.Part_Number_Available',
    showClearBtn: false,
    data: [
      {
        label: 'Match',
        value: 'isPartNumberAvailable',
        select: false
      },
    ]
  },

  {
    heading: '',
    type: 'array',
    payloadHeading: 'isHotBrand',
    field: 'rc.Hot_Brand',
    showClearBtn: false,
    data: [
      {
        label: 'Is Hot Brand',
        value: 'isHotBrand',
        select: false
      },
    ]
  },

  {
    heading: '',
    payloadHeading: 'isPartNumberDetected',
    type: 'radio',
    field: 'rc.Part_Number_Detected',
    data: 'noFilter',

    options: [
      {
        label: 'Display all RFQ',
        value: 'noFilter',
        select: false
      },
      {
        label: 'Display only RFQs with part number detected.',
        value: 'isPartNumberDetected',
        select: false
      },
      {
        label: 'Display only RFQs with part number not detected.',
        value: 'isNotPartNumberDetected',
        select: false
      },
    ]
  },
];
