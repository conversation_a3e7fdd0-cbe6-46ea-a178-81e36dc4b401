export const REQUESTEDPRICE = 'requested-price'
export const FORMREQUEST = 'FormRequest'
export const PRICETABLE = 'PriceTable'
export const ARRAY_TYPE = 'arrayType'

export const checkIsAllBrandSelected = (tableData) => {
  if (!tableData || tableData.length === 0) return false;
  return tableData.every((brand) => brand.select === true);
};

export const filterData =
  [
    {
      heading: 'Brand',
      type: 'text',
      field: 'Brand',

      data: ''
    },
    {
      heading: "RFQ ID",
      type: "text",
      field: "RFQ_ID",
      data: ""
    },
    {
      heading: 'Due Date',
      type: 'date',
      field: 'Deadline',

      data: null
    },
    {
      heading: 'Date Requested',
      type: 'date',
      field: 'RFQ_Date',

      data: null
    },
    {
      heading: 'Requested Price',
      type: 'slider',
      field: 'requestedCount',

      data: 0
    },
    {
      heading: 'Received Price',
      type: 'slider',
      field: 'respondedCount',
      data: 0
    },
  ];


export const defaultFilterPayload = {
  limit: 10,
  page: 1,
  filterData: []
}

export const addSupplierFilter = (filters, newFilter, key) => {
  let filterData = filters || [];

  // Check if an entry with heading: "search" already exists
  const searchIndex = filterData.findIndex(item => item.field === key);

  if (searchIndex !== -1) {
    // Update the existing entry
    filterData[searchIndex].data = newFilter?.data;
  } else {
    // Add new entry if not found
    filterData.push(newFilter);
  }
  return filterData
}