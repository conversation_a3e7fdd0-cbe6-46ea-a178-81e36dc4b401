const {
  isEmpty,
  setDate,
  generateID,
  round,
  applyFloor,
} = require("../utils/misc.util");
const { sendNotification } = require("../utils/notifications.util");
const { apiError, apiResponse, apiHandler } = require("../utils/api.util");
const {
  NOT_FOUND,
  FETCH,
  STATUS_SUCCESS,
  ADD_SUCCESS,
  DELETE_SUCCESS,
  UNAUTHORIZED,
  UPDATE_SUCCESS,
  SENT_ERROR,
} = require("../utils/message.util");
const {
  mainDataset,
  bigQueryClient,
  scrapedDataset,
  landingZoneDataset,
} = require("../db");
const {
  DATASET_ID_MAIN,
  DATASET_ID_SCRAPE,
  DATASET_ID_LANDINGZONE,
  BASE_URL,
  DATASET_ID_DASHBOARDS,
  DATASET_ID_AUTOMATION,
} = require("../constants");

const RFQStatus = [
  "NEW",
  "RESERVED",
  "QUOTING",
  "ANSWERED",
  "FORGOTTEN",
  "WON",
  "LOST",
  "EXPIRED",
  "IRRELEVANT",
  "VOID",
];

const getQuoteDetails = async (rfqId) => {
  try {
    // Fetch RFQ data from the BigQuery API
    const [existingRFQ] = await bigQueryClient.query({
      query: `
        WITH quote_data AS (
          SELECT
            q.MaterialID,
            ARRAY_AGG(
              STRUCT(
                q.QuoteID,
                q.RFQID AS RFQID,
                q.MaterialID AS MaterialID,
                q.SupplierID AS supplierID,
                q.QuoteDate,
                q.UnitPrice AS unitPrice,
                q.UnitCurrency,
                q.Quantity AS quantity,
                q.ShippingCost,
                q.Tax,
                q.TotalCost,
                q.OfferedPrice,
                CASE 
                WHEN q.Quantity = 0 THEN NULL 
                ELSE ROUND(q.OfferedPrice / q.Quantity, 2) 
                END AS unitOfferPrice, 
                q.DeliveryDate,
                'REGISTERED' AS status,
                q.KAM,
                q.Offered,
                q.Notes AS notes,
                q.LeadTime,
                q.Weight
              )
            ) AS supplier_json_array
          FROM
            ${DATASET_ID_MAIN}.Quotes AS q
          GROUP BY
            q.MaterialID
        ),
        material_data AS (
          SELECT
            r.RFQ_ID,
            r.RFQ_Number,
            r.RFQ_Name,
            r.RFQ_Date,
            r.Delivery_Date,
            r.Deadline,
            ar.RFQID AS RFQID_2,
            ar.Status,
            ar.logMessage,
            ar.logDate,
            r.Portal,
            r.URL,
            r.Company_Name,
            c.ClientID,
            c.Name,
            u.FirstName,
            u.LastName,
            u.UserID,
            rs.CurrentStatus,
            m.Material_ID AS MaterialID,
            m.RFQ_ID AS RFQID,
            pc.ConfirmedPartNumber AS Part_Number,
            m.Quantity_Required AS Quantity_Required,
            m.Material_Description AS Material_Description,
            mw.WeightInKilos AS prepopulatedWeight,
            m.showSuppliers,
            m.orders,
            pc.ConfirmedBrand AS brand,
            cr.ID AS centralizedRequestID,
            (
              SELECT supplier_json_array
              FROM quote_data
              WHERE quote_data.MaterialID = m.Material_ID
            ) AS supplier_json_array
          FROM
            ${DATASET_ID_SCRAPE}.RFQ AS r
          LEFT JOIN ${DATASET_ID_SCRAPE}.Material AS m
            ON m.RFQ_ID = r.RFQ_ID
          LEFT JOIN ${DATASET_ID_MAIN}.Predictions_Confirmations AS pc
            ON pc.MaterialID = m.Material_ID AND pc.RFQID = r.RFQ_ID
          LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status AS rs
            ON r.RFQ_ID = rs.RFQID
          LEFT JOIN ${DATASET_ID_MAIN}.Users AS u
            ON u.UserID = rs.AssignedUser
          LEFT JOIN ${DATASET_ID_MAIN}.Clients AS c
            ON UPPER(TRIM(r.Company_Name)) = UPPER(TRIM(c.Name)) 
               OR UPPER(TRIM(r.ClientID)) = UPPER(TRIM(c.ClientID))
          LEFT JOIN ${DATASET_ID_MAIN}.Centralized_Requests AS cr
            ON TRIM(cr.PartNumber) = TRIM(pc.ConfirmedPartNumber) 
               AND TRIM(UPPER(cr.Brand)) = TRIM(UPPER(pc.ConfirmedBrand))
          LEFT JOIN ${DATASET_ID_AUTOMATION}.automated_RFQ_logs AS ar
            ON ar.RFQID = r.RFQ_ID
          LEFT JOIN (
            SELECT MaterialID, WeightInKilos, Timestamp
            FROM ${DATASET_ID_AUTOMATION}.material_weight
            QUALIFY ROW_NUMBER() OVER (PARTITION BY MaterialID ORDER BY Timestamp DESC) = 1
          ) AS mw
            ON mw.MaterialID = m.Material_ID
          WHERE
            r.RFQ_ID = @rfqId
        ),
        final_result AS (
          SELECT
            RFQ_ID,
            RFQ_Number,
            RFQ_Name,
            RFQ_Date,
            Delivery_Date,
            Deadline,
            Portal,
            URL,
            Company_Name,
            RFQID_2,
            Status,
            logMessage,
            logDate,
            ClientID,
            Name,
            FirstName,
            LastName,
            UserID,
            CurrentStatus,
            ARRAY_AGG(
              STRUCT(
                MaterialID AS Material_ID,
                RFQID AS RFQ_ID,
                Part_Number,
                Quantity_Required,
                prepopulatedWeight,
                Material_Description,
                brand,
                showSuppliers,
                orders,
                centralizedRequestID,
                supplier_json_array AS suppliers
              )
            ) AS material_json_array
          FROM
            material_data
          GROUP BY
            RFQ_ID,
            RFQ_Number,
            RFQ_Name,
            RFQ_Date,
            Delivery_Date,
            Deadline,
            Portal,
            URL,
            UserID,
            Company_Name,
            ClientID,
            Name,
            FirstName,
            LastName,
            RFQID_2,
            Status,
            logMessage,
            logDate,
            CurrentStatus
        )
        SELECT
          RFQ_ID,
          RFQ_Number,
          RFQ_Name,
          RFQ_Date,
          Delivery_Date,
          Deadline,
          Portal,
          RFQID_2,
          UserID,
          Status,
          logMessage,
          logDate,
          URL,
          Company_Name,
          ClientID,
          Name,
          FirstName,
          LastName,
          CurrentStatus,
          material_json_array AS materials
        FROM
          final_result;
      `,
      params: {
        rfqId: parseInt(rfqId),
      },
    });

    if (isEmpty(existingRFQ[0])) {
      throw new Error("RFQ not found");
    }

    const RFQ = existingRFQ[0];

    let materials = [];
    let materialIds = [];

    for (const material of RFQ.materials) {
      let suppliers = [];
      let supplierIds = [];
      for (const supplier of material.suppliers) {
        if (!supplierIds.includes(supplier.QuoteID)) {
          suppliers.push(supplier);
          supplierIds.push(supplier.QuoteID);
        }
      }
      if (!materialIds.includes(material.Material_ID)) {
        !isEmpty(material.Part_Number) ? materials.push(material) : null;
        materialIds.push(material.Material_ID);
      }
      material.suppliers = suppliers;
    }
    RFQ.materials = materials;

    if (!isEmpty(RFQ.materials)) {
      const brands = RFQ.materials.map((material) => material.brand);
      const partNumbers = RFQ.materials.map((material) => material.Part_Number);

      const brandsString = brands.map((brand) => `"${brand}"`).join(", ");
      const partNumbersString = partNumbers
        .map((partNumber) => `"${partNumber}"`)
        .join(", ");

      // Query to get suppliers data
      let query = `
      SELECT sb.SupplierID as supplierID, supplier.name, sb.brand, pn.manufacturer_catalog_number as Part_Number, pn.manufacturer_name, sp.*, sr.materialRequested, sr.status as statusOfRequest, sp.currency as UnitCurrency
      FROM ${DATASET_ID_MAIN}.Suppliers_Brand as sb
      LEFT JOIN ${DATASET_ID_MAIN}.Suppliers as supplier ON supplier.SupplierID = sb.SupplierID
      LEFT JOIN ${DATASET_ID_MAIN}.partnumbers as pn ON TRIM(UPPER(sb.Brand)) = TRIM(UPPER(pn.manufacturer_name))
      LEFT JOIN ${DATASET_ID_MAIN}.supplier_price as sp ON CAST(sp.supplierID as string) = CAST(sb.supplierID as string) AND TRIM(UPPER(sp.brand)) = TRIM(UPPER(sb.Brand)) AND TRIM(sp.partNumber) = TRIM(pn.manufacturer_catalog_number)
      LEFT JOIN ${DATASET_ID_MAIN}.Supplier_Requests as sr ON CAST(sr.supplierID as string) = CAST(sb.supplierID as string)
      WHERE UPPER(sb.Brand) IN (${brandsString})
      AND pn.manufacturer_catalog_number IN (${partNumbersString});
    `;

      const [quoteSuppliers] = await bigQueryClient.query({
        query: query,
      });

      let [bulkSuppliers] = await mainDataset.query({
        query: `SELECT CAST(bsp.supplierID as INT) as supplierID, supplier.name, Price as unitPrice, PartNumber as Part_Number, bsp.Date as date, bsp.Currency as UnitCurrency FROM bulk_supplier_price as bsp
      LEFT JOIN Suppliers as supplier ON CAST(supplier.SupplierID as STRING) = CAST(bsp.supplierID as STRING)
      WHERE bsp.PartNumber IN (${partNumbersString})`,
      });

      if (!isEmpty(bulkSuppliers)) {
        for (const bulkSupplier of bulkSuppliers) {
          for (const quoteSupplier of quoteSuppliers) {
            if (
              bulkSupplier.supplierID === quoteSupplier.supplierID &&
              bulkSupplier.Part_Number === quoteSupplier.Part_Number
            ) {
              const bulkSupplierDate = new Date(bulkSupplier.date.value);
              const quoteSupplierDate = new Date(quoteSupplier?.date?.value);

              if (
                bulkSupplierDate > quoteSupplierDate ||
                isEmpty(quoteSupplierDate)
              ) {
                quoteSupplier.unitPrice = bulkSupplier.unitPrice;
                quoteSupplier.date = bulkSupplier.date;
                quoteSupplier.UnitCurrency = bulkSupplier.UnitCurrency;
              }

              bulkSuppliers = bulkSuppliers.filter(
                (supplier) => supplier.supplierID !== bulkSupplier.supplierID
              );
            }
          }
        }
      }

      let allSuppliers = [];
      if (!isEmpty(bulkSuppliers)) {
        allSuppliers = quoteSuppliers.concat(bulkSuppliers);
      } else {
        allSuppliers = quoteSuppliers;
      }

      for (const material of RFQ.materials) {
        let filteredSuppliers = allSuppliers.filter(
          (supplier) => supplier.Part_Number === material.Part_Number
        );

        let showSuppliers = JSON.parse(material.showSuppliers);

        let filteredSupplierstoShow = [];
        if (!isEmpty(showSuppliers)) {
          let selectedSuppliers = filteredSuppliers.filter((supplier) =>
            showSuppliers?.includes(supplier.supplierID)
          );

          filteredSupplierstoShow = selectedSuppliers;
        }

        const filteredsupplierIDs = [];
        const filteredSuppliersList = [];

        for (const supplier of filteredSupplierstoShow) {
          if (!filteredsupplierIDs.includes(supplier.supplierID)) {
            filteredSuppliersList.push(supplier);
            filteredsupplierIDs.push(supplier.supplierID);
          }
        }

        filteredSupplierstoShow = filteredSuppliersList;

        if (!isEmpty(filteredSuppliers)) {
          material.supplierPricesFound = true;
          for (const supplier of filteredSupplierstoShow) {
            if (!isEmpty(supplier.Price)) {
              supplier.unitPrice = supplier.Price;
              supplier.date = supplier.bulk_date;
            }
            if (!supplier.unitPrice) {
              supplier.status = "NOPRICE";
            } else {
              supplier.status = "RECIEVED";
            }
          }
          if (!isEmpty(showSuppliers)) {
            material.suppliers = material?.suppliers?.concat(
              filteredSupplierstoShow
            );
          }
        } else {
          material.supplierPricesFound = false;
        }
      }

      for (const material of RFQ.materials) {
        let i = 0;
        for (const supplier of material.suppliers) {
          supplier.counter = i;
          i++;
          if (!isEmpty(supplier.materialRequested)) {
            const materialRequested = JSON.parse(supplier.materialRequested);
            supplier.materialRequested = JSON.parse(supplier.materialRequested);
            for (const existingMaterial of materialRequested) {
              if (
                // existingMaterial.rfqID === material.RFQ_ID.toString() &&
                // existingMaterial.materialID === material.Material_ID.toString() &&
                existingMaterial.partNumber ===
                  material.Part_Number.toString() &&
                existingMaterial.status !== "RESPONDED"
                // existingMaterial.brand ===
                // material.brand.toString().toUpperCase() &&
                // supplier.statusOfRequest !== "RESPONDED"
              ) {
                supplier.status = "REQUESTED";
              }
            }
          }
        }
        if (!isEmpty(material.suppliers)) {
          material.suppliers = material.suppliers.sort((a, b) => {
            return a.counter - b.counter;
          });

          // material.suppliers = material.suppliers.filter(
          //   (supplier) => !isEmpty(supplier.name)
          // );
        }
      }

      if (!isEmpty(RFQ.materials)) {
        RFQ.materials = RFQ.materials.sort((a, b) => {
          return a.orders - b.orders;
        });
      }
    }
    return RFQ;
  } catch (error) {
    console.error(error);
    throw new Error("An error occurred while processing the RFQ");
  }
};

const getAllRFQ = apiHandler(async (req, res) => {
  const { limit, page, filterData } = req.body;
  const { userId, role } = req.user;
  currentDate = setDate();

  // Fetch country of logged in user
  const [existingUser] = await mainDataset.query({
    query: `
      SELECT Country FROM Users
      WHERE UserID = @UserID
    `,
    params: {
      UserID: parseInt(userId),
    },
  });
  let countryId = existingUser[0].Country;

  let whereExpression = ``;
  let orderExpression = ` ORDER BY 
                        rc.Part_Number_Available DESC, 
                        rc.Part_Number_Detected DESC,  
                        rc.Hot_Brand DESC `;
  if (role === "KAM") {
    whereExpression = `WHERE ((rs.AssignedUser = @userId AND rs.CurrentStatus NOT IN ("FORGOTTEN", "EXPIRED", "ANSWERED", "WON", "LOST", "IRRELEVANT", "RESERVED", "QUOTING", "VOID")) OR rs.CurrentStatus = 'NEW' OR rs.RFQID IS NULL)`;
  } else {
    whereExpression = `WHERE (rs.CurrentStatus NOT IN ("FORGOTTEN", "EXPIRED", "ANSWERED", "WON", "LOST", "IRRELEVANT", "RESERVED", "QUOTING", "VOID") OR rs.RFQID IS NULL)`;
  }

  // Add country filter for COUNTRY_MANAGER role
  if (role === "COUNTRY_MANAGER") {
    whereExpression += ` AND client.Country = @countryId`;
  }

  filterData?.forEach((filter) => {
    if (!isEmpty(filter.data)) {
      if (filter.type === "text") {
        if (filter.heading === "Search" || filter.heading === "Client" || filter.heading === "RFQ ID") {
          whereExpression += ` 
            AND (
              CAST(r.RFQ_ID AS STRING) LIKE CONCAT('%', TRIM('${filter.data}'), '%') OR
              TRIM(UPPER(r.RFQ_Name)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(r.Company_Name)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(r.Address_Information)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(r.Additional_Notes)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(m.Material_Description)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(m.Notes)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(r.RFQ_Number)) LIKE '%${filter.data.trim()}%'
            )
          `;
        }

        if (filter.heading === "Reserved") {
          whereExpression = whereExpression.replace(
            `, "RESERVED", "QUOTING"`,
            ``
          );
          whereExpression += ` AND (${filter.field} = '${filter.data}' OR ${filter.field} = 'QUOTING')`;

          if (role === "KAM") {
            whereExpression += ` AND rs.AssignedUser = @userId`;
          }
        }
      } else if (filter.type === "array") {
        if (filter.field === "r.deadline") {
          filter.data.forEach((value) => {
            if (value === "today") {
              whereExpression += ` AND DATE(${filter.field})= CURRENT_DATE()`;
            } else if (value === "next3days") {
              whereExpression += ` AND ${filter.field} BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL 3 DAY)`;
            } else if (value === "next7days") {
              whereExpression += ` AND ${filter.field} BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL 7 DAY)`;
            } else if (value === "noFilter") {
              whereExpression += ``;
            }
          });
        }

        if (filter.heading === "Options") {
          filter.data.forEach((value) => {
            if (value === "IRRELEVANT") {
              whereExpression = whereExpression.replace(`"IRRELEVANT",`, ``);

              whereExpression += ` AND ${filter.field} = '${filter.data}'`;
            }
          });
        }
      }
      if (filter.type === "select") {
        whereExpression += ` AND ${filter.field} = CAST('${filter.data}' AS int)`;
      }
    }

    if (filter.heading === "RFQ ID") {
      whereExpression += ` AND ${
        filter.field
      } = CAST('${filter.data.trim()}' AS int)`;
    }
    if (filter.heading === "isPartNumberDetected") {
      filter.data.forEach((value) => {
        if (value === "isPartNumberDetected") {
          whereExpression += ` AND ${filter.field} = TRUE`;
        }

        if (value === "isNotPartNumberDetected") {
          whereExpression += ` AND ( ${filter.field} = FALSE OR ${filter.field} IS NULL)`;
        }
      });
    }
    if (filter.heading === "isPartNumberAvailable") {
      filter.data.forEach((value) => {
        if (value === "isPartNumberAvailable") {
          whereExpression += ` AND ${filter.field} = TRUE`;
        }
      });
    }

    if (filter.heading === "isHotBrand") {
      filter.data.forEach((value) => {
        if (value === "isHotBrand") {
          whereExpression += ` AND ${filter.field} = TRUE`;
        }
      });
    }
    if (filter.heading === "sort") {
  const direction = filter.orderBy[0].toUpperCase(); // 'ASC' or 'DESC'
  const field = filter.field;

  // For both ASC and DESC, we want NULL and empty values to appear last
  orderExpression = `
    ORDER BY
      CASE 
        WHEN ${field} IS NULL OR TRIM(CAST(${field} AS STRING)) = '' THEN 1 
        ELSE 0 
      END ASC,
      ${field} ${direction},
      r.RFQ_Date DESC,
      r.RFQ_ID DESC
  `;
} else {
  // default order
  orderExpression += `, r.RFQ_Date DESC, r.RFQ_ID DESC`;
}

  });

  let joinExpression = `
    LEFT JOIN ${DATASET_ID_SCRAPE}.Material as m ON r.RFQ_ID = m.RFQ_ID
    LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status as rs ON r.RFQ_ID = rs.RFQID
    LEFT JOIN ${DATASET_ID_MAIN}.Company AS c ON UPPER(r.Company_Name) = UPPER(c.Name)
    LEFT JOIN ${DATASET_ID_MAIN}.Users AS u ON rs.AssignedUser = u.UserID
    LEFT JOIN ${DATASET_ID_DASHBOARDS}.rfq_classifications AS rc ON rc.RFQ_ID = CAST(r.RFQ_ID as STRING)
    LEFT JOIN ${DATASET_ID_MAIN}.Email_Supplier_Requests AS esr ON esr.RFQID = r.RFQ_ID AND esr.Email_Status = "REPLIED"
  `;

  if (role === "COUNTRY_MANAGER") {
    joinExpression += `LEFT JOIN ${DATASET_ID_MAIN}.Clients AS client ON client.ClientID = r.Created_By`;
  }

  let fetchQuery = `
      WITH row_count_cte AS (
        SELECT COUNT(DISTINCT r.RFQ_ID) AS row_count
        FROM ${DATASET_ID_SCRAPE}.RFQ as r
        ${joinExpression}
        ${whereExpression}
      ),
      data_cte AS (
        SELECT DISTINCT r.RFQ_ID, r.RFQ_Name, r.isManuallyAdded, r.RFQ_Number, r.RFQ_Date, r.Deadline, r.Delivery_Date, 
              r.Company_Name, r.Created_From_RFQ_ID, rs.CurrentStatus, u.FirstName, u.LastName, u.Role, u.UserID,
              c.Logo, r.Created_By, rc.Part_Number_Detected, rc.Part_Number_Available, rc.Hot_Brand, esr.Email_Status
        FROM ${DATASET_ID_SCRAPE}.RFQ AS r
        ${joinExpression}
        ${whereExpression}
        ${orderExpression}
        LIMIT @limit OFFSET @offset
      )
      SELECT d.*, r.row_count
      FROM data_cte d, row_count_cte r;
    `;

  let paramObj = {
    userId,
    limit: limit || 10,
    offset: ((page || 1) - 1) * (limit || 10),
  }

  if (role === "COUNTRY_MANAGER") {
    paramObj.countryId = countryId 
  }

  const [existingRFQs] = await bigQueryClient.query({
    query: fetchQuery,
    params: paramObj,
  });
  if (isEmpty(existingRFQs)) {
    return apiError(NOT_FOUND, "RFQs", null, res);
  }

  const data = {
    count: existingRFQs[0].row_count,
    data: existingRFQs,
  };

  let groupedData = [];
  let RFQIDs = [];
  for (const RFQ of existingRFQs) {
    if (!RFQIDs.includes(RFQ.RFQ_ID)) {
      groupedData.push(RFQ);
      RFQIDs.push(RFQ.RFQ_ID);
    }
  }

  return apiResponse(FETCH, "RFQs", applyFloor(data), res);
});

const getRFQWithConfirmedMaterials = apiHandler(async (req, res) => {
  const { limit, page, filterData } = req.body;
  const { userId, role } = req.user;
  currentDate = setDate();

  let whereExpression = ``;
  let orderExpression = `ORDER BY r.RFQ_Date DESC, r.RFQ_ID DESC`;

  if (role === "KAM") {
    whereExpression = `WHERE ((rs.AssignedUser = @userId AND rs.CurrentStatus NOT IN ("FORGOTTEN", "EXPIRED", "ANSWERED", "WON", "LOST", "IRRELEVANT", "RESERVED", "QUOTING", "VOID")) OR rs.CurrentStatus = 'NEW' OR rs.RFQID IS NULL)`;
  } else {
    whereExpression = `WHERE (rs.CurrentStatus NOT IN ("FORGOTTEN", "EXPIRED", "ANSWERED", "WON", "LOST", "IRRELEVANT", "RESERVED", "QUOTING", "VOID") OR rs.RFQID IS NULL)`;
  }
  filterData?.forEach((filter) => {
    if (!isEmpty(filter.data)) {
      if (filter.type === "text") {
        if (filter.heading === "Search") {
          whereExpression += ` 
            AND (
              TRIM(UPPER(r.RFQ_Name)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(r.Company_Name)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(r.Address_Information)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(r.Additional_Notes)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(m.Material_Description)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(m.Notes)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(r.RFQ_Number)) LIKE '%${filter.data.trim()}%'
            )
          `;
        }

        if (filter.heading === "Reserved") {
          whereExpression = whereExpression.replace(
            `, "RESERVED", "QUOTING"`,
            ``
          );
          whereExpression += ` AND (${filter.field} = '${filter.data}' OR ${filter.field} = 'QUOTING')`;

          if (role === "KAM") {
            whereExpression += ` AND rs.AssignedUser = @userId`;
          }
        }
      } else if (filter.type === "array") {
        if (filter.field === "r.deadline") {
          filter.data.forEach((value) => {
            if (value === "today") {
              whereExpression += ` AND DATE(${filter.field})= CURRENT_DATE()`;
            } else if (value === "next3days") {
              whereExpression += ` AND ${filter.field} BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL 3 DAY)`;
            } else if (value === "next7days") {
              whereExpression += ` AND ${filter.field} BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL 7 DAY)`;
            } else if (value === "noFilter") {
              whereExpression += ``;
            }
          });
        }

        if (filter.heading === "Options") {
          filter.data.forEach((value) => {
            if (value === "IRRELEVANT") {
              whereExpression = whereExpression.replace(`"IRRELEVANT",`, ``);

              whereExpression += ` AND ${filter.field} = '${filter.data}'`;
            }
          });
        }
      }
      if (filter.type === "select") {
        whereExpression += ` AND ${filter.field} = CAST('${filter.data}' AS int)`;
      }
    }

    if (filter.heading === "RFQ ID") {
      whereExpression += ` AND ${
        filter.field
      } = CAST('${filter.data.trim()}' AS int)`;
    }
    if (filter.heading === "isPartNumberDetected") {
      filter.data.forEach((value) => {
        if (value === "isPartNumberDetected") {
          whereExpression += ` AND ${filter.field} = TRUE`;
        }

        if (value === "isNotPartNumberDetected") {
          whereExpression += ` AND ( ${filter.field} = FALSE OR ${filter.field} IS NULL)`;
        }
      });
    }
    if (filter.heading === "isPartNumberAvailable") {
      filter.data.forEach((value) => {
        if (value === "isPartNumberAvailable") {
          whereExpression += ` AND ${filter.field} = TRUE`;
        }
      });
    }

    if (filter.heading === "isHotBrand") {
      filter.data.forEach((value) => {
        if (value === "isHotBrand") {
          whereExpression += ` AND ${filter.field} = TRUE`;
        }
      });
    }
    if (filter.heading === "sort") {
      orderExpression = ` ORDER BY ${filter.field} ${filter.orderBy}`;
    }
  });
  const [existingRFQs] = await bigQueryClient.query({
    query: `
      WITH row_count_cte AS (
    SELECT COUNT(DISTINCT r.RFQ_ID) AS row_count
    FROM ${DATASET_ID_SCRAPE}.RFQ as r
    LEFT JOIN ${DATASET_ID_SCRAPE}.Material as m ON r.RFQ_ID = m.RFQ_ID
    LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status as rs ON r.RFQ_ID = rs.RFQID
    LEFT JOIN ${DATASET_ID_MAIN}.Users AS u ON rs.AssignedUser = u.UserID
    LEFT JOIN ${DATASET_ID_MAIN}.Predictions_Confirmations AS pc ON pc.MaterialID = m.Material_ID AND pc.RFQID = r.RFQ_ID
    ${whereExpression}
),
material_data AS (
    SELECT
        r.RFQ_ID,
        r.RFQ_Number,
        r.RFQ_Name,
        r.RFQ_Date,
        r.Deadline,
        r.Delivery_Date,
        r.Company_Name,
        r.Created_From_RFQ_ID,
        rs.CurrentStatus,
        u.FirstName,
        u.LastName,
        u.Role,
        u.UserID,
        c.Logo,
        r.Created_By,
ARRAY(
    SELECT DISTINCT STRUCT(
        m.Material_ID AS Material_ID,
        m.RFQ_ID AS RFQ_ID,
        pc.ConfirmedPartNumber AS Part_Number,
        m.Quantity_Required AS Quantity_Required,
        m.Material_Description AS Material_Description,
        pc.ConfirmedBrand AS brand
    )
    FROM ${DATASET_ID_SCRAPE}.Material AS m
    LEFT JOIN ${DATASET_ID_MAIN}.Predictions_Confirmations AS pc 
        ON pc.MaterialID = m.Material_ID AND pc.RFQID = m.RFQ_ID
    WHERE m.RFQ_ID = r.RFQ_ID
) AS materials
    FROM ${DATASET_ID_SCRAPE}.RFQ AS r
    LEFT JOIN ${DATASET_ID_SCRAPE}.Material AS m ON r.RFQ_ID = m.RFQ_ID
    LEFT JOIN ${DATASET_ID_MAIN}.Predictions_Confirmations AS pc ON pc.MaterialID = m.Material_ID AND pc.RFQID = r.RFQ_ID
    LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status AS rs ON r.RFQ_ID = rs.RFQID
    LEFT JOIN ${DATASET_ID_MAIN}.Company AS c ON UPPER(r.Company_Name) = UPPER(c.Name)
    LEFT JOIN ${DATASET_ID_MAIN}.Users AS u ON rs.AssignedUser = u.UserID
    ${whereExpression}
    GROUP BY 
        r.RFQ_ID, r.RFQ_Number, r.RFQ_Name, r.RFQ_Date, r.Deadline, r.Delivery_Date, 
        r.Company_Name, r.Created_From_RFQ_ID, rs.CurrentStatus, u.FirstName, u.LastName, 
        u.Role, u.UserID, c.Logo, r.Created_By
    ${orderExpression}
    LIMIT @limit OFFSET @offset
)
SELECT d.*, r.row_count
FROM material_data d, row_count_cte r;
    `,
    params: {
      userId,
      limit: limit || 10,
      offset: ((page || 1) - 1) * (limit || 10),
    },
  });
  if (isEmpty(existingRFQs)) {
    return apiError(NOT_FOUND, "RFQs", null, res);
  }

  let groupedData = [];
  let RFQIDs = [];
  for (const RFQ of existingRFQs) {
    if (!RFQIDs.includes(RFQ.RFQ_ID)) {
      if (RFQ?.materials?.length > 0) {
        groupedData.push(RFQ);
      }
      RFQIDs.push(RFQ.RFQ_ID);
    }
  }

  const data = {
    count: groupedData[0].row_count,
    data: groupedData,
  };

  return apiResponse(FETCH, "RFQs", applyFloor(data), res);
});

const getAutomatedRFQ = apiHandler(async (req, res) => {
  const { limit, page, filterData } = req.body;
  const { userId, role } = req.user;

  currentDate = setDate();
  let [existingSystemUser] = await mainDataset.query({
    query: `
      SELECT UserID
      FROM Users
      WHERE isSystemReserved is TRUE
    `,
  });
  let whereExpression = ``;
  let orderExpression = ``;
  // if (role === "KAM") {
  //   whereExpression = `WHERE (rs.AssignedUser = @userId AND rs.CurrentStatus != "Answered")`;
  // } else {

  whereExpression = `WHERE ((rs.AssignedUser = ${existingSystemUser[0].UserID} OR rs.AssignedUser = @userId )  AND rs.CurrentStatus != "ANSWERED")`;

  // }
  filterData?.forEach((filter) => {
    if (!isEmpty(filter.data)) {
      if (filter.type === "text") {
        if (filter.heading === "Search") {
          whereExpression += ` 
            AND (
              TRIM(UPPER(r.RFQ_Name)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(r.Company_Name)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(r.Address_Information)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(r.Additional_Notes)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(m.Material_Description)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(m.Notes)) LIKE UPPER('%${filter.data.trim()}%') OR
              TRIM(UPPER(r.RFQ_Number)) LIKE '%${filter.data.trim()}%'
            )
          `;
        }
      } else if (filter.type === "array") {
        if (filter.field === "r.deadline") {
          filter.data.forEach((value) => {
            if (value === "today") {
              whereExpression += ` AND DATE(${filter.field})= CURRENT_DATE()`;
            } else if (value === "next3days") {
              whereExpression += ` AND ${filter.field} BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL 3 DAY)`;
            } else if (value === "next7days") {
              whereExpression += ` AND ${filter.field} BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL 7 DAY)`;
            } else if (value === "noFilter") {
              whereExpression += ``;
            }
          });
        }

        if (filter.heading === "Options") {
          filter.data.forEach((value) => {
            if (value === "IRRELEVANT") {
              whereExpression = whereExpression.replace(`"IRRELEVANT",`, ``);

              whereExpression += ` AND ${filter.field} = '${filter.data}'`;
            }
          });
        }
      }
      if (filter.type === "select") {
        whereExpression += ` AND ${filter.field} = CAST('${filter.data}' AS int)`;
      }
    }

    if (filter.heading === "RFQ ID") {
      whereExpression += ` AND ${
        filter.field
      } = CAST('${filter.data.trim()}' AS int)`;
    }

    if (filter.heading === "Client") {
      whereExpression += ` AND ${filter.field} = '${filter.data}'`;
    }

    if (filter.heading === "isPartNumberDetected") {
      filter.data.forEach((value) => {
        if (value === "isPartNumberDetected") {
          whereExpression += ` AND ${filter.field} = TRUE`;
        }

        if (value === "isNotPartNumberDetected") {
          whereExpression += ` AND ( ${filter.field} = FALSE OR ${filter.field} IS NULL)`;
        }
      });
    }

    if (filter.heading === "isPartNumberAvailable") {
      filter.data.forEach((value) => {
        if (value === "isPartNumberAvailable") {
          whereExpression += ` AND ${filter.field} = TRUE`;
        }
      });
    }

    if (filter.heading === "isHotBrand") {
      filter.data.forEach((value) => {
        if (value === "isHotBrand") {
          whereExpression += ` AND ${filter.field} = TRUE`;
        }
      });
    }

    if (filter.heading === "sort") {
      orderExpression += `, ${filter.field} ${filter.orderBy}`;
    } else {
      orderExpression += `, r.RFQ_Date DESC, r.RFQ_ID DESC`;
    }
  });

  const [existingRFQs] = await bigQueryClient.query({
    query: `
WITH latest_arl AS (
  SELECT *
  FROM (
    SELECT *,
           ROW_NUMBER() OVER (PARTITION BY RFQID ORDER BY logDate DESC) AS row_num
    FROM ${DATASET_ID_AUTOMATION}.automated_RFQ_logs
  )
  WHERE row_num = 1
),
row_count_cte AS (
  SELECT COUNT(DISTINCT r.RFQ_ID) AS row_count
  FROM ${DATASET_ID_AUTOMATION}.automated_RFQ as ar
  LEFT JOIN ${DATASET_ID_SCRAPE}.RFQ as r ON r.RFQ_ID = ar.RFQID
  LEFT JOIN ${DATASET_ID_SCRAPE}.Material as m ON r.RFQ_ID = m.RFQ_ID
  LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status as rs ON r.RFQ_ID = rs.RFQID
  LEFT JOIN ${DATASET_ID_MAIN}.Users AS u ON rs.AssignedUser = u.UserID
  LEFT JOIN ${DATASET_ID_DASHBOARDS}.rfq_classifications AS rc ON rc.RFQ_ID = CAST(r.RFQ_ID as STRING)
  LEFT JOIN latest_arl AS arl ON ar.RFQID = arl.RFQID
  ${whereExpression}
),
data_cte AS (
  SELECT DISTINCT r.RFQ_ID, r.RFQ_Name, r.isManuallyAdded, r.RFQ_Number, r.RFQ_Date, r.Deadline, r.Delivery_Date, 
                  r.Company_Name, r.Created_From_RFQ_ID, rs.CurrentStatus, u.FirstName, u.LastName, u.Role, u.UserID,
                  c.Logo, r.Created_By, rc.Part_Number_Detected, rc.Part_Number_Available, rc.Hot_Brand, ar.Status, arl.*
  FROM ${DATASET_ID_AUTOMATION}.automated_RFQ as ar
  LEFT JOIN ${DATASET_ID_SCRAPE}.RFQ as r ON r.RFQ_ID = ar.RFQID
  LEFT JOIN ${DATASET_ID_SCRAPE}.Material as m ON r.RFQ_ID = m.RFQ_ID
  LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status AS rs ON r.RFQ_ID = rs.RFQID
  LEFT JOIN ${DATASET_ID_MAIN}.Company AS c ON UPPER(r.Company_Name) = UPPER(c.Name)
  LEFT JOIN ${DATASET_ID_MAIN}.Users AS u ON rs.AssignedUser = u.UserID
  LEFT JOIN ${DATASET_ID_DASHBOARDS}.rfq_classifications AS rc ON rc.RFQ_ID = CAST(r.RFQ_ID as STRING)
  LEFT JOIN latest_arl AS arl ON ar.RFQID = arl.RFQID
  ${whereExpression}
  ${orderExpression}
  LIMIT @limit OFFSET @offset
)
SELECT d.*, r.row_count
FROM data_cte d, row_count_cte r;

    `,
    params: {
      userId,
      limit: limit || 10,
      offset: ((page || 1) - 1) * (limit || 10),
    },
  });
  if (isEmpty(existingRFQs)) {
    return apiError(NOT_FOUND, "RFQs", null, res);
  }

  let updatedExistingRFQ = await Promise.all(
    existingRFQs?.map(async (existingRfq) => {
      if (existingRfq?.Status !== "SUCCESS") {
        let quoteData = await getQuoteDetails(existingRfq?.RFQ_ID);

        let materialCount = quoteData?.materials?.length || 0;

        // Calculate the supplier count where at least one supplier exists with the required statuses
        let supplierCount = quoteData?.materials?.reduce((total, material) => {
          let hasValidSupplier = material?.suppliers?.some(
            (supplier) =>
              !isEmpty(supplier.unitPrice) &&
              supplier.unitPrice !== 0 &&
              supplier.status !== "REGISTERED"
          );
          return total + (hasValidSupplier ? 1 : 0); // Count the material if it has at least one valid supplier
        }, 0);

        return { ...existingRfq, materialCount, supplierCount };
      }

      return existingRfq; // Return the unmodified RFQ if the status is 'SUCCESS'
    })
  );

  const data = {
    count: existingRFQs[0].row_count,
    data: updatedExistingRFQ,
  };

  let groupedData = [];
  let RFQIDs = [];
  for (const RFQ of existingRFQs) {
    if (!RFQIDs.includes(RFQ.RFQ_ID)) {
      groupedData.push(RFQ);
      RFQIDs.push(RFQ.RFQ_ID);
    }
  }

  return apiResponse(FETCH, "RFQs", applyFloor(data), res);
});

const getSingleRFQ = apiHandler(async (req, res) => {
  const { id } = req.params;

  const [existingRFQ] = await bigQueryClient.query({
    query: `
      SELECT r.RFQ_Name, r.isManuallyAdded, r.RFQ_Date, r.Delivery_Date, r.Deadline, r.Portal, r.RFQ_Number,
            r.URL, r.Company_Name, r.Created_From_RFQ_ID, rs.CurrentStatus, u.FirstName, u.LastName, u.UserID,
            pc.*,  m.*, cl.ClientID, cl.Name, q.*, r.Address_Information, r.Additional_Notes, r.Priority, ar.*
      FROM ${DATASET_ID_SCRAPE}.RFQ as r
      LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status as rs ON r.RFQ_ID = rs.RFQID
      LEFT JOIN ${DATASET_ID_MAIN}.Company as c ON UPPER(r.Company_Name) = UPPER(c.Name)
      LEFT JOIN ${DATASET_ID_MAIN}.Users as u ON u.UserID = rs.AssignedUser
      LEFT JOIN ${DATASET_ID_SCRAPE}.Material as m ON m.RFQ_ID = r.RFQ_ID
      LEFT JOIN ${DATASET_ID_MAIN}.Quotes as q ON (q.RFQID = r.RFQ_ID AND q.MaterialID = m.Material_ID)
      LEFT JOIN ${DATASET_ID_MAIN}.Predictions_Confirmations as pc ON (m.Material_ID = pc.MaterialID AND m.RFQ_ID = pc.RFQID)
      LEFT JOIN ${DATASET_ID_AUTOMATION}.automated_RFQ_logs as ar ON ar.RFQID = r.RFQ_ID
      LEFT JOIN ${DATASET_ID_MAIN}.Clients AS cl ON UPPER(TRIM(r.Company_Name)) = UPPER(TRIM(cl.Name)) OR UPPER(TRIM(r.ClientID)) = UPPER(TRIM(cl.ClientID))
      WHERE r.RFQ_ID = @rfqId
    `,
    params: { rfqId: parseInt(id) },
  });
  if (isEmpty(existingRFQ)) {
    return apiError(NOT_FOUND, "RFQ", null, res);
  }
  let groupedData = {};
  existingRFQ.forEach((rfq) => {
    const {
      Notes,
      Quantity_Required,
      Position,
      Material_ID,
      Material_Description,
      Part_Number,
      ConfirmationStatus,
      ConfirmationDate,
      ConfirmedPartNumber,
      ConfirmedBrand,
      ExtractedPartNumber,
      ExtractedBrand,
      orders,

      QuoteID,
      MaterialID_1,
      RFQID_1,
      SupplierID,
      QuoteDate,
      UnitPrice,
      UnitCurrency,
      Quantity,
      ShippingCost,
      Tax,
      Weight,
      TotalCost,
      OfferedPrice,
      DeliveryDate,
      KAM,
      LeadTime,
      Offered,
      Notes_1,
    } = rfq;
    const partNumber =
      ConfirmationStatus === "Done"
        ? ConfirmedPartNumber
        : ExtractedPartNumber || Part_Number;

    const brand =
      ConfirmationStatus === "Done" ? ConfirmedBrand : ExtractedBrand;

    const unitOfferPrice = round(OfferedPrice / Quantity, 2);

    const supplier = {
      QuoteID,
      MaterialID: MaterialID_1,
      RFQID: RFQID_1,
      SupplierID,
      QuoteDate,
      UnitPrice,
      UnitCurrency,
      Quantity,
      ShippingCost,
      Tax,
      Weight,
      TotalCost,
      OfferedPrice,
      DeliveryDate,
      LeadTime,
      unitOfferPrice: isNaN(unitOfferPrice) ? null : unitOfferPrice,
      KAM,
      Offered,
      Notes: Notes_1,
    };

    const material = {
      Notes,
      Quantity_Required,
      Position,
      Material_ID,
      Material_Description,
      Part_Number: partNumber,
      Brand: brand,
      ConfirmationStatus,
      ConfirmationDate,
      Suppliers: [],
      specsSheetAvailable: false,
      history: false,
      orders,
    };

    if (isEmpty(groupedData)) {
      groupedData = { ...rfq, Materials: [material] };
      delete groupedData.Notes;
      delete groupedData.Quantity_Required;
      delete groupedData.Position;
      delete groupedData.Material_ID;
      delete groupedData.Material_Description;
      delete groupedData.Part_Number;
      delete groupedData.ConfirmationStatus;
      delete groupedData.ConfirmationDate;
      delete groupedData.PredictionDate;
      delete groupedData.PredictionStatus;
      delete groupedData.ConfirmedPartNumber;
      delete groupedData.ConfirmedBrand;
      delete groupedData.ExtractedPartNumber;
      delete groupedData.ExtractedBrand;

      delete groupedData.QuoteID;
      delete groupedData.MaterialID_1;
      delete groupedData.RFQID_1;
      delete groupedData.SupplierID;
      delete groupedData.QuoteDate;
      delete groupedData.UnitPrice;
      delete groupedData.UnitCurrency;
      delete groupedData.Quantity;
      delete groupedData.ShippingCost;
      delete groupedData.Tax;
      delete groupedData.TotalCost;
      delete groupedData.OfferedPrice;
      delete groupedData.DeliveryDate;
      delete groupedData.unitOfferPrice;
      delete groupedData.KAM;
      delete groupedData.Offered;
      delete groupedData.LeadTime;
      delete groupedData.Notes_1;
      delete groupedData.Weight;
    }
    const foundMaterial = groupedData.Materials.find(
      (m) => m.Material_ID === Material_ID
    );
    if (foundMaterial) {
      const foundQuote = foundMaterial.Suppliers.find(
        (s) => s.QuoteID === QuoteID
      );
      if (!foundQuote && !isEmpty(supplier)) {
        foundMaterial.Suppliers.push(supplier);
      }
    } else {
      groupedData.Materials.push(material);
    }
  });

  if (!isEmpty(groupedData.Materials)) {
    groupedData.Materials = groupedData.Materials.sort((a, b) => {
      if (a.orders === null || b.orders === null) {
        return a.Material_ID - b.Material_ID;
      }
      return a.orders - b.orders;
    });
  }

  for (const material of groupedData.Materials) {
    if (!isEmpty(material.Material_ID)) {
      const [specsSheets] = await landingZoneDataset.query({
        query: `
          SELECT pd.group_items_name ,pd.group_items_display_name
          FROM products_downloads as pd
          INNER JOIN products_master as pm
          ON pd.product_id = pm.product_id
          WHERE pm.manufacturer_catalog_number=@partNumber
        `,
        params: {
          partNumber: material.Part_Number,
        },
      });

      const [materialHistory] = await bigQueryClient.query({
        query: `
          SELECT m.Material_Description, m.Part_Number, m.RFQ_ID, m.Material_ID,
                pc.ConfirmedPartNumber, s.ContactName, s.ContactLastname,
                q.*, o.OfferCurrency, o.Margin, o.Status
          FROM ${DATASET_ID_SCRAPE}.Material as m
          LEFT JOIN ${DATASET_ID_MAIN}.Predictions_Confirmations as pc ON m.Part_Number = pc.ConfirmedPartNumber
          LEFT JOIN ${DATASET_ID_MAIN}.Quotes as q ON pc.MaterialID = q.MaterialID
          LEFT JOIN ${DATASET_ID_MAIN}.Offers as o ON q.QuoteID = o.QuoteID
          LEFT JOIN ${DATASET_ID_MAIN}.Suppliers as s ON q.SupplierID = s.SupplierID
          WHERE m.Material_ID = @materialId
        `,
        params: { materialId: parseInt(material.Material_ID) },
      });

      let groupedData = {};
      materialHistory.forEach((material) => {
        const {
          Material_Description,
          ConfirmedPartNumber,
          Part_Number,
          RFQID,
          RFQ_ID,
          MaterialID,
          Material_ID,
          ...history
        } = JSON.parse(JSON.stringify(material));

        if (isEmpty(groupedData)) {
          groupedData = {
            MaterialDescription: Material_Description,
            PartNumber: ConfirmedPartNumber || Part_Number,
            RFQID: RFQID || RFQ_ID,
            MaterialID: MaterialID || Material_ID,
            History: [],
          };
        }
        if (!isEmpty(history.QuoteID)) {
          groupedData.History.push(history);
        }
      });

      let quoteIds = [];
      let quoteData = [];

      for (const data of groupedData.History) {
        if (!quoteIds.includes(data.QuoteID)) {
          quoteIds.push(data.QuoteID);
          quoteData.push(data);
        }
      }

      groupedData.History = quoteData;

      if (!isEmpty(groupedData.History)) {
        material.history = true;
      }

      if (!isEmpty(specsSheets)) {
        material.specsSheetAvailable = true;
      }
    } else {
      groupedData.Materials = [];
    }
  }

  return apiResponse(FETCH, "RFQ", applyFloor(groupedData), res);
});

const updateRFQStatus = apiHandler(async (req, res) => {
  const { rfqId, status, userId } = req.body;

  const [existingRFQ] = await mainDataset.query({
    query: `
      SELECT RFQID
      FROM RFQ_Status
      WHERE RFQID = @rfqId
    `,
    params: { rfqId: parseInt(rfqId) },
  });
  if (isEmpty(existingRFQ)) {
    await mainDataset.query({
      query: `
        INSERT INTO RFQ_Status (RFQID, CurrentStatus, StatusChangeDate, AssignedUser, timestamp_date)
        VALUES (@rfqId, @status, @date, @userId, CURRENT_TIMESTAMP())
      `,
      params: {
        rfqId: parseInt(rfqId),
        userId: parseInt(userId),
        status,
        date: setDate(),
      },
    });
  } else {
    await mainDataset.query({
      query: `
        UPDATE RFQ_Status as rs
        SET CurrentStatus = @status, StatusChangeDate = @date
        ${!isEmpty(userId) ? `, AssignedUser = ${userId}` : ``}
        WHERE rs.RFQID = @rfqId
      `,
      params: {
        status,
        date: setDate(),
        rfqId: parseInt(rfqId),
      },
    });
  }

  if (status === "RESERVED" && !isEmpty(userId)) {
    if (req.user.userId.toString() !== userId.toString()) {
      let name = `${req.user.firstName} ${req.user.lastName}`;

      let message = `The RFQ ( ${parseInt(
        rfqId
      )} ) was assigned to you, please check.`;
      // let link = `${BASE_URL}/reserve-single-rfq/${parseInt(rfqId)}`;

      try {
        sendNotification({
          userID: userId,
          message: message,
          RFQID: parseInt(rfqId),
          sender: name,
        });
      } catch (error) {
        return apiError(SENT_ERROR, "Notification", null, res);
      }
    }
  }

  return apiResponse(STATUS_SUCCESS, "RFQ", null, res);
});

const generateRFQ = apiHandler(async (req, res) => {
  const {
    RFQ_Number,
    RFQ_Name,
    Company_Name,
    Portal,
    Priority,
    Deadline,
    Address_Information,
    Additional_Notes,
    Delivery_Date,
    URL,
    Client_Id,
    materials,
  } = req.body;

  const { userId, role } = req.user;
  const RFQ_ID = generateID(6);

  // Fetch client name
  const [existingClient] = await mainDataset.query({
    query: `
      SELECT Name FROM Clients
      WHERE ClientID=@clientID
    `,
    params: {
      clientID: Client_Id,
    },
  });

  await scrapedDataset.query({
    query: `
      INSERT INTO RFQ (RFQ_ID, RFQ_Number, RFQ_Name, RFQ_Date, Company_Name, Portal, Status, Priority, Deadline, Created_By, Address_Information, Additional_Notes, Delivery_Date, URL, isManuallyAdded) 
      VALUES 
      (@RFQ_ID, @RFQ_Number, @RFQ_Name, @RFQ_Date, @Company_Name, @Portal, @Status, @Priority, @Deadline, @Created_By, @Address_Information, @Additional_Notes, @Delivery_Date, @URL, @isManuallyAdded)
      `,
    params: {
      RFQ_ID: RFQ_ID,
      RFQ_Number: RFQ_Number || "",
      RFQ_Name: RFQ_Name || "",
      RFQ_Date: setDate(),
      Company_Name: existingClient[0].Name,
      Portal: Portal || "",
      Status: "Open",
      Priority: Priority || "",
      Deadline,
      Created_By: Client_Id.toString(),
      Address_Information: Address_Information || "",
      Additional_Notes: Additional_Notes || "",
      Delivery_Date: Delivery_Date || null,
      URL: URL || "",
      isManuallyAdded: true,
    },
    types: Delivery_Date
      ? {}
      : {
          Delivery_Date: "date",
        },
  });

  if (role !== "COUNTRY_MANAGER") {    
    await mainDataset.query({
      query: `
        INSERT INTO RFQ_Status (RFQID, CurrentStatus, StatusChangeDate, AssignedUser, timestamp_date) 
        VALUES 
        (@RFQ_ID, @CurrentStatus, @StatusChangeDate, @AssignedUser, CURRENT_TIMESTAMP())
        `,
      params: {
        RFQ_ID: RFQ_ID,
        CurrentStatus: "RESERVED",
        StatusChangeDate: setDate(),
        AssignedUser: req.user.userId,
      },
    });
  }

  let i = 1;
  for (const material of materials) {
    material.Material_ID = generateID();
    material.orders = i;

    i++;

    const [existingPartNumber] = await mainDataset.query({
      query: `
        SELECT * FROM partnumbers
        WHERE manufacturer_catalog_number=@Part_Number
      `,
      params: {
        Part_Number: material.Part_Number,
      },
    });

    if (isEmpty(existingPartNumber[0])) {
      const [newPartNumber] = await mainDataset.query({
        query: `
          INSERT INTO partnumbers (id, manufacturer_catalog_number, description, manufacturer_name)
          VALUES (@ID, @Part_Number, @description, @brand)
        `,
        params: {
          ID: generateID().toString(),
          Part_Number: material.Part_Number,
          description: material.Material_Description,
          brand: material.brand,
        },
      });
    } else {
      const [updatedPartNumber] = await mainDataset.query({
        query: `
          UPDATE partnumbers SET
          description = @description, manufacturer_name = @brand
          WHERE manufacturer_catalog_number=@Part_Number
        `,
        params: {
          Part_Number: material.Part_Number,
          description: material.Material_Description,
          brand: material.brand,
        },
      });
    }

    // material.partNumberId = generateID();
  }

  let materialInsertValues = materials.map((material) => {
    return {
      Material_ID: material.Material_ID,
      RFQ_ID: RFQ_ID,
      Part_Number: material.Part_Number,
      Quantity_Required: material.Quantity_Required,
      Material_Description: material.Material_Description || "",
      Notes: material.Notes || "",
      orders: material.orders,
    };
  });

  let predictionConfirmationInsertValues = materials.map((material) => {
    return {
      MaterialID: material.Material_ID,
      RFQID: RFQ_ID,
      ExtractedBrand: material.brand,
      ExtractedPartNumber: material.Part_Number,
      ConfirmedBrand: material.brand,
      ConfirmedPartNumber: material.Part_Number,
      PredictionStatus: "Done",
      PredictionDate: setDate(),
      ConfirmationDate: setDate(),
      ConfirmationStatus: "Done",
    };
  });

  let materialQuery = `
      INSERT INTO Material (Material_ID, RFQ_ID, Part_Number, Quantity_Required, Material_Description, Notes, orders)
      VALUES 
  `;

  let predictionConfirmationQuery = `
      INSERT INTO Predictions_Confirmations (MaterialID, RFQID, ExtractedBrand, ExtractedPartNumber, PredictionStatus, PredictionDate, ConfirmedBrand, ConfirmedPartNumber, ConfirmationDate, ConfirmationStatus) 
      VALUES 
  `;

  let materialParams = {};
  materialInsertValues.forEach((material, index) => {
    let suffix = index === materialInsertValues.length - 1 ? ";" : ",";
    materialQuery += `(@Material_ID${index}, @RFQ_ID${index}, @Part_Number${index}, @Quantity_Required${index}, @Material_Description${index}, @Notes${index}, @orders${index})${suffix}`;

    materialParams[`Material_ID${index}`] = material.Material_ID;
    materialParams[`RFQ_ID${index}`] = material.RFQ_ID;
    materialParams[`Part_Number${index}`] = material.Part_Number;
    materialParams[`Quantity_Required${index}`] = material.Quantity_Required;
    materialParams[`Material_Description${index}`] =
      material.Material_Description;
    materialParams[`Notes${index}`] = material.Notes;
    materialParams[`orders${index}`] = material.orders;
  });

  let predictionConfirmationParams = {};
  predictionConfirmationInsertValues.forEach((material, index) => {
    let suffix =
      index === predictionConfirmationInsertValues.length - 1 ? ";" : ",";
    predictionConfirmationQuery += `(@MaterialID${index}, @RFQID${index}, @ExtractedBrand${index}, @ExtractedPartNumber${index}, @PredictionStatus${index}, @PredictionDate${index}, @ConfirmedBrand${index}, @ConfirmedPartNumber${index}, @ConfirmationDate${index}, @ConfirmationStatus${index})${suffix}`;

    predictionConfirmationParams[`MaterialID${index}`] = material.MaterialID;
    predictionConfirmationParams[`RFQID${index}`] = material.RFQID;
    predictionConfirmationParams[`ExtractedBrand${index}`] =
      material.ExtractedBrand;
    predictionConfirmationParams[`ExtractedPartNumber${index}`] =
      material.ExtractedPartNumber;
    predictionConfirmationParams[`PredictionStatus${index}`] =
      material.PredictionStatus;
    predictionConfirmationParams[`PredictionDate${index}`] =
      material.PredictionDate;
    predictionConfirmationParams[`ConfirmedBrand${index}`] =
      material.ConfirmedBrand;
    predictionConfirmationParams[`ConfirmedPartNumber${index}`] =
      material.ConfirmedPartNumber;
    predictionConfirmationParams[`ConfirmationStatus${index}`] =
      material.ConfirmationStatus;
    predictionConfirmationParams[`ConfirmationDate${index}`] =
      material.ConfirmationDate;
  });

  await scrapedDataset.query({
    query: materialQuery,
    params: materialParams,
  });

  await mainDataset.query({
    query: predictionConfirmationQuery,
    params: predictionConfirmationParams,
  });

  return apiResponse(ADD_SUCCESS, "RFQ", null, res);
});

const updateRFQ = apiHandler(async (req, res) => {
  const { userId } = req.user;
  const {
    rfqId,
    RFQ_Number,
    RFQ_Name,
    Company_Name,
    Portal,
    Priority,
    Deadline,
    Address_Information,
    Additional_Notes,
    Delivery_Date,
    URL,
    Client_Id,
    existingMaterials,
    newMaterials,
  } = req.body;

  await scrapedDataset.query({
    query: `
      UPDATE RFQ SET
      RFQ_Number=@RFQ_Number, RFQ_Name=@RFQ_Name, RFQ_Date=@RFQ_Date, Company_Name=@Company_Name, Portal=@Portal, Priority=@Priority, Deadline=@Deadline, Address_Information=@Address_Information, Additional_Notes=@Additional_Notes, Delivery_Date=@Delivery_Date, URL=@URL, Created_By=@CLient_Id
      WHERE (RFQ_ID=@rfqId)
    `,
    params: {
      rfqId: parseInt(rfqId),
      Client_Id: Client_Id.toString(),
      RFQ_Number: RFQ_Number || "",
      RFQ_Name: RFQ_Name,
      RFQ_Date: setDate(),
      Company_Name,
      Portal: Portal || "",
      Priority: Priority || "",
      Deadline,
      Address_Information: Address_Information || "",
      Additional_Notes: Additional_Notes || "",
      Delivery_Date: Delivery_Date || null,
      URL: URL || "",
    },
    types: Delivery_Date
      ? {}
      : {
          Delivery_Date: "date",
        },
  });

  const [materialsinDBArray] = await scrapedDataset.query({
    query: `
    SELECT  Material_ID FROM Material
    WHERE RFQ_ID=@rfqId
  `,
    params: {
      rfqId: parseInt(rfqId),
    },
  });

  let materialsinDB = materialsinDBArray.map(
    (material) => material.Material_ID
  );

  let materialsbyUser = existingMaterials.map((material) =>
    parseInt(material.Material_ID)
  );

  materialsToRemove = [];

  for (const materialinDB of materialsinDB) {
    if (!materialsbyUser.includes(materialinDB)) {
      materialsToRemove.push(materialinDB);
    }
  }

  for (const material of materialsToRemove) {
    await scrapedDataset.query({
      query: `
      DELETE FROM Material 
      WHERE (Material_ID=@material)
    `,
      params: {
        material,
      },
    });
  }

  let i = 0;
  for (const material of existingMaterials) {
    i++;
    await scrapedDataset.query({
      query: `
      UPDATE Material SET
      Part_Number=@Part_Number, Quantity_Required=@Quantity_Required, Material_Description=@Material_Description, Notes=@Notes, orders=@orders
      WHERE (RFQ_ID=@rfqId AND Material_ID=@Material_ID)
    `,
      params: {
        Material_ID: parseInt(material.Material_ID),
        rfqId: parseInt(rfqId),
        Part_Number: material.Part_Number,
        Quantity_Required: material.Quantity_Required,
        Material_Description: material.Material_Description,
        Notes: material.Notes,
        // Position: material.Position,
        orders: i,
      },
    });

    const [existingPartNumber] = await mainDataset.query({
      query: `
        SELECT * FROM partnumbers
        WHERE manufacturer_catalog_number=@Part_Number
      `,
      params: {
        Part_Number: material.Part_Number,
      },
    });

    if (isEmpty(existingPartNumber[0])) {
      const [newPartNumber] = await mainDataset.query({
        query: `
          INSERT INTO partnumbers (id, manufacturer_catalog_number, description, manufacturer_name)
          VALUES (@ID, @Part_Number, @description, @brand)
        `,
        params: {
          ID: generateID().toString(),
          Part_Number: material.Part_Number,
          description: material.Material_Description,
          brand: material.brand,
        },
      });
    } else {
      const [updatedPartNumber] = await mainDataset.query({
        query: `
          UPDATE partnumbers SET
          description = @description, manufacturer_name = @brand
          WHERE manufacturer_catalog_number=@Part_Number
        `,
        params: {
          Part_Number: material.Part_Number,
          description: material.Material_Description,
          brand: material.brand,
        },
      });
    }

    await mainDataset.query({
      query: `
      UPDATE Predictions_Confirmations SET
      ExtractedBrand=@ExtractedBrand, ExtractedPartNumber=@ExtractedPartNumber, PredictionDate=@PredictionDate, ConfirmedBrand=@ConfirmedBrand, ConfirmedPartNumber=@ConfirmedPartNumber, ConfirmationStatus=@ConfirmationStatus, ConfirmationDate=@ConfirmationDate
      WHERE (RFQID=@rfqId AND MaterialID=@Material_ID)
      `,
      params: {
        Material_ID: parseInt(material.Material_ID),
        rfqId: parseInt(rfqId),
        PredictionDate: setDate(),
        ExtractedBrand: material.brand,
        ExtractedPartNumber: material.Part_Number,
        ConfirmationDate: setDate(),
        ConfirmedBrand: material.brand,
        ConfirmedPartNumber: material.Part_Number,
        ConfirmationStatus: "Done",
      },
    });
  }
  for (const material of newMaterials) {
    i++;
    let Material_ID = generateID();
    await scrapedDataset.query({
      query: `
        INSERT INTO Material (Material_ID, RFQ_ID, Part_Number, Quantity_Required, Material_Description, Notes, orders)
        VALUES
        (@Material_ID, @RFQ_ID, @Part_Number, @Quantity_Required, @Material_Description, @Notes, @orders)
        `,
      params: {
        Material_ID: Material_ID,
        RFQ_ID: parseInt(rfqId),
        Part_Number: material.Part_Number,
        Quantity_Required: material.Quantity_Required,
        Material_Description: material.Material_Description || "",
        Notes: material.Notes || "",
        // Position: material.Position || "",
        orders: i,
      },
    });

    const [existingPartNumber] = await mainDataset.query({
      query: `
        SELECT * FROM partnumbers
        WHERE manufacturer_catalog_number=@Part_Number
      `,
      params: {
        Part_Number: material.Part_Number,
      },
    });

    if (isEmpty(existingPartNumber[0])) {
      const [newPartNumber] = await mainDataset.query({
        query: `
          INSERT INTO partnumbers (id, manufacturer_catalog_number, description, manufacturer_name)
          VALUES (@ID, @Part_Number, @description, @brand)
        `,
        params: {
          ID: generateID().toString(),
          Part_Number: material.Part_Number,
          description: material.Material_Description,
          brand: material.brand,
        },
      });
    } else {
      const [updatedPartNumber] = await mainDataset.query({
        query: `
          UPDATE partnumbers SET
          description = @description, manufacturer_name = @brand
          WHERE manufacturer_catalog_number=@Part_Number
        `,
        params: {
          Part_Number: material.Part_Number,
          description: material.Material_Description,
          brand: material.brand,
        },
      });
    }

    await mainDataset.query({
      query: `
        INSERT INTO Predictions_Confirmations (MaterialID, RFQID, ExtractedBrand, ExtractedPartNumber, PredictionStatus, PredictionDate, ConfirmedBrand, ConfirmedPartNumber, ConfirmationStatus, ConfirmationDate)
        VALUES
        (@MaterialID, @RFQID, @ExtractedBrand, @ExtractedPartNumber, @PredictionStatus, @PredictionDate, @ConfirmedBrand, @ConfirmedPartNumber, @ConfirmationStatus, @ConfirmationDate)
        `,
      params: {
        MaterialID: Material_ID,
        RFQID: parseInt(rfqId),
        ExtractedBrand: material.brand,
        ExtractedPartNumber: material.Part_Number,
        PredictionStatus: "Done",
        PredictionDate: setDate(),
        ConfirmationDate: setDate(),
        ConfirmedBrand: material.brand,
        ConfirmedPartNumber: material.Part_Number,
        ConfirmationStatus: "Done",
      },
    });
  }
  return apiResponse(UPDATE_SUCCESS, "RFQ", null, res);
});

const getPortalNames = apiHandler(async (req, res) => {
  const [portals] = await bigQueryClient.query({
    query: `
      SELECT DISTINCT Portal
      FROM ${DATASET_ID_SCRAPE}.RFQ`,
  });

  let Portalnames = portals.map((portal) => portal.Portal);
  return apiResponse(FETCH, "Portals", Portalnames, res);
});

const discardRFQ = apiHandler(async (req, res) => {
  const { role } = req.user;
  const { id } = req.params;

  currentDate = setDate();

  if (role !== "SUPERVISOR") {
    return apiError(UNAUTHORIZED, "User", null, res);
  }

  const [existingRFQ] = await mainDataset.query({
    query: `
      SELECT RFQID
      FROM RFQ_Status
      WHERE RFQID = @rfqId
    `,
    params: { rfqId: parseInt(id) },
  });

  if (isEmpty(existingRFQ)) {
    await mainDataset.query({
      query: `
        INSERT INTO RFQ_Status (RFQID, CurrentStatus, StatusChangeDate, AssignedUser, timestamp_date) 
        VALUES 
        (@RFQ_ID, @CurrentStatus, @StatusChangeDate, @AssignedUser, CURRENT_TIMESTAMP())
        `,
      params: {
        RFQ_ID: parseInt(id),
        CurrentStatus: "IRRELEVANT",
        StatusChangeDate: setDate(),
        AssignedUser: req.user.userId,
      },
    });
  }

  await mainDataset.query({
    query: `UPDATE RFQ_Status 
    SET 
    CurrentStatus = 'IRRELEVANT', 
    StatusChangeDate = @currentDate
    WHERE rfqId=@rfqId;
    `,
    params: {
      rfqId: parseInt(id),
      currentDate,
    },
  });

  return apiResponse(UPDATE_SUCCESS, "RFQ", null, res);
});

const getReasons = apiHandler(async (req, res) => {
  const reasons = [
    "No part number available",
    "Part number discontinued",
    "Suppliers don’t have stock available",
    "Suppliers cannot meet requested delivery time",
    "Suppliers did not provide quote within available time",
    "RFQ cancelled by client",
    "Reserved by mistake",
    "Missing Information",
    "Not Complying with Remiex Policies",
    "Order Amount Below Minimum",
  ];
  return apiResponse(FETCH, "Reasons", reasons, res);
});

const releaseRFQ = apiHandler(async (req, res) => {
  const { rfqId, changeStatusToNew, reason } = req.body;
  const { userId } = req.user;

  if (changeStatusToNew) {
    await mainDataset.query({
      query: `
        UPDATE RFQ_Status as rs
        SET CurrentStatus = "NEW", StatusChangeDate = @date, AssignedUser = NULL
        WHERE rs.RFQID = @rfqId
      `,
      params: {
        date: setDate(),
        rfqId: parseInt(rfqId),
      },
    });
  } else {
    await mainDataset.query({
      query: `
        UPDATE RFQ_Status as rs
        SET CurrentStatus = "IRRELEVANT", StatusChangeDate = @date
        WHERE rs.RFQID = @rfqId
      `,
      params: {
        date: setDate(),
        rfqId: parseInt(rfqId),
      },
    });
  }

  let release_reason = [
    {
      user: userId,
      reason: reason,
      date: setDate(),
    },
  ];

  const [release_reason_array] = await scrapedDataset.query({
    query: `
      SELECT Release_Reason FROM RFQ
      WHERE RFQ_ID = @rfqId
    `,
    params: {
      rfqId: parseInt(rfqId),
    },
  });

  const release_reason_stringify = JSON.stringify(release_reason);

  if (isEmpty(release_reason_array)) {
    await scrapedDataset.query({
      query: `
        UPDATE RFQ SET 
        Release_Reason = @release_reason_stringify
        WHERE RFQ_ID = @rfqId
      `,
      params: {
        rfqId: parseInt(rfqId),
        release_reason_stringify: release_reason_stringify,
      },
    });
  } else {
    let existingReasonsArray = JSON.parse(
      release_reason_array[0].Release_Reason
    );

    existingReasonsArray.push(release_reason[0]);

    let updatedReasonsStringify = JSON.stringify(existingReasonsArray);

    await scrapedDataset.query({
      query: `
        UPDATE RFQ SET 
        Release_Reason = @updatedReasonsStringify
        WHERE RFQ_ID = @rfqId
      `,
      params: {
        rfqId: parseInt(rfqId),
        updatedReasonsStringify: updatedReasonsStringify,
      },
    });
  }

  return apiResponse(UPDATE_SUCCESS, "RFQStatus", null, res);
});

module.exports = {
  RFQStatus,
  getAllRFQ,
  getAutomatedRFQ,
  getSingleRFQ,
  updateRFQStatus,
  updateRFQ,
  generateRFQ,
  getPortalNames,
  discardRFQ,
  getReasons,
  releaseRFQ,
  getRFQWithConfirmedMaterials,
};
