import React, { useState, useEffect } from 'react';
import MainCard from 'components/MainCard';
import { Grid, FormControl, Typography, Button, Select, MenuItem, IconButton } from '@mui/material';
import GlobalTimePicker from 'pages/component/timefield';
import { addTimeSlotAction, getTimeSlots, setTimeSlot } from 'redux/reducers/settingReducer';
import { useDispatch, useSelector } from 'react-redux';
import Loader from 'components/Loader';
import { showAlert } from 'utils/helper';
import dayjs from 'dayjs';
import { Delete } from '@mui/icons-material';
import SelectComponent from 'pages/component/selectComponent';
import DynamicAutocomplete from 'pages/component/autoComplete';
import CloseIcon from '@mui/icons-material/Close';
import { getAllUserAction } from 'redux/reducers/userReducer';
import { getAllKamAction } from 'redux/reducers/RfqReducer';
import { get } from 'lodash';
import { days } from '../constant';

// Fields configuration
const fields = [
  { name: 'FromTime', label: 'From', type: 'time', className: 'time-picker' },
  { name: 'ToTime', label: 'To', type: 'time', className: 'time-picker' }
];

const TimeConfig = () => {
  const [times, setTimes] = useState([]);
  const [selectedUser, setSelectedUser] = useState('');
  const setting = useSelector((state) => state.setting);
  const kamListData = useSelector((state) => state.rfq);
  const [kamUsers, setAllKamUser] = useState([]);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setTimeSlot([]));
    dispatch(getAllKamAction());
  }, []);

  useEffect(() => {
    if (selectedUser) {
      dispatch(getTimeSlots(selectedUser));
    }
  }, [selectedUser]);

  useEffect(() => {
    setAllKamUser(
      get(kamListData, 'kamList', [])?.map((userData) => ({
        value: userData?.UserID,
        label: `${userData.FirstName} ${userData.LastName}`?.trim()
      }))
    );
  }, [kamListData?.kamList]);

  useEffect(() => {
    const formattedData = get(setting, 'timeSlot', [])?.map((item) => ({
      ...item,
      from: item.FromTime,
      to: item.FromTime,
      FromTime: item.FromTime,
      ToTime: item.ToTime
    }));

    if (formattedData && formattedData?.length > 0) {
      setTimes([...formattedData]);
    } else {
      setTimes([]);
    }
  }, [setting?.timeSlot]);

  const handleTimeChange = (index, field, newValue) => {
    setTimes((prevTimes) => prevTimes.map((item, i) => (i === index ? { ...item, [field]: newValue } : item)));
  };

  const handleAddTimeSlot = () => {
    if (selectedUser === '') {
      showAlert(dispatch, false, 'Please select a user', true);
      return;
    }

    const newSlot = {
      UserID: selectedUser,
      Day: '',
      FromTime: '',
      ToTime: '',
      ID: times.length + 1
    };
    setTimes((prevTimes) => [...prevTimes, newSlot]);
  };

  const handleRemoveTimeSlot = (index) => {
    setTimes((prevTimes) => prevTimes.filter((_, i) => i !== index));
  };

  const submit = () => {
    let isValid = true;

    // Check if the time is a valid UNIX timestamp
    const isValidTimestamp = (timeStr) => {
      const time = dayjs.unix(Number(timeStr));
      return time.isValid();
    };

    for (const item of times) {
      // Validate from and to times
      const fromTime = isValidTimestamp(item.FromTime);
      const toTime = isValidTimestamp(item.ToTime);
      if (!item.Day) {
        isValid = false;
        showAlert(dispatch, false, `Day is required`, true);
        return;
      }
      if (!item.FromTime) {
        isValid = false;
        showAlert(dispatch, false, `From Time is required`, true);
        return;
      }
      if (!item.ToTime) {
        isValid = false;
        showAlert(dispatch, false, `To Time is required`, true);
        return;
      }
      if (!fromTime || !toTime) {
        isValid = false;
        showAlert(dispatch, false, `Invalid date format for ${item.Day}`, true);
        return;
      }

      const fromDayjs = dayjs.unix(Number(item.FromTime));
      const toDayjs = dayjs.unix(Number(item.ToTime));

      // Check if 'From' time is after 'To' time
      if (fromDayjs.isAfter(toDayjs)) {
        isValid = false;
        showAlert(dispatch, false, `"To" time must be later than "From" time for ${item.Day}`, true);
        return;
      }
    }

    if (isValid) {
      const formattedTimes = times.map((item) => {
        const fromTime = item.FromTime;
        const toTime = item.ToTime;

        return {
          Day: item.Day,
          FromTime: fromTime?.toString(),
          ToTime: toTime?.toString()
        };
      });

      dispatch(addTimeSlotAction({ UserID: selectedUser?.toString(), timeSlots: formattedTimes }));
    }
  };
  const renderField = (field, index, value) => {
    const { className, label, type, name } = field || {};
    switch (type) {
      case 'time':
        return (
          <>
            <Typography variant="h6" color="secondary">
              {label}
            </Typography>
            <GlobalTimePicker className={className} value={value} onChange={(newValue) => handleTimeChange(index, name, newValue)} />
          </>
        );
      default:
        return null;
    }
  };
  const checkDisabled = () => {};
  return (
    <>
      {setting?.loading && <Loader />}

      <Grid container spacing={2} mt={1}>
        <Typography variant="h5" color="secondary" ml={2}>
          Assign Time Slots to KAM Users
        </Typography>

        <Grid item xs={12} ml={2}>
          <FormControl fullWidth>
            <div style={{ maxWidth: '400px' }}>
              <DynamicAutocomplete
                // label='Select KAM'
                placeholder="Search KAM user"
                value={kamUsers?.find((user) => user.value === selectedUser) || null}
                onChange={(event, newValue) => {
                  const value = newValue ? newValue.value : '';
                  setSelectedUser(value);
                }}
                options={kamUsers || []}
                getOptionLabel={(option) => option?.label || ''}
                isLoading={kamListData?.loading || false}
              />
            </div>
          </FormControl>
        </Grid>

        <Grid item xs={12} ml={2}>
          <Button onClick={handleAddTimeSlot} variant="contained" disabled={!selectedUser}>
            Add Time Slot
          </Button>
        </Grid>
        <Grid item xs={12} ml={2}>
          <Grid container spacing={2} mt={2}>
            {times?.map((slot, index) => (
              <Grid item xs={12} key={slot.ID} m={1} className="time-slot-row">
                <FormControl fullWidth>
                  <Grid container>
                    <Grid item xs={4}>
                      <Typography>Day</Typography>
                      <div style={{ maxWidth: '200px' }}>
                        <SelectComponent
                          value={slot.Day}
                          placeholder="Select Day"
                          onSelectChange={(e) => handleTimeChange(index, 'Day', e.target.value)}
                          fullWidth
                          items={days}
                        />
                      </div>
                    </Grid>

                    {fields?.map((field) => (
                      <Grid item xs={3} key={field.name} ml={2}>
                        {renderField(field, index, slot[field.name])}
                      </Grid>
                    ))}

                    <Grid item sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                      <IconButton onClick={() => handleRemoveTimeSlot(index)} color="error">
                        <CloseIcon />
                      </IconButton>
                    </Grid>
                  </Grid>
                </FormControl>
              </Grid>
            ))}
          </Grid>
        </Grid>
        <Grid container mt={2} justifyContent="flex-end">
          <Button onClick={submit} variant="contained" disabled={setting?.loading || times?.length === 0}>
            Submit
          </Button>
        </Grid>
      </Grid>
    </>
  );
};

export default TimeConfig;
