import React, { useEffect, useRef, useState } from 'react';
import pdflogo from '../../../assets/images/users/pdflogo.png';
import { english, spanish } from './constant';
import { convertDateToStringFormat, formattedPrice } from 'utils/helper';
import { fontSize } from '@mui/system';
import TermsAndConditions from './spanishTerms&conditions';
const PDFGenerator = ({ singleOffer }) => {
  const [pdfData, setPdfData] = useState({});
  const isEnglishPDF = singleOffer?.Language === 'English';

  useEffect(() => {
    if (singleOffer) {
      if (isEnglishPDF) {
        setPdfData(english);
      } else {
        setPdfData(spanish);
      }
    }
  }, [singleOffer, isEnglishPDF]);

  const styles = {
    container: {
      width: '100%',
      padding: '30px 0',
      fontFamily: 'sans-serif'
    },
    header: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 44px',
      borderBottom: '2px solid #E1E1E1',
      paddingBottom: '60px'
    },
    logoContainer: {
      display: 'flex',
      width: '50%',
      justifyContent: 'space-between'
    },
    logo: {
      width: '30%',
      height: 'auto'
    },
    remiexInfo: {
      width: '67%',
      fontSize: '22px',
      marginTop: '36px'
    },
    rootContainer: {
      border: '4px solid orange',
      width: 'fit-content',
      padding: '40px 62px',
      color: 'orange',
      textAlign: 'center',
      borderRadius: '14px'
    },
    rootTitle: {
      fontSize: '38px',
      margin: '0',
      marginBottom: '12px',
      lineHeight: '1.3'
    },
    cotizacionTitle: {
      fontSize: '38px',
      fontWeight: '600',
      margin: '0',
      lineHeight: '1.3'
    },
    content: {
      padding: '12px 44px'
    },
    section: {
      display: 'flex',
      marginTop: '7px',
      fontSize: '22px',
      justifyContent: 'space-between'
    },
    sectionItem: {
      width: '30%'
    },
    itemTitle: {
      color: '#969BA5',
      fontWeight: '600'
    },
    tableContainerLeft: {
      minHeight: '40vw',
      width: '60%',
      border: '1px solid rgb(175 175 175)',
      borderRadius: '6px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px'
    },
    tableContainerRight: {
      minHeight: '40vw',
      width: '40%',
      border: '1px solid rgb(175 175 175)',
      borderRadius: '6px',
      borderTopLeftRadius: '0px',
      borderBottomLeftRadius: '0px',
      borderLeft: '0'
    },
    commentBox: {
      border: '1px solid rgb(175 175 175)',
      borderRadius: '6px',
      width: '61%',
      padding: '12px 20px'
    },
    discountBox: {
      border: '1px solid rgb(175 175 175)',
      borderRadius: '6px',
      width: '32%',
      padding: '12px 20px',
      minHeight: '120px'
    },
    commentTitle: {
      fontSize: '22px',
      color: '#969BA5',
      fontWeight: '600'
    },
    totalText: {
      fontSize: '18px',
      fontWeight: '600'
    },
    itemText: {
      color: 'black',
      fontSize: '18px'
    },
    tableCell: {
      overflow: 'break-word',
      maxWidth: '200px',
      wordBreak: 'break-all',
      padding: '8px'
    }
  };
  const offerDate = new Date(singleOffer?.OfferDate?.value);
  const tableHeaders = [
    { title: 'Item', dataKey: 'index' },
    { title: pdfData?.partNumber, dataKey: 'partNumber' },
    { title: pdfData?.description, dataKey: 'materialDescription' },
    { title: pdfData?.brand, dataKey: 'brand' },
    { title: pdfData?.qty, dataKey: 'quantity' },
    { title: pdfData?.leadTime, dataKey: 'leadTime' },
    { title: pdfData?.unitPrice, dataKey: 'unitOfferPriceWithHandlingCost' },
    // { title: 'Rec/Desc', dataKey: 'recDesc' },

    { title: pdfData?.total, dataKey: 'totalCostAfterMargin' }
  ];

  const getValidUntilDate = (offerDate, validFor) => {
    if (!validFor || !(offerDate || offerDate?.value)) return 'N/A';
    let rawDate = typeof offerDate === 'object' && offerDate?.value ? offerDate.value : offerDate;
    let baseDate = new Date(rawDate);
    if (!baseDate || isNaN(baseDate)) return 'N/A';
    baseDate.setDate(baseDate.getDate() + validFor);

    return convertDateToStringFormat(baseDate, isEnglishPDF ? 'dd/mm/yyyy' : '');
  };

  const offerFields = [
    {
      label: pdfData?.name,
      value: singleOffer?.Name
    },
    {
      label: pdfData?.city,
      value: singleOffer?.Municipality,
      condition: !!pdfData?.city
    },
    {
      label: pdfData?.buisness,
      value: singleOffer?.BusinessActivity,
      condition: !!pdfData?.buisness
    },
    {
      label: pdfData?.id,
      value: singleOffer?.ClientID
    },
    {
      label: pdfData?.address,
      value: (
        <>
          {singleOffer?.Address}
          <br />
          {singleOffer?.State}, {singleOffer?.Country}
        </>
      )
    },
    {
      label: pdfData?.paymentTerm,
      value: singleOffer?.PaymentTerms
    },
    {
      label: pdfData?.representative,
      value: `${singleOffer?.FirstName || ''} ${singleOffer?.LastName || ''} ${singleOffer?.Email || ''}`
    },
    {
      label: pdfData?.currency,
      value: singleOffer?.OfferCurrency
    },
    {
      label: pdfData?.country,
      value: singleOffer?.Municipality,
      condition: !!pdfData?.country
    },
    {
      label: pdfData?.date,
      value: singleOffer?.OfferDate ? convertDateToStringFormat(singleOffer?.OfferDate, isEnglishPDF ? 'dd/mm/yyyy' : '') : 'N/A'
    },
    {
      label: pdfData?.validUntil,
      value: getValidUntilDate(singleOffer?.OfferDate, singleOffer?.ValidFor)
    }
  ];

  return (
    <div style={{ width: '100%', padding: '30px 0', fontFamily: 'sans-serif' }}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 44px',
          borderBottom: '2px solid #E1E1E1',
          paddingBottom: ' 60px'
        }}
      >
        <div style={{ display: 'flex', width: '50%', justifyContent: 'flex-start', gap: '10px', ...styles.itemText }}>
          <img src={pdflogo} alt="logo" style={styles?.logo} />
          <div style={styles?.remiexInfo}>
            <strong style={{ lineHeight: 1.6 }}>{pdfData?.heading?.company}</strong>
            <p style={{ lineHeight: 1.6, margin: 0 }}>
              {pdfData?.heading?.address1} <br />
              {pdfData?.heading?.ABN && (
                <>
                  {pdfData.heading.ABN}
                  <br />
                </>
              )}
              {pdfData?.heading?.address2}
              <br />
              {isEnglishPDF && 'QLD Australia'}
              {isEnglishPDF && <br />}
              {pdfData?.heading?.phone}: {pdfData?.heading?.phoneValue}
              <br />
              {pdfData?.heading?.email}: {pdfData?.heading?.emailValue}
            </p>
          </div>
        </div>
        <div style={styles?.rootContainer}>
          {pdfData?.headerRight?.heading && <h2 style={styles?.rootTitle}>{pdfData?.headerRight?.heading}</h2>}
          <h3 style={styles?.cotizacionTitle}>
            {pdfData?.headerRight?.subHeading} <br /> N° {singleOffer?.RFQ_ID}
          </h3>
        </div>
      </div>
      <>
        <div style={styles?.content}>
          <div style={styles?.section}>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px', borderBottom: '2px solid #E1E1E1', paddingBottom: '10px' }}>
              {offerFields
                ?.filter((field) => field.label && (field.condition === undefined || field.condition)) // Skip falsy labels or unmet conditions
                ?.map((field, index) => (
                  <div
                    key={index}
                    style={{
                      flex: '1 1 25%',
                      maxWidth: '20%',
                      marginLeft: '50px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <b style={{ color: '#969BA5', fontWeight: 600 }}>{field.label}</b>
                    <p style={{ margin: 0, ...styles.itemText }}>{field.value || 'N/A'}</p>
                  </div>
                ))}
            </div>
          </div>
        </div>
        <div style={{ display: 'flex', padding: '12px 44px' }}>
          <div
            style={{
              width: '100%',
              minWidth: '600px',
              border: '1px solid rgb(175, 175, 175)',
              borderRadius: '6px',
              minHeight: '40vw'
            }}
          >
            <table style={{ width: '100%', borderCollapse: 'collapse' }} cellSpacing="0" cellPadding="0">
              <thead>
                <tr>
                  {tableHeaders?.map((header, index) => (
                    <th
                      key={index}
                      style={{
                        backgroundColor: '#F7F7F7',
                        fontSize: '18px',
                        paddingBottom: '4px',
                        paddingTop: '12px',
                        fontWeight: 600,
                        textAlign: 'left',
                        borderBottom: '1px solid rgb(175, 175, 175)',
                        borderLeft: header?.dataKey === 'quantity' && '1px solid rgb(175, 175, 175)',
                        ...styles.itemText
                      }}
                    >
                      {header.title}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody style={{ verticalAlign: 'top' }}>
                {singleOffer?.Quotes?.map((material, index) => (
                  <tr key={index}>
                    <td
                      style={{
                        ...styles.itemText,
                        ...styles.tableCell
                      }}
                    >
                      {index + 1 || '-'}
                    </td>
                    <td
                      style={{
                        ...styles.itemText,
                        ...styles.tableCell
                      }}
                    >
                      {material?.partNumber || '-'}
                    </td>
                    <td
                      style={{
                        ...styles.itemText,
                        ...styles.tableCell
                      }}
                      title={material?.materialDescription}
                    >
                      {material?.materialDescription || '-'}
                    </td>

                    <td
                      style={{
                        ...styles.itemText,
                        ...styles.tableCell
                      }}
                    >
                      {material?.brand || '-'}
                    </td>

                    <td
                      style={{
                        ...styles.itemText,
                        ...styles.tableCell,
                        borderLeft: '1px solid rgb(175, 175, 175)'
                      }}
                    >
                      {material?.quantity} UN
                    </td>
                    <td
                      style={{
                        ...styles.itemText,
                        ...styles.tableCell
                      }}
                    >
                      {material?.leadTime || '-'}
                    </td>
                    <td
                      style={{
                        ...styles.itemText,
                        ...styles.tableCell
                      }}
                    >
                      ${' '}
                      {formattedPrice(
                        material?.pdfUnitOfferPriceWithHandlingCost || material?.unitOfferPriceWithHandlingCost || material?.unitPrice,
                        isEnglishPDF
                      ) || '0.00'}
                    </td>

                    <td
                      style={{
                        ...styles.itemText,
                        ...styles.tableCell
                      }}
                    >
                      ${' '}
                      {formattedPrice(
                        material?.pdfExchangedOfferCostWithHandlingCost || material?.totalCostAfterMargin || material?.totalCost,
                        isEnglishPDF
                      ) || '0.00'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div style={{ display: 'flex', justifyContent: 'space-between', padding: '12px 44px' }}>
          <div style={{ border: '1px solid rgb(175 175 175)', borderRadius: '6px', width: '61%', padding: '12px 20px' }}>
            <b style={{ fontSize: '24px', color: 'rgb(***********)', fontWeight: 500 }}>{pdfData?.comments}</b>
            <p style={{ margin: 0, ...styles.itemText, fontSize: '18px' }}>
              {singleOffer?.Notes?.split('\n').map((line, index) => (
                <span key={index}>
                  {line}
                  <br />
                </span>
              ))}
            </p>
          </div>

          <div style={{ border: '1px solid rgb(175 175 175)', borderRadius: '6px', width: '32%', padding: '12px 20px' }}>
            <div style={{ minHeight: '120px' }}>
              {pdfData?.discount && (
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <p style={{ fontSize: '24px', color: 'rgb(***********)', margin: 0 }}>{pdfData?.discount}.</p>
                  <p style={{ margin: 0, fontSize: '24px', margin: 0, ...styles.itemText }}>
                    $ {formattedPrice(singleOffer?.discount, isEnglishPDF) || '0.00'}
                  </p>
                </div>
              )}
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <p style={{ fontSize: '24px', color: 'rgb(***********)', margin: 0 }}>{pdfData?.subTotal}</p>
                <p style={{ margin: 0, fontSize: '24px', margin: 0, ...styles.itemText }}>
                  $ {formattedPrice(singleOffer?.pdfSubTotal || singleOffer?.SubTotal, isEnglishPDF) || '0.00'}
                </p>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <p style={{ fontSize: '24px', color: 'rgb(***********)', margin: 0 }}> {pdfData?.tax}</p>

                <p style={{ margin: 0, fontSize: '24px', margin: 0, ...styles.itemText }}>
                  $ {formattedPrice(singleOffer?.pdfTax || singleOffer?.Tax, isEnglishPDF) || '0.00'}
                </p>
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <p style={{ fontSize: '24px', color: 'rgb(***********)', margin: 0 }}>{pdfData?.grandTotal}</p>
              <p style={{ margin: 0, fontSize: '24px', margin: 0, color: 'orange' }}>
                $ {formattedPrice(singleOffer?.pdfGrandTotal || singleOffer?.GrandTotal, isEnglishPDF) || '0.00'}
              </p>
            </div>
          </div>
        </div>
      </>
    </div>
  );
};
export default PDFGenerator;
