import { <PERSON>, Button, Grid, Stack, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import { Link } from 'react-router-dom';
import { checkIsRfqReserved, convertDateToStringFormat } from 'utils/helper';
import Release from './buttons/release/release';
import { get } from 'lodash';

const Overview = ({ rfqDetails, buttonsConfig, loading, isAutomatedRFQ, isLoading }) => {
  // Define the mapping for custom labels
  const labels = [
    { name: 'RFQ_ID', label: 'RFQ ID', type: 'text' },
    { name: 'RFQ_Name', label: 'Name', type: 'text' },
    { name: 'Deadline', label: 'Deadline', type: 'date' },
    { name: 'Delivery_Date', label: 'Delivery', type: 'date' },
    { name: 'RFQ_Date', label: 'Date', type: 'date' },
    { name: 'Portal', label: 'Portal', type: 'text' },
    { name: 'RFQ_Number', label: 'Number', type: 'text' },
    { name: 'URL', label: 'Url', type: 'text' },
    { name: 'Name', label: 'Client', type: 'text' },
    { name: 'kam', label: 'Kam', type: 'text' }
  ];

  // Create an array of label and value pairs based on the mapping
  const labelValuePairs = rfqDetails
    ? labels.map(({ name, label, type }) => {
        let value = rfqDetails[name];
        if (type === 'date' && value) {
          value = convertDateToStringFormat(value?.value);
        }
        return {
          label,
          value: value || 'N/A' // Default to 'N/A' if the value is not present
        };
      })
    : [];

  const renderKeyValuePairs = (entries) => {
    return (
      <Box className="over-view-box">
        {entries.map((entry, index) => (
          <Box key={index} className="over-view-inner-container">
            <Typography variant="caption" className="over-view-label">
              {entry.label}
            </Typography>
            <Typography variant="caption" className="over-view-value">
              <span className="material-value">
                {entry.label === 'Url' ? (
                  <Link to={entry?.value || '#'} className="link" target="_blank" rel="noopener noreferrer">
                    Link
                  </Link>
                ) : (
                  entry.value
                )}
              </span>
            </Typography>
          </Box>
        ))}
      </Box>
    );
  };

  const renderButtons = (buttonsConfig) => {
    return buttonsConfig?.map((button, index) => {
      if (button?.type === 'generate') {
        return (
          <Button
            key={index}
            variant="outlined"
            className="material-request-btn"
            size="small"
            onClick={button.onClick}
            disabled={button?.disabled}
            style={{ display: button.show ? 'block' : 'none' }}
          >
            {button.label}
          </Button>
        );
      } else if (button.type === 'close') {
        return (
          <Link key={index} to={button.link}>
            <Button
              variant="contained"
              size={!button?.smallBtn ? 'small' : ''}
              className="material-request-btn"
              disabled={button?.disabled}
            >
              {button.label}
            </Button>
          </Link>
        );
      } else if (button.type === 'component' && !button?.hide) {
        return <button.component key={index} variant="outlined" loading={loading} order={rfqDetails} pageUrl={button?.pageUrl} />;
      } else if (button.type === 'button') {
        return (
          <Button
            key={index}
            variant="outlined"
            className="material-request-btn"
            onClick={button.onClick}
            style={{ display: !button.show && 'none' }}
            disabled={button?.disabled}
          >
            {button.label}
          </Button>
        );
      } else {
        return null;
      }
    });
  };

  return (
    <MainCard sx={{ mt: 2 }} content={false}>
      <div style={{ display: 'flex', gap: '10px' }}>
        <Typography variant="h4" color="secondary">
          Overview
        </Typography>
        {isAutomatedRFQ && (
          <Typography variant="h6" mt={'4px'} sx={{ textAlign: 'center' }} color="secondary">
            ( Automated RFQ )
          </Typography>
        )}
      </div>
      <Stack spacing={1} mt={1}>
        <Grid container style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Grid xs={10}>
            <Grid spacing={2} container>
              <Grid item xs={12} md={6} sm={9}>
                <Box className="over-view-container">
                  <Grid container spacing={2}>
                    <Grid item xs={7}>
                      <Box className="secondary-container">
                        {renderKeyValuePairs(labelValuePairs.slice(0, Math.ceil(labelValuePairs.length / 2)))}
                      </Box>
                    </Grid>
                    <Grid item xs={5}>
                      <Box className="secondary-container">
                        {renderKeyValuePairs(labelValuePairs.slice(Math.ceil(labelValuePairs.length / 2)))}
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Grid>
              {!isLoading && get(rfqDetails, 'CurrentStatus') && get(rfqDetails, 'CurrentStatus') === 'VOID' && (
                <Grid item xs={12} md={6} sm={9}>
                  <Box className="over-view-container">
                    <Grid container spacing={2}>
                      <Grid item xs={7}>
                        <Box className="secondary-container">
                          <Box className="over-view-box">
                            <Box className="over-view-inner-container">
                              <Typography sx={{ minWidth: '110px !important' }} variant="caption" className="over-view-label-status">
                                Status
                              </Typography>
                              <Typography variant="caption" className="over-view-value-status">
                                <span className="material-value-void">{get(rfqDetails, 'CurrentStatus')}</span>
                              </Typography>
                            </Box>
                            <Box className="over-view-inner-container">
                              <Typography sx={{ minWidth: '110px !important' }} variant="caption" className="over-view-label">
                                RFQ Number
                              </Typography>
                              <Typography variant="caption" className="over-view-value">
                                <span className="material-value">{get(rfqDetails, 'RFQ_ID')}</span>
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Grid>
              )}

              {!isLoading && get(rfqDetails, 'Created_From_RFQ_ID') && (
                <Grid item xs={12} md={6} sm={9}>
                  <Box className="over-view-container">
                    <Grid container spacing={2}>
                      <Grid item xs={7}>
                        <Box className="secondary-container">
                          <Box className="over-view-box">
                            <Box className="over-view-inner-container">
                              <Typography sx={{ minWidth: '110px !important' }} variant="caption" className="over-view-label-status">
                                Status
                              </Typography>
                              <Typography variant="caption" className="over-view-value-status">
                                <span className="material-value-void">{get(rfqDetails, 'CurrentStatus')}</span>
                              </Typography>
                            </Box>
                            <Box className="over-view-inner-container">
                              <Typography sx={{ minWidth: '110px !important' }} variant="caption" className="over-view-label">
                                Copied From
                              </Typography>
                              <Typography variant="caption" className="over-view-value">
                                <span className="material-value">{get(rfqDetails, 'Created_From_RFQ_ID')}</span>
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Grid>
              )}
            </Grid>
          </Grid>
          <Grid>
            <Stack direction="column" spacing={1}>
              {renderButtons(buttonsConfig)}
            </Stack>
          </Grid>
        </Grid>
      </Stack>
    </MainCard>
  );
};

export default Overview;
