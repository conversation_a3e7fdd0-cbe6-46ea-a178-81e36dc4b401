import React, { useEffect, useState } from 'react';
import { Button, Grid, Typography, Box } from '@mui/material';
import ErrorMessage from 'pages/component/errorMessage';
import MainCard from 'components/MainCard';
import InputField from 'pages/component/inputField';
import SelectComponent from 'pages/component/selectComponent';
import TextAreaComponent from 'pages/component/textArea';
import DynamicAutocomplete from 'pages/component/autoComplete';
import { KAM, SUPERVISER, userPageUrl } from 'utils/constant';
import { useDispatch, useSelector } from 'react-redux';
import { addUserMaintenanace, getSingleUser, setSingleUserData, updateUserMaintenanace } from 'redux/reducers/userReducer';
import Loader from 'components/Loader';
import { validate } from 'pages/component/validation';
import { useNavigate, useParams } from 'react-router';
import { rolesList } from './constant';
import { getCountriesAction, getStatesAction } from 'redux/reducers/countryReducer';

// Define styles
const styles = {
  formContainer: {
    padding: '16px'
  },
  card: {
    margin: '16px 0'
  },
  buttonGroup: {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '10px'
  },
  inputField: {
    width: '100%'
  },
  textarea: {
    width: '100%',
    padding: '4px'
  },
  errorText: {
    color: 'red',
    marginTop: '4px'
  }
};

const initialState = {
  userId: '',
  name: '',
  municipality: '',
  businessActivity: '',
  address: ''
};

const AddEditUser = () => {
  const userData = useSelector((state) => state.users);
  const countriesData = useSelector((state) => state.countries);
  const [userDetails, setuserDetails] = useState(initialState);
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [errors, setErrors] = useState({});
  const userID = useParams()?.id;
  const dispatch = useDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    dispatch(getCountriesAction());
  }, []);

  useEffect(() => {
    setCountries(countriesData?.countries?.countries?.map((country) => ({ label: country?.Country, value: country?.CountryId })));
  }, [countriesData?.countries]);

  useEffect(() => {
    if (userDetails?.country) {
      setStates(countriesData?.states?.states?.map((state) => ({ label: state?.State, value: state?.StateId })));
    } else {
      setStates([]);
    }
  }, [countriesData?.states]);

  const fieldConfigurations = [
    {
      type: 'text',
      name: 'firstName',
      placeholder: "Enter user's first name",
      label: 'User First Name',
      showInput: true
    },
    {
      type: 'text',
      name: 'lastName',
      placeholder: "Enter user's last name",
      label: 'User Last Name',
      showInput: true
    },
    {
      type: 'email',
      name: 'email',
      placeholder: 'Enter email',
      label: 'Email',
      showInput: true,
      autoComplete: 'off'
    },
    {
      type: 'password',
      name: 'password',
      placeholder: 'Enter password',
      label: 'Password',
      showInput: !userID,
      autoComplete: 'new-password'
    },

    {
      type: 'autocomplete',
      name: 'role',
      placeholder: 'Search role',
      label: 'Role',
      showInput: true,
      items: rolesList
    },
    {
      type: 'autocomplete',
      label: 'Country',
      name: 'country',
      placeholder: 'Search country',
      items: countries || [],
      showInput: true
    },
    {
      type: 'autocomplete',
      label: 'State',
      name: 'state',
      placeholder: 'Search state',
      items: states || [],
      showInput: true
    },
    {
      type: 'text',
      label: 'Post Code',
      name: 'postalCode',
      placeholder: 'Enter Postal Code',
      showInput: true
    }
  ];

  useEffect(() => {
    if (userID) {
      dispatch(getSingleUser(userID));
    }
  }, [userID]);

  useEffect(() => {
    if (userID && userData?.singleUser) {
      const {
        FirstName: firstName,
        StateId: state,
        CountryId: country,
        Postal_Code: postalCode,
        LastName: lastName,
        Email: email,
        Role: role,
        userID: userId
      } = userData.singleUser || {};
      if (country) {
        dispatch(getStatesAction(country));
      }

      setuserDetails({
        userId,
        firstName,
        lastName,
        state,
        country,
        postalCode,
        email,
        role
      });
    } else {
      setuserDetails(initialState);
    }
  }, [userData?.singleUser]);

  const handleChange = (name, value) => {
    setuserDetails({ ...userDetails, [name]: value });
  };

  const handleSelectChange = (name, value) => {
    if (name === 'country') {
      setuserDetails({ ...userDetails, [name]: value, state: '' });
      setErrors({ ...errors, [name]: '' });
      dispatch(getStatesAction(value));
    } else {
      setuserDetails({ ...userDetails, [name]: value });
      setErrors({ ...errors, [name]: '' });
    }
  };

  const handleAutocompleteChange = (name) => (event, newValue) => {
    const value = newValue ? newValue.value : '';
    handleSelectChange(name, value);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    let rules = {
      firstName: { required: true, label: 'First Name' },
      lastName: { required: true, label: 'Last Name' },
      email: { required: true, type: 'email', label: 'Email' },
      role: { required: true, label: 'Role' },
      country: { required: true, label: 'Country' },
      state: { required: true, label: 'State' },
      postalCode: { required: true, label: 'Postal Code' }
    };

    if (!userID) {
      rules = { ...rules, password: { required: true, label: 'Password' } };
    }

    const validation = validate(userDetails, rules);
    setErrors(validation);

    if (Object.keys(validation).length === 0) {
      const { firstName, lastName, role, email, password, country, state, postalCode } = userDetails || {};
      const payload = {
        FirstName: firstName,
        LastName: lastName,
        Role: role,
        State: state,
        Country: country,
        Postal_Code: postalCode,
        Email: email,
        Password: password,
        navigate: navigate
      };

      if (userID) {
        dispatch(updateUserMaintenanace({ ...payload, UserID: userID?.toString() }));
      } else {
        dispatch(addUserMaintenanace(payload));
      }
    }
  };

  const renderField = (fieldConfig) => {
    const { type, name, placeholder, autoComplete, label, value, rows, items, showInput } = fieldConfig || {};

    if (!showInput) return null;

    switch (type) {
      case 'text':
      case 'email':
      case 'password':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="subtitle1" gutterBottom>
              {label}
            </Typography>
            <InputField
              type={type}
              name={name}
              placeholder={placeholder}
              value={userDetails[name] || ''}
              onChange={(e) => handleChange(name, e?.target?.value)}
              errors={errors}
              style={styles.inputField}
              fullWidth
              autoComplete={autoComplete}
            />
          </Grid>
        );

      case 'select':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="subtitle1" gutterBottom>
              {label}
            </Typography>
            <SelectComponent
              name={name}
              onSelectChange={(e) => handleSelectChange(name, e?.target?.value)}
              value={userDetails[name] || 'placeholder'}
              items={items}
              placeholder={placeholder}
              error={errors[name]}
              style={{ color: !userDetails[name] && 'rgb(168, 166, 166)' }}
            />
            {errors[name] && <ErrorMessage message={errors[name]} style={styles.errorText} />}
          </Grid>
        );

      case 'autocomplete':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="subtitle1" gutterBottom>
              {label}
            </Typography>
            <DynamicAutocomplete
              options={items || []}
              // label={label}
              placeholder={placeholder}
              value={items?.find((item) => item.value === userDetails[name]) || null}
              onChange={handleAutocompleteChange(name)}
              getOptionLabel={(option) => option?.label || ''}
              isLoading={name === 'country' ? countriesData?.loading : name === 'state' ? countriesData?.statesLoading : false}
            />
            {errors[name] && <ErrorMessage message={errors[name]} style={styles.errorText} />}
          </Grid>
        );

      case 'textarea':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="subtitle1" gutterBottom>
              {label}
            </Typography>
            <TextAreaComponent
              name={name}
              placeholder={placeholder}
              value={userDetails[name] || ''}
              onChange={(name, e) => handleChange(name, e?.target?.value)}
              minRows={rows}
              className="name-text-field"
              style={styles.textarea}
              error={errors[name]}
            />
            {errors[name] && <ErrorMessage message={errors[name]} style={styles.errorText} />}
          </Grid>
        );

      default:
        return null;
    }
  };

  const cancel = () => {
    dispatch(setSingleUserData({}));
    navigate(userPageUrl);
  };
  const { loading } = userData || {};

  return (
    <Grid>
      {(loading || countriesData?.loading) && <Loader />}
      <Box component="form" onSubmit={handleSubmit} style={styles.formContainer}>
        <MainCard className="maincard-boder" boxShadow={true} style={styles.card}>
          <Typography variant="h4" component="h1" gutterBottom color="secondary">
            {userID ? 'Update User' : 'New User'}
          </Typography>

          <Grid container spacing={3} mt={1}>
            {fieldConfigurations.map((config) => renderField(config))}
          </Grid>
        </MainCard>

        <MainCard sx={{ mt: 3 }} className="maincard-boder" boxShadow={true} style={styles.card}>
          <Grid item style={styles.buttonGroup}>
            <Button variant="outlined" color="secondary" onClick={() => cancel()}>
              Cancel
            </Button>
            <Button variant="contained" color="primary" type="submit">
              Save
            </Button>
          </Grid>
        </MainCard>
      </Box>
    </Grid>
  );
};

export default AddEditUser;
