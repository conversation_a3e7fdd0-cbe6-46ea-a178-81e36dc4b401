import { clientPageUrl, COUNTRY_MANAGER, KAM, SUPERVISER, supplierUrl, userPageUrl } from 'utils/constant';
import GroupAddIcon from '@mui/icons-material/GroupAdd';
// ==============================|| MENU ITEMS - DASHBOARD ||============================== //

const masterData = {
  id: 'group-dashboard',
  title: 'Master Data',
  type: 'collapse',
  icon: GroupAddIcon,
  children: [
    {
      id: 'supplier',
      title: 'Suppliers',
      type: 'item',
      accessRole: [SUPERVISER, KAM],
      url: supplierUrl,
      breadcrumbs: false
    },
    {
      id: 'client',
      title: 'Clients',
      type: 'item',
      url: clientPageUrl,
      accessRole: [SUPERVISER, KAM, COUNTRY_MANAGER],
      breadcrumbs: false
    },
    {
      id: 'User',
      title: 'Users',
      type: 'item',
      accessRole: [SUPERVISER, KAM],
      url: userPageUrl,
      breadcrumbs: false
    }
  ]
};

export default masterData;
