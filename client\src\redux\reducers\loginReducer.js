
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { post } from 'utils/axios';
import { setItemToLocalStorage } from 'utils/helper';
import { ALERT_SUCCESS } from './alertReducer';
import { loginApiUrl, rfqListPageUrl } from 'utils/constant';

export const loginUser = createAsyncThunk(loginApiUrl, async ({ email, password, navigate, dispatch }, { rejectWithValue }) => {
  try {
    const response = await post(loginApiUrl, { email, password }, false,dispatch);
    const { success, message, error, data } = get(response, 'data', {});
    if (success) {
      dispatch({
        type: ALERT_SUCCESS,
        payload: { success: true, message: message || 'Something went wrong', error: false }
      });
      setItemToLocalStorage('token', data?.token);
      setItemToLocalStorage('isProductManager', data?.isProductManager);
      dispatch(storeToken(data?.token));
      navigate(rfqListPageUrl);

      return data;
    }
    if (!success) {
      dispatch({
        type: ALERT_SUCCESS,
        payload: { success: false, message: message || 'Something went wrong', error: true }
      });
    }
  } catch (error) {
    dispatch({
      type: ALERT_SUCCESS,
      payload: { success: false, message: 'Something went wrong', error: true }
    });
    return rejectWithValue(error.message);
  }
});

const authSlice = createSlice({
  name: 'auth',
  initialState: {
    user: null,
    token: '',
    status: 'idle',
    error: null
  },
  reducers: {
    storeToken: (state, action) => {
      state.token = action.payload;
    },
    logoutSuccess: (state) => {
      state.user = null;
      state.token = '';
      state.status = 'idle';
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginUser.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.user = action.payload;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      });
  }
});

export const selectUser = (state) => state.auth.user;
export const selectAuthStatus = (state) => state.auth.status;
export const selectAuthError = (state) => state.auth.error;
export const { storeToken,logoutSuccess } = authSlice.actions;
export default authSlice.reducer;
