import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage'; // defaults to localStorage for web
import profileReducer from './reducers/profileReducer';
import RfqReducer from './reducers/RfqReducer';
import offerReducer from './reducers/offerReducer';
import alertReducer from './reducers/alertReducer';
import loginReducer from './reducers/loginReducer';
import singleRfq from './reducers/singleRfq';
import historyReducer from './reducers/historyReducer';
import myOffersReducer from './reducers/myOffersReducer';
import supplierPortalReducer from './reducers/supplierPortalReducer';
import priceMaitenanceReducer from './reducers/priceMaitenanceReducer';
import notificationReducer from './reducers/notificationReducer';
import supplierMaintenance from './reducers/supplierMaintenance';
import clientReducer from './reducers/clientReducer';
import userReducer from './reducers/userReducer';
import settingReducer from './reducers/settingReducer';
import pageTitleReducer from './reducers/pageTitleReducer';
import centralizedReducer from './reducers/centralizedReducer';
import countryReducer from './reducers/countryReducer';
const persistConfig = {
  key: 'root',
  storage
};

const persistedReducer = persistReducer(
  persistConfig,
  combineReducers({
    profile: profileReducer,
    rfq: RfqReducer,
    login: loginReducer,
    singleRfq: singleRfq,
    offer: offerReducer,
    alert: alertReducer,
    countries: countryReducer,
    history: historyReducer,
    myOffer: myOffersReducer,
    notification: notificationReducer,
    requestedQuote: supplierPortalReducer,
    supplier: supplierMaintenance,
    client: clientReducer,
    users: userReducer,
    priceMaitenance: priceMaitenanceReducer,
    setting: settingReducer,
    pageTitle: pageTitleReducer,
    requestedPrice: centralizedReducer
  })
);

const store = configureStore({
  reducer: persistedReducer
});

export default store;
