import React, { useState } from 'react';
import { Box, Button, Grid, TextField, Typography } from '@mui/material';

const SearchComponent = ({ material, searchMaterial, similarMaterials, loading, useMaterial }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearched, setIsSearched] = useState(false);
  const handleSearch = () => {
    if (searchTerm) {
      setIsSearched(true);
    }
    searchMaterial(searchTerm, true);
  };
  // Function to handle keydown event
  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };
  return (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={9}>
        <TextField
          label="Search"
          variant="outlined"
          fullWidth
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyDown={handleKeyDown}
        />
      </Grid>
      <Grid item xs={3}>
        <Button variant="contained" onClick={handleSearch} disabled={loading}>
          Search
        </Button>
      </Grid>
      {similarMaterials && similarMaterials?.length > 0 ? (
        <Grid item xs={12}>
          <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
            {similarMaterials?.map((result) => (
              <Box key={result.partNumber} display="flex" justifyContent="space-between" alignItems="center" m={1}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body1" noWrap>
                      {result.partNumber}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={6}>
                    <Typography variant="body1" noWrap>
                      {result.name}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={12} md={3} container justifyContent={{ xs: 'flex-start', md: 'flex-end' }}>
                    <Button variant="contained" onClick={() => useMaterial(result)}>
                      Use
                    </Button>
                  </Grid>
                </Grid>
              </Box>
            ))}
          </div>
        </Grid>
      ) : (
        isSearched &&
        !loading && (
          <Grid item xs={12}>
            <p style={{ textAlign: 'center' }}>No Material Found</p>
          </Grid>
        )
      )}
    </Grid>
  );
};

export default SearchComponent;
