import React from 'react';
import { Tooltip, IconButton, Button } from '@mui/material';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import Checkbox from '@mui/material/Checkbox';
import { REGISTEREDQUOTE, NOPRICEAVAILABLEQUOTE, RECIEVEDQUOTE, REQUESTEDQUOTE } from './constant';
import DescriptionIcon from '@mui/icons-material/Description';
import BadgeInputComponent from 'pages/component/table/badgeInput';

const label = { inputProps: { 'aria-label': 'Checkbox demo' } };

const OfferMaterialActionButton = ({ onClick, showDeleteButton, rowData, type, disabled, value, disabledBtn }) => {
  const { status, notes, unitPrice } = rowData || {};
  const isConfirmedQuote = status === REGISTEREDQUOTE;
  const isQuoteRecieved = status === RECIEVEDQUOTE;
  const isRequested = status === REQUESTEDQUOTE && !unitPrice;
  const isNoPriceAvailable = status === NOPRICEAVAILABLEQUOTE;
  const isRequestedWithPrice = status === REQUESTEDQUOTE && unitPrice;

  const updateButton = (isReceived) => {
    return (
      <>
        {!isRequested && (
          <Tooltip title="Edit" aria-label="edit">
            <Button variant="contained" size="small" onClick={() => onClick(isReceived ? 'use' : 'edit')} disabled={disabledBtn}>
              {isReceived ? 'Use' : 'Edit'}
            </Button>
          </Tooltip>
        )}
        {(showDeleteButton || isRequested) && (
          <Tooltip title="" aria-label="">
            <Button
              variant="outlined"
              color="error"
              size="small"
              onClick={() => onClick(isReceived || isRequested ? 'deleteUse' : 'delete')}
              sx={{ marginLeft: '10px' }}
              disabled={disabledBtn}
            >
              Delete
            </Button>
          </Tooltip>
        )}
      </>
    );
  };

  const renderButton = () => {
    switch (type) {
      case 'update':
        return (
          <>
            {(isConfirmedQuote || isQuoteRecieved) && updateButton(isQuoteRecieved)}
            {isRequestedWithPrice && <div style={{ display: 'flex' }}>{updateButton(true)}</div>}
            {isRequested && <div style={{ display: 'flex' }}>{updateButton(false)}</div>}
          </>
        );
      case 'checkbox':
        return (
          <>
            {isConfirmedQuote && (
              <Tooltip title="" aria-label="">
                <IconButton aria-label="" onClick={() => onClick('checkbox')} disabled={disabled || disabledBtn}>
                  <Checkbox {...label} disabled={disabled || disabledBtn} checked={value} value={value} />
                </IconButton>
              </Tooltip>
            )}
          </>
        );
      case 'pdf':
        return (
          <>
            {notes && (
              <Tooltip title="" aria-label="">
                <IconButton aria-label="" onClick={() => onClick('pdf')}>
                  <DescriptionIcon />
                </IconButton>
              </Tooltip>
            )}
          </>
        );
      default:
        return null;
    }
  };

  return <>{renderButton()}</>;
};

export default OfferMaterialActionButton;
