// src/routes/index.js
import { createBrowserRouter, Navigate } from 'react-router-dom';

// project import
import MainRoutes from './MainRoutes';
import LoginRoutes from './LoginRoutes';
import SupplierPortalLayout from './supplierPortalLayout';
const router = createBrowserRouter([MainRoutes, LoginRoutes, SupplierPortalLayout], { basename: import.meta.env.VITE_APP_BASE_NAME });

export default router;
