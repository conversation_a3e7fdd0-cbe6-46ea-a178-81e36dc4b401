import { Box, Typography } from "@mui/material";
import { get } from "lodash";
import RadioButtonsGroup from "pages/component/radioBtn";
import PageTitle from "pages/pageTitle";

const UpdatePrice = ({ setSelectedValue ,selectedValue }) => {
  const options = [{ label: 'Update', value: 'update' }, { label: 'Replace', value: 'replace' }]

  const handleChange = (value) => {
    setSelectedValue({ ...selectedValue, action: value })
  }

  return (
    <>
      <Box className="main-container">
      <PageTitle title='Price Maintenance' />
      <Box className="inner-container">
        <Typography
          variant="h5"
          color="secondary"
          className="header"
        >
          Update Supplier Price
        </Typography>
        <div  className="update-btn">
        <RadioButtonsGroup
          type="radio"
          name="radioSelect"
          row={true}          
          onChange={(e) => {
            handleChange(get(e, 'target.value'));
          }}
          value={get(selectedValue, 'action', 'update')}
          labels={options}
        />
        </div>
      </Box>
      </Box>
    </>
  )
}
export default UpdatePrice