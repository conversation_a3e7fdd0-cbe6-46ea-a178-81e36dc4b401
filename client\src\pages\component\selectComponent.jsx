import React, { useState, useEffect } from 'react';
import { Select, MenuItem, FormControl, InputLabel, Chip, OutlinedInput, Box } from '@mui/material';
import PropTypes from 'prop-types';

const SelectComponent = ({ label, error, onSelectChange, items, name, value, onBlur, style, placeholder, multiple, ...rest }) => {
  const inputLabel = React.useRef(null);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    setOpen(false);
  }, []);

  const handleChange = (event) => {
    onSelectChange(event);
    if (multiple) {
      setOpen(false);
    }
  };

  return (
    <FormControl variant="outlined" fullWidth>
      <InputLabel ref={inputLabel} id={`select-${label}`}>
        {label || ''}
      </InputLabel>
      <Select
        multiple={multiple}
        label={label}
        labelId={`select-${label}`}
        style={{ ...style, borderRadius: '10px' }}
        name={name}
        error={error}
        onBlur={onBlur}
        onChange={handleChange}
        value={value}
        input={<OutlinedInput label={label} />}
        open={open}
        onOpen={() => setOpen(true)}
        onClose={() => setOpen(false)}
        renderValue={(selected) =>
          multiple ? (
            <Box className="multiselect-chip-wrapper">
              {selected?.map((val) => {
                const selectedItem = items?.find((item) => item.value === val);
                return (
                  <Chip
                    key={val}
                    label={selectedItem?.label}
                    className="multiselect-chip"
                    onDelete={(event) => {
                      event.stopPropagation();
                      onSelectChange({
                        target: { name, value: selected?.filter((v) => v !== val) }
                      });
                    }}
                    onMouseDown={(event) => event.stopPropagation()}
                  />
                );
              })}
            </Box>
          ) : (
            items.find((item) => item?.value === selected)?.label || ''
          )
        }
        MenuProps={{
          PaperProps: {
            style: {
              maxHeight: 200,
              overflowY: 'auto'
            }
          }
        }}
        {...rest}
      >
        {placeholder && (
          <MenuItem value="" disabled>
            {placeholder}
          </MenuItem>
        )}
        {items?.map((item, i) => (
          <MenuItem key={i} value={item.value}>
            {item.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

SelectComponent.propTypes = {
  label: PropTypes.string.isRequired,
  onSelectChange: PropTypes.func.isRequired,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.any.isRequired
    })
  ).isRequired,
  name: PropTypes.string.isRequired,
  value: PropTypes.any.isRequired,
  error: PropTypes.bool,
  onBlur: PropTypes.func,
  style: PropTypes.object,
  placeholder: PropTypes.string,
  multiple: PropTypes.bool
};

SelectComponent.defaultProps = {
  placeholder: 'Select an option',
  multiple: false
};

export default SelectComponent;
