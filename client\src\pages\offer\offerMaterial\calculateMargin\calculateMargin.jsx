import { Box, Button, Tooltip } from '@mui/material';
import Loader from 'components/Loader';
import AlertDialog from 'pages/component/dialogbox';
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { calculateMarginAction, setCalculatedMargin } from 'redux/reducers/offerReducer';
import { notification } from 'utils/helper';
import FinalPriceBox from './finalPrice';
import { validate } from 'pages/component/validation';
import { isEmpty } from 'lodash';

const CalculateMargin = ({ rowData, offer }) => {
  const dispatch = useDispatch();
  const offerData = useSelector((state) => state.offer);
  const [quoteInfo, setQuoteInfo] = useState({});
  const [open, setOpen] = useState(false);

  const handleClose = () => {
    setQuoteInfo({});
    dispatch(setCalculatedMargin(''));
    setOpen(false);
  };

  const handleConfirm = async () => {
    if (quoteInfo?.unitOfferPrice) {
      let res = await dispatch(
        calculateMarginAction({
          offerCurrency: offer?.offerCurrency,
          desiredUnitPrice: quoteInfo?.unitOfferPrice,
          quoteCurrency: rowData?.currency,
          unitPrice: rowData?.unitPrice,
          supplierId: rowData?.supplierId,
          weight: rowData?.weight,
          isTax: rowData?.isTax,
          ClientID: offer?.ClientID || ''
        })
      );
    } else {
      dispatch(notification(false, 'Please enter unit offer price', true));
    }
  };

  const buttons = [
    { label: 'Cancel', onClick: () => handleClose(), variant: 'outlined', color: 'error', disabled: offerData?.loading },
    { label: 'Calculate Margin', onClick: () => handleConfirm(), variant: 'contained', color: 'primary', disabled: offerData?.loading }
  ];

  const handleInputChange = (name, value) => {
    setQuoteInfo((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const handleOpen = () => {
    const calculateMarginFields = {
      offerCurrency: { required: true, label: 'Offer Currency' },
      ClientID: { required: true, label: 'Client' }
    };
    const validation = validate(offer || {}, calculateMarginFields);

    if (isEmpty(validation)) {
      setOpen(true);
    } else {
      const firstErrorMessage = Object.values(validation)?.[0];
      dispatch(notification(false, firstErrorMessage, true));
    }
  };

  return (
    <>
      {offerData?.loading && <Loader />}
      <AlertDialog
        cancel={handleClose}
        showCard={true}
        buttons={buttons}
        borderRadius="20px"
        Component={<FinalPriceBox handleInputChange={handleInputChange} rowData={rowData} quoteInfo={quoteInfo} />}
        open={open}
      />
      <Box display="flex" alignItems="center" gap={1} mb={2}>
        <Tooltip title="Calculate Margin">
          <Button variant="contained" size="small" onClick={() => handleOpen()}>
            Get Margin
          </Button>
        </Tooltip>
      </Box>
    </>
  );
};
export default CalculateMargin;
