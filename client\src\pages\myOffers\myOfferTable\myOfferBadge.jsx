import BadgeInputComponent from 'pages/component/table/badgeInput';
import React from 'react';
import { Typography } from '@mui/material';

const MyOfferBadge = ({ onClick, data, id, disableBtn }) => {
  const isArray = Array.isArray(data);

  return (
    <div>
      {isArray ? (
        <div className="myoffer-badge-wrapper">
          {data?.map((badge, index) =>
            badge?.type === 'text' ? (
              <Typography>{badge?.title}</Typography>
            ) : (
              <BadgeInputComponent
                key={index}
                badgeContent={badge.badgeContent}
                color={badge.color}
                onClick={(id) => onClick(badge, id)}
                id={id}
                btnBadge={true}
                disableBtn={disableBtn}
              />
            )
          )}
        </div>
      ) : (
        <Typography
          sx={
            data === 'VOID' && {
              color: 'error.main'
            }
          }
        >
          {data}
        </Typography>
      )}
    </div>
  );
};

export default MyOfferBadge;
