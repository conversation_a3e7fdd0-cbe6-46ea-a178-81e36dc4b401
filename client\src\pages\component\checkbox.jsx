import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { FormControlLabel, Checkbox, FormLabel, FormControl } from '@mui/material';
import { get } from 'lodash';

const CheckboxComponent = ({ options, value, name, onChange, className,label}) => {
  const [selectedOption, setSelectedOption] = useState('');
  const handleOptionChange = (e) => {
    if (name === 'paymentMethod') {
      const selectedValue = e.target.value;
      setSelectedOption(selectedValue);
    }
    onChange(e);
  };
  return (
    <>
      <FormLabel component="legend">{label}</FormLabel>
      <FormControl>
        {options?.map((option, index) => (
          <div key={`inline-${option.value}-${index}`} className="mb-2">
            <FormControlLabel
              key={value?.id}
              control={
                <Checkbox
                  name={name}
                  checked={option?.select}
                  onChange={(e) => handleOptionChange(e)}
                  value={option?.value}
                  color="primary"
                />
              }
              label={option?.label}
              className={className}
            />
            {selectedOption === get(option, 'value') && (
              <>
                {get(option, 'description') && ( // Check for "description" property
                  <p className="payment_method_description">Description: {get(option, 'description')}</p>
                )}
                {get(option, 'detail', [])?.map(
                  (item, index) =>
                    item?.value && (
                      <p key={index} className="payment_method_description">
                        {item?.key}: {item?.value}
                      </p>
                    )
                )}
              </>
            )}
          </div>
        ))}
      </FormControl>
    </>
  );
};

CheckboxComponent.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired
    })
  ).isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  className: PropTypes.string,
  type: PropTypes.string,
  name: PropTypes.string.isRequired
};

export default CheckboxComponent;
