import { Box, Typography, Divider, Stack, Grid } from '@mui/material';
import { useSelector } from 'react-redux';
import { getUserDetail } from 'utils/auth';
import TableComponent from 'pages/component/table/table';
import MailOutlineIcon from '@mui/icons-material/MailOutline';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';
import SubjectIcon from '@mui/icons-material/Subject';
import clsx from 'clsx';
import { isEmpty } from 'lodash';

const Step2 = ({ values, isSentEmail }) => {
  const supplierData = useSelector((state) => state.offer);
  const userDetail = getUserDetail();

  const formattedRFQs = (values?.rfq || []).filter((rfq) => !isEmpty(values?.selectedMaterials[rfq])).join(', ') || '';

  const materialColumns = [
    { type: 'text', name: 'Material_ID', title: 'Material ID', width: '200px' },
    { type: 'text', name: 'Material_Description', title: 'Name' },
    { type: 'text', name: 'Part_Number', title: 'Part Number' },
    { type: 'text', name: 'brand', title: 'Brand' },
    { type: 'text', name: 'Quantity_Required', title: 'Quantity Required' }
  ];

  const EmailBody = () =>
    Object.keys(values?.selectedMaterials || {})?.map(
      (rfqID) =>
        !isEmpty(values?.selectedMaterials?.[rfqID]) && (
          <Box key={rfqID} className="email-request-section">
            <Typography variant="h6" color="primary" fontWeight={700} className="email-request-rfq-title" m={1}>
              RFQ ID: {rfqID}
            </Typography>
            <Divider className="email-request-divider" />
            <TableComponent enablePagination columns={materialColumns} rows={values?.selectedMaterials?.[rfqID] || []} title="Materials" />
          </Box>
        )
    );

  const data = [
    { label: 'From', value: '<EMAIL>', icon: <MailOutlineIcon color="primary" /> },
    {
      label: 'To',
      value: isSentEmail
        ? values?.supplierEmail
        : supplierData?.supplierList
            ?.filter((item) => values?.suppliers?.includes(item?.SupplierID))
            ?.map((item) => item.Email)
            ?.join(' , ') || 'N/A',
      icon: <PersonOutlineIcon color="primary" />
    },
    { label: 'CC', value:values?.ccEmail, icon: <MailOutlineIcon color="primary" /> },
    { label: 'Subject', value: `[RFQ- ${formattedRFQs}] REMIEX Price Request`, icon: <SubjectIcon color="primary" /> },
    { type: 'component', value: <EmailBody />, md: 12, xl: 12 }
  ];

  return (
    <Box className="email-request-container">
      <Stack spacing={2}>
        <Grid container>
          {data.map((item, index) => (
            <Grid item xs={12} md={item?.md || 6} xl={item?.xl || 6} key={index}>
              <Box
                key={index}
                className={clsx('email-request-info-box', { 'email-request-component-box': item.type === 'component' })}
                ml={1}
                mt={1}
              >
                {item.icon && <Box className="email-request-icon">{item.icon}</Box>}
                {item.label && (
                  <Typography variant="subtitle1" color="textSecondary" fontWeight={600} className="email-request-label">
                    {item.label}:
                  </Typography>
                )}
                {item.type === 'component' ? (
                  <Box className="email-request-component">{item.value}</Box>
                ) : (
                  <Typography className="email-request-text">{item.value || 'N/A'}</Typography>
                )}
              </Box>
            </Grid>
          ))}
        </Grid>
      </Stack>
    </Box>
  );
};

export default Step2;
