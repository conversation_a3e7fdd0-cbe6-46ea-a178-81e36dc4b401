import { Typography } from '@mui/material';
import React, { useState } from 'react';

const CustomSwitch = ({ onSwitch }) => {
  const [isChecked, setIsChecked] = useState(false);

  const handleToggle = () => {
    setIsChecked(!isChecked);
    onSwitch(!isChecked);
  };

  return (
    <>
      <button className={`custom-switch ${isChecked ? 'checked' : ''}`} onClick={handleToggle} style={{ padding: '10px' }}>
        <Typography variant="body1" className={`switch-text ${isChecked ? 'active right-align' : 'left-align'}`}>
          {isChecked ? 'All Offers' : 'Open'}
        </Typography>

        <div className="switch-slider">
          <Typography variant="body1" className={`switch-text center-align ${isChecked ? 'active ' : ''}`}>
            {!isChecked ? 'All Offers' : 'Open'}
          </Typography>
        </div>
      </button>
    </>
  );
};

export default CustomSwitch;
