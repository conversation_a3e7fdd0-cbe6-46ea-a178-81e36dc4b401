// DynamicTabs.js
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Tabs, Tab, Box, Typography } from '@mui/material';

const TabPanel = ({ children, value, index, ...other }) => (
  <div role="tabpanel" hidden={value !== index} id={`tabpanel-${index}`} aria-labelledby={`tab-${index}`} {...other}>
    {value === index && (
      <Box>
        <Typography>{children}</Typography>
      </Box>
    )}
  </div>
);

TabPanel.propTypes = {
  children: PropTypes.node,
  value: PropTypes.number.isRequired,
  index: PropTypes.number.isRequired
};

const DynamicTabs = ({ tabs, name, value, handleChange, defaultValue }) => {
  return (
    <Box sx={{ width: '100%' }}>
      <Tabs
        value={value || defaultValue || 'ALL'}
        onChange={(event, newValue) => handleChange(name, newValue)}
        aria-label="dynamic tabs"
        name={name}
      >
        {tabs?.map((tab, index) => (
          <Tab label={tab.label} key={index} id={`tab-${index}`} aria-controls={`tabpanel-${index}`} value={tab?.value} />
        ))}
      </Tabs>
      {tabs?.map((tab, index) => (
        <TabPanel value={tab?.value} index={index} key={index}>
          {tab.content}
        </TabPanel>
      ))}
    </Box>
  );
};

DynamicTabs.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      content: PropTypes.node.isRequired
    })
  ).isRequired
};

export default DynamicTabs;
