import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import InputField from 'pages/component/inputField';
import AddIcon from '@mui/icons-material/Add';
import { Checkbox, Typography } from '@mui/material';
import CheckBoxInput from 'pages/component/table/checkboxInput';
import EditIcon from '@mui/icons-material/Edit';
import { MenuItem, Select } from '@mui/material';
import { get } from 'lodash';
import ErrorMessage from 'pages/component/errorMessage';
import { getApprovedSupplier, isCountryManager, showAlert } from 'utils/helper';
import { useDispatch } from 'react-redux';
import SelectComponent from 'pages/component/selectComponent';
import TextAreaComponent from 'pages/component/textArea';
import DynamicAutocomplete from 'pages/component/autoComplete';

const validationSchema = Yup.object().shape({
  supplier: isCountryManager() ? Yup.string() : Yup.string().required('Supplier is required'),
  unitPrice: Yup.number().min(0.1, 'Unit Cost must be greater than 0').required('Unit Cost is required'),
  quantity: Yup.number().min(1, 'Qty must be greater than 0').required('Qty is required'),
  weight: Yup.number().min(0.1, 'Weight must be greater than or equal to 0').required('Weight is required'),
  currency: Yup.string().required('Currency is required'),
  leadTime: Yup.number().min(1, 'Lead Time must be greater than 0').required('Lead Time is required')
});

const inputFields = [
  {
    label: 'Supplier',
    name: 'supplier',
    type: 'select',
    options: ['Supplier A', 'Supplier B', 'Supplier C'],
    hideField: isCountryManager()
  },
  { label: 'EXW Unit Cost', name: 'unitPrice', type: 'number' },
  {
    label: 'Qty',
    name: 'quantity',
    type: 'number',
    width: '100px',
    onBlur: (e, quantity, dispatch) => {
      if (e?.target?.value > quantity) {
        showAlert(dispatch, false, 'Warning: The quantity entered differs from the requested RFQ quantity', false, true);
      }
    }
  },
  { label: 'Unit Weight (kg)', name: 'weight', type: 'number' },
  {
    label: 'Currency',
    name: 'currency',
    type: 'select',
    values: [
      { label: 'USD', value: 'USD' },
      { label: 'EUR', value: 'EUR' },
      { label: 'GBP', value: 'GBP' },
      { label: 'CLP', value: 'CLP' },
      { label: 'AUD', value: 'AUD' }
    ]
  },

  { label: 'Lead Time(days)', name: 'leadTime', type: 'number' },
  { label: 'Notes', name: 'notes', type: 'textarea' },
  { label: 'AV', name: 'isTax', type: 'checkbox' }
];

const OfferMaterialForm = forwardRef((props, ref) => {
  const { addSupplier, quantity, editSupplier, suppliers, initialValues, editMode, supplierList, loading } = props;
  const sortedSuppliers = getApprovedSupplier(supplierList).sort((a, b) => a.label.localeCompare(b.label));
  const dispatch = useDispatch();
  const isDisabled = (supplier) => {
    return suppliers?.some((selectedSupplier) => Number(selectedSupplier?.id) === supplier?.SupplierID);
  };

  return (
    <div>
      <Formik
        enableReinitialize
        initialValues={initialValues}
        validationSchema={validationSchema}
        validateOnBlur={false}
        validateOnChange={false}
        onSubmit={(values, { setSubmitting, resetForm }) => {
          if (editMode) {
            editSupplier(values, resetForm);
          } else {
            addSupplier(values, resetForm);
          }
          setSubmitting(false);
        }}
        innerRef={ref}
      >
        {({ isSubmitting, errors, touched, handleBlur, setFieldValue, handleChange, values }) => {
          useImperativeHandle(ref, () => ({
            setFieldValue,
            quantity
          }));
          return (
            <Form style={{ display: 'flex', flexDirection: 'column', overflowX: 'auto' }}>
              <div style={{ display: 'flex', minWidth: '100px' }}>
                {inputFields.map((field, index) => (
                  <div className="generateOffer-input" key={index} style={{ marginRight: '10px' }}>
                    {!field.hideField && <Typography variant="h6">{field.label}</Typography>}
                    {field.type === 'select' ? (
                      !field.hideField && (
                        <>
                          <DynamicAutocomplete
                            value={
                              (field.name === 'currency' ? field.values : getApprovedSupplier(supplierList)).find(
                                (option) => option.value === values[field.name]
                              ) || null
                            }
                            onChange={(e, newValue) => {
                              setFieldValue(field.name, newValue?.value || '');
                            }}
                            getOptionLabel={(option) => option.label || ''}
                            options={field.name === 'currency' ? field.values : getApprovedSupplier(supplierList)}
                            isNotRemoveButton={false}
                            isLoading={false}
                            // label={field.label || field.name}
                            placeholder={'Select an option'}
                            padding="8px"
                            error={errors[field.name]}
                            fullWidth={false}
                          />

                          <ErrorMessage message={get(errors, `${field.name}`, '')} />
                        </>
                      )
                    ) : field.type === 'checkbox' ? (
                      <>
                        <Checkbox
                          value={values[field?.name] || ''}
                          checked={values[field?.name] || false}
                          name={field?.name}
                          onChange={(e) => {
                            setFieldValue(field.name, e.target.value);
                            handleChange(e);
                          }}
                        />
                      </>
                    ) : field.type === 'textarea' ? (
                      <TextAreaComponent
                        name={field?.name}
                        value={values[field?.name] !== undefined ? values[field?.name] : ''}
                        className="name-text-field textarea-field"
                        onChange={(name, e) => {
                          setFieldValue(field.name, e.target.value);
                          handleChange(e);
                        }}
                        errors={errors}
                        placeholder={field.placeholder}
                        minRows={2}
                      />
                    ) : field.type === 'autocomplete' ? (
                      <DynamicAutocomplete
                        name={field?.name}
                        value={values[field?.name] !== undefined ? values[field?.name] : ''}
                        className="name-text-field textarea-field"
                        onChange={(e, newValue) => {
                          setFieldValue(field.name, newValue);
                          handleChange(e);
                        }}
                        getOptionLabel={(option) => option.label}
                        errors={errors}
                        options={sortedSuppliers}
                      />
                    ) : (
                      <InputField
                        type={field?.type || 'text'}
                        style={{ width: field?.width, height: '40px' }}
                        name={field?.name}
                        value={values[field?.name] !== undefined ? values[field?.name] : ''}
                        handleBlur={(e) => {
                          handleBlur(e);
                          if (field?.onBlur) {
                            field?.onBlur(e, quantity, dispatch);
                          }
                        }}
                        onChange={(e) => {
                          setFieldValue(field.name, e.target.value);
                          handleChange(e);
                        }}
                        errors={errors}
                        placeholder={field.placeholder}
                      />
                    )}
                  </div>
                ))}

                <div className="generateOffer-input">
                  <button type="submit" className={`add-material-offer ${!loading && 'disable-add-material'}`} disabled={loading}>
                    {!editMode ? <AddIcon className="add-icon" /> : <EditIcon className="add-icon" />}
                  </button>
                </div>
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
});

export default OfferMaterialForm;
