const Joi = require("joi");
const {
  stringValidation,
  numberValidation,
  currencyValidation,
  booleanValidation,
} = require("../utils/validator.util");

const getAllOfferSchema = Joi.object({
  limit: numberValidation.optional(),
  page: numberValidation.optional(),
}).unknown();

const getOfferSchema = Joi.object({
  id: stringValidation,
});

const generateOfferSchema = Joi.object({
  clientId: stringValidation,
  rfqId: stringValidation,
  offerCurrency: currencyValidation,
  validFor: numberValidation,
  paymentTerms: stringValidation,
  notes: stringValidation,
  language:stringValidation,
  materials: Joi.array()
    .required()
    .items(
      Joi.object({
        materialId: stringValidation,
        partNumber: stringValidation,
        brand: stringValidation,
        materialDescription: stringValidation,
        Quantity_Required: numberValidation,
        supplier: Joi.object({
          quoteId: stringValidation,
          supplierId: stringValidation,
          unitPrice: numberValidation,
          unitOfferPrice: numberValidation,
          shippingCost: numberValidation,
          tax: numberValidation,
          isTax: booleanValidation,
          quantity: numberValidation,
          totalCost: numberValidation,
          unitCurrency: currencyValidation,
          margin: numberValidation,
          weight: numberValidation,
          offeredPrice: numberValidation,
          notes: stringValidation.optional().allow(""),
          leadTime: numberValidation,
          totalOfferPrice: numberValidation,
          exchangedOfferCostWithHandlingCost: numberValidation,
          exchangedOfferCost: numberValidation,
          exchangeOfferHandlingCost: numberValidation,
          unitOfferPrice: numberValidation,
          unitOfferPriceHandlingCost: numberValidation,
          unitOfferPriceWithHandlingCost: numberValidation,
        }),
      })
    ),
});

const updateStatusSchema = Joi.object({
  rfqId: stringValidation,
  offerId: stringValidation,
  status: stringValidation,
});

// const calculateOfferSchema = Joi.object({
//   offerCurrency: currencyValidation,
//   quotes: Joi.array()
//     .required()
//     .items(
//       Joi.object({
//         quoteId: Joi.when("isEdit", { is: true, then: stringValidation }),
//         rfqId: stringValidation,
//         materialId: stringValidation,
//         supplierId: stringValidation,
//         unitPrice: numberValidation,
//         quantity: numberValidation,
//         weight: numberValidation,
//         currency: currencyValidation,
//         isOffered: booleanValidation,
//         isTax: booleanValidation,
//         leadTime: numberValidation,
//         isEdit: booleanValidation,
//         margin: numberValidation,
//       }).required()
//     ),
// });

const calculateOfferSchema = Joi.object({
  offerCurrency: currencyValidation,
  clientId: stringValidation,
  quotes: Joi.array()
    .required()
    .items(
      Joi.object({
        currency: currencyValidation,
        margin: numberValidation.optional(),
        quantity: numberValidation,
        quoteId: stringValidation,
        supplierId: stringValidation,
        unitPrice: numberValidation,
        weight: numberValidation,
        isTax: booleanValidation,
        totalCost: numberValidation,
      }).required()
    ),
});

const specsSheetSchema = Joi.object({
  offerId: stringValidation,
});

const winOfferSchema = Joi.object({
  OC_Number: stringValidation,
  Amount_CLP: numberValidation,
  Amount_USD: numberValidation,
  Client: stringValidation.optional().allow(""),
  rfqId: numberValidation,
  offerId: numberValidation,
  status: stringValidation,
});

const copyRFQSchema = Joi.object({
  rfqId: stringValidation,
  offerId: stringValidation,
  expirationDate: stringValidation
});

module.exports = {
  getAllOfferSchema,
  getOfferSchema,
  generateOfferSchema,
  calculateOfferSchema,
  updateStatusSchema,
  specsSheetSchema,
  winOfferSchema,
  copyRFQSchema,
};
