import FilterListIcon from '@mui/icons-material/FilterList';
import SelectComponent from 'pages/component/selectComponent';
import { defaultSorting, sortingData } from './constant';
import { useState } from 'react';
const MaterialSorting = ({ handleSorting }) => {
	const [selectedOption, setSelectedOption] = useState(defaultSorting?.value)
	const handleChange = (e) => {
		const { value } = e?.target || {}
		setSelectedOption(value)
		if (value === 'none') {
			handleSorting({})
		} else {
			const seletedSorting = sortingData?.find((sort) => sort.value === value)
			let payload = {
				heading: seletedSorting?.heading,
				field: seletedSorting?.field,
				orderBy: [seletedSorting?.type]
			}
			handleSorting(payload)
		}
	}
	return (<>
		<SelectComponent value={selectedOption || ''} style={{ minWidth: '200px' }} onChange={(e) => handleChange(e)} items={sortingData} label='Sort By' />
	</>)
}
export default MaterialSorting