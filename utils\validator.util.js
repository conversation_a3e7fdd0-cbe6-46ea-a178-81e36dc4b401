const Joi = require("joi");
const { RFQStatus } = require("../rfq/rfq.controller");

const emailValidation = Joi.string().trim().required().email();
const stringValidation = Joi.string().trim().required();
const currencyValidation = Joi.string()
  .trim()
  .required()
  .valid("USD", "EUR", "GBP", "CLP", "AUD");
const conditionValidation = Joi.string()
  .trim()
  .required()
  .valid("NEW", "NEW OPEN BOX", "NEW FACTORY SEALED", "NEW SURPLUS", "");
const numberValidation = Joi.number().required();
const booleanValidation = Joi.boolean().required();
const dateValidation = Joi.date().iso().required();
const statusValidation = Joi.string()
  .trim()
  .required()
  .valid(...Object.values(RFQStatus));
const arrayObjectValidation = Joi.array()
  .required()
  .items(Joi.object({}).unknown());
const urlValidation = Joi.string().trim().required().uri();
const roleValidation = Joi.string()
  .trim()
  .required()
  .valid("KAM", "SUPERVISOR", "COUNTRY_MANAGER");

const messages = {
  "date.base": ":key must be a valid date",
  "date.empty": ":key is required",
  "date.format": ":key must be a valid date",
  "string.base": ":key must be a string",
  "string.email": "Please enter a valid email",
  "string.uri": "Please enter a valid URL",
  "string.empty": ":key is required",
  "number.base": ":key must be a number",
  "number.integer": ":key must be a valid number",
  "number.empty": ":key is required",
  "number.unsafe": ":key must be valid",
  "boolean.base": ":key must be true/false",
  "any.required": ":key is required",
  "any.invalid": ":key is invalid",
  "any.only": ":value is not a valid :key",
  "object.unknown": ":key is not allowed",
  "array.base": ":key must be an array",
  "array.includesRequiredUnknowns": ":key connot be empty",
};

const populateMessage = (error) => {
  const errorDetails = error.details[0];
  let message = messages[errorDetails.type];
  message = message.replace(
    new RegExp(":key", "g"),
    errorDetails.context.key === 0
      ? errorDetails.context.label
      : errorDetails.context.key
  );
  message = message.replace(
    new RegExp(":value", "g"),
    errorDetails.context.value
  );
  return message;
};

module.exports = {
  messages,
  populateMessage,
  conditionValidation,
  emailValidation,
  stringValidation,
  currencyValidation,
  numberValidation,
  booleanValidation,
  dateValidation,
  statusValidation,
  arrayObjectValidation,
  urlValidation,
  roleValidation,
};
