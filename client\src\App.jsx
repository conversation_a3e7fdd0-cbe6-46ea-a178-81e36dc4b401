import { BrowserRouter, Navigate, RouterProvider, createBrowserRouter } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import './App.css';
import { useEffect, useMemo, useState } from 'react';
import { getUserDetail, insertToken } from 'utils/auth';
import LoginRoutes from 'routes/LoginRoutes';
import MainRoutes, { supervisorRoutes } from 'routes/MainRoutes';
import { getItemFromLocalStorage } from 'utils/helper';
import SupplierPortalLayout from 'routes/supplierPortalLayout';
import { SUPERVISER } from 'utils/constant';

export default function App() {
  const dispatch = useDispatch();
  const login = useSelector((state) => state.login);

  // Precompute the routes based on the user role
  const routes = useMemo(() => {
    const { role } = getUserDetail() || {};

    if (role === SUPERVISER) {
      return { ...MainRoutes, children: [...MainRoutes?.children, ...supervisorRoutes] };
    } else {
      return MainRoutes;
    }
  }, [login?.token]);

  useEffect(() => {
    dispatch(insertToken());
  }, [dispatch]);

  const authRouter = useMemo(() => 
    createBrowserRouter(
      [
        LoginRoutes,
        SupplierPortalLayout,
        {
          path: '*',
          element: <Navigate to="/login" />
        }
      ],
      { basename: import.meta.env.VITE_APP_BASE_NAME }
    ), []
  );

  const authenticatedRouter = useMemo(() => 
    createBrowserRouter([routes, SupplierPortalLayout], { basename: import.meta.env.VITE_APP_BASE_NAME }), 
    [routes]
  );

  return (
    <>
      {login?.token || getItemFromLocalStorage('token', '') ? (
        <RouterProvider router={authenticatedRouter} />
      ) : (
        <RouterProvider router={authRouter} />
      )}
    </>
  );
}
