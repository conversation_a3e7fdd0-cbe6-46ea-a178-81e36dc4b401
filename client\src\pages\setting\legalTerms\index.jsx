import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { Box, Button, Typography, CircularProgress, Grid } from '@mui/material';
import MainCard from 'components/MainCard';
import { getLegalTerms, updateLegalTerms } from 'redux/reducers/settingReducer';
import { Editor } from '@tinymce/tinymce-react';

const LegalTerms = () => {
  const dispatch = useDispatch();
  const [englishContent, setEnglishContent] = useState('');
  const [spanishContent, setSpanishContent] = useState('');
  const [savingEN, setSavingEN] = useState(false);
  const [savingES, setSavingES] = useState(false);
  const editorRef = useRef(null);

  const { loading, legalTerms } = useSelector((state) => state.setting);

  useEffect(() => {
    dispatch(getLegalTerms('EN'));
    dispatch(getLegalTerms('ES'));
  }, [dispatch]);

  useEffect(() => {
    setEnglishContent(legalTerms.EN || '');
    setSpanishContent(legalTerms.ES || '');
  }, [legalTerms]);

  const handleSaveEN = async () => {
    setSavingEN(true);
    try {
      await dispatch(updateLegalTerms({ language: 'EN', content: englishContent }));
    } finally {
      setSavingEN(false);
      dispatch(getLegalTerms('EN'));
    }
  };

  const handleSaveES = async () => {
    setSavingES(true);
    try {
      await dispatch(updateLegalTerms({ language: 'ES', content: spanishContent }));
    } finally {
      setSavingES(false);
      dispatch(getLegalTerms('ES'));
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography variant="h4" sx={{ mb: 2 }}>
              English Terms & Conditions
            </Typography>

            <Editor
              apiKey={APIKEY}
              onInit={(_evt, editor) => (editorRef.current = editor)}
              value={englishContent}
              onEditorChange={(content) => setEnglishContent(content)}
              init={{
                height: 600,
                menubar: false,
                plugins: [
                  'advlist',
                  'autolink',
                  'lists',
                  'link',
                  'image',
                  'charmap',
                  'preview',
                  'anchor',
                  'searchreplace',
                  'visualblocks',
                  'code',
                  'fullscreen',
                  'insertdatetime',
                  'media',
                  'table',
                  'help',
                  'wordcount'
                ],
                toolbar:
                  'undo redo | blocks | ' +
                  'bold italic forecolor | bullist numlist outdent indent | alignleft aligncenter ' +
                  'alignright alignjustify',
                content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:18px; color:#000000; }'
              }}
            />
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button variant="contained" onClick={handleSaveEN} disabled={savingEN || loading}>
                {savingEN ? <CircularProgress size={24} color="inherit" /> : 'Save English'}
              </Button>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="h4" sx={{ mb: 2 }}>
              Spanish Terms & Conditions
            </Typography>

            <Editor
              apiKey={APIKEY}
              onInit={(_evt, editor) => (editorRef.current = editor)}
              value={spanishContent}
              onEditorChange={(content) => setSpanishContent(content)}
              init={{
                height: 600,
                menubar: false,
                plugins: [
                  'advlist',
                  'autolink',
                  'lists',
                  'link',
                  'image',
                  'charmap',
                  'preview',
                  'anchor',
                  'searchreplace',
                  'visualblocks',
                  'code',
                  'fullscreen',
                  'insertdatetime',
                  'media',
                  'table',
                  'help',
                  'wordcount'
                ],
                toolbar:
                  'undo redo | blocks | ' +
                  'bold italic forecolor | bullist numlist outdent indent | alignleft aligncenter ' +
                  'alignright alignjustify',
                content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:18px; color:#000000; }'
              }}
            />
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button variant="contained" onClick={handleSaveES} disabled={savingES || loading}>
                {savingES ? <CircularProgress size={24} color="inherit" /> : 'Save Spanish'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default LegalTerms;

const quillModules = {
  toolbar: [
    [{ header: [1, 2, 3, 4, false] }],
    [{ size: ['small', false, 'large', 'huge'] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ list: 'ordered' }, { list: 'bullet' }],
    [{ color: [] }, { background: [] }],
    ['link'],
    ['clean']
  ]
};
const APIKEY = import.meta.env.VITE_TINYMCE_API_KEY;
console.log(APIKEY);
console.log(import.meta.env.VITE_TESTING);
const HtmlRenderer = ({ htmlString }) => {
  return <div dangerouslySetInnerHTML={{ __html: htmlString }} />;
};
