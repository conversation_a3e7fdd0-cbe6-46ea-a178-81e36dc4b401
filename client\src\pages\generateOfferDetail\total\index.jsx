import React from 'react';
import { Box, Button, Grid, Stack, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import { get } from 'lodash';
import Price from 'pages/component/price';

const OfferDetailTotal = ({ generateOffer, totalDetail, loading, showGenerateOfferBtn, calculateOffer, disableUpdateMaterial }) => {
  const labels = [
    { name: 'subTotal', label: 'SubTotal' },
    // { name: 'handlingCost', label: 'Handle' },
    { name: 'taxCost', label: totalDetail?.tax ? `Tax (${totalDetail?.tax}%)` : 'Tax', percentName: 'taxPercent' },
    { name: 'grandTotal', label: 'Total' }
  ];

  const renderKeyValuePairs = (labels, total) => (
    <Box className="over-view-box" pt={5} pr={5}>
      <div className="overview-container">
        {labels?.map(({ name, label, percentName }) => (
          <div key={name} className="over-view-inner-container">
            <Typography variant="body1" className="over-view-label">
              {label}
            </Typography>
            <Typography variant="body1" className="over-view-value">
              <span className="material-value">
                <Price price={get(total, name, 0)} />
              </span>
            </Typography>
          </div>
        ))}
      </div>
    </Box>
  );

  return (
    <>
      {totalDetail && Object.keys(totalDetail || {})?.length > 0 && (
        <MainCard sx={{ mt: 2 }} content={false}>
          <Typography variant="h5" color="secondary">
            Total
          </Typography>
          <Stack spacing={4}>
            <Grid container className="card-container" display="flex" justifyContent="flex-end">
              {renderKeyValuePairs(labels, totalDetail)}
              <Grid item xs={12} display="flex" justifyContent="flex-end" m={2} gap={1}>
                <Button
                  variant="outlined"
                  color="primary"
                  size="small"
                  onClick={calculateOffer}
                  disabled={disableUpdateMaterial || loading}
                >
                  Calculate Offer
                </Button>
                <Button variant="outlined" color="primary" size="small" onClick={generateOffer} disabled={showGenerateOfferBtn || loading}>
                  Generate
                </Button>
              </Grid>
            </Grid>
          </Stack>
        </MainCard>
      )}
    </>
  );
};

export default OfferDetailTotal;
