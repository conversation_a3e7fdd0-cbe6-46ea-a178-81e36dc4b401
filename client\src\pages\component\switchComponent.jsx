// DynamicSwitch.js
import React from 'react';
import { Switch, FormControlLabel } from '@mui/material';

const DynamicSwitch = ({ label, checked, onChange, color = 'primary' }) => {
  return (
    <FormControlLabel
      control={
        <Switch
          checked={checked}
          onChange={onChange}
          color={color}
        />
      }
      label={label}
    />
  );
};

export default DynamicSwitch;
