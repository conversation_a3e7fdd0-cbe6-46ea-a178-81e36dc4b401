/* eslint-disable no-unused-vars */
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Loader from 'components/Loader';
import MainCard from 'components/MainCard';
import { get } from 'lodash';
import Pagination from 'pages/component/pagination';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getAllKamAction, getAllRfq } from 'redux/reducers/RfqReducer';
import { getUserDetail } from 'utils/auth';
import {
  IRRELEVANT,
  KAM,
  QUOTING,
  RESERVED,
  RFQPERPAGELIMIT,
  ReservedSingleRfqPageUrl,
  SUPERVISER,
  addRfqPageUrl,
  reserveAddEditRfq
} from 'utils/constant';
import MaterialCard from './materialCard';
import MaterialFilter from './materialFilter.js';
import Assign from './materialFilter.js/component/assign';
import QuickView from './materialFilter.js/component/quickView';
import ReserveBtn from './materialFilter.js/component/reserve';
import { filterData, ReserveRFQFilterData } from './materialFilter.js/filterData';

const avatarSX = {
  width: 36,
  height: 36,
  fontSize: '1rem'
};

// action style
const actionSX = {
  mt: 0.75,
  ml: 1,
  top: 'auto',
  right: 'auto',
  alignSelf: 'flex-start',
  transform: 'none'
};

// ==============================|| DASHBOARD - DEFAULT ||============================== //
const LIMIT = 10;
import moment from 'moment';
import { Button } from '@mui/material';
import { Link } from 'react-router-dom';
import { ARRAY, DATE, RADIO, SEARCH, SELECT, TEXT } from './materialFilter.js/component/constant';
import MaterialSorting from './materialSorting';
import { convertDateToStringFormat } from 'utils/helper';

export const reservedRfq = {
  heading: 'Reserved',
  type: 'text',
  field: 'rs.CurrentStatus',
  data: 'RESERVED'
};

export default function ReservedRFQ() {
  const dispatch = useDispatch();
  const materialsRequest = useSelector((state) => state.rfq);
  const singleRfq = useSelector((state) => state.singleRfq);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(10);
  const [filtersData, setFiltersData] = useState([]);
  const [sortingValues, setSortingValues] = useState({});

  const [filters, setFilters] = useState([reservedRfq]);
  const [materialRequestedData, setMaterialRequestedData] = useState([]);
  const [kamList, setKamList] = useState([]);

  const buttons = [
    { buttonComponent: QuickView, type: 'button', variant: 'contained', name: 'quick' },
    { buttonComponent: ReserveBtn, type: 'button', variant: 'contained', name: 'reserver' },
    { buttonComponent: Assign, type: 'button', variant: 'contained', name: 'assign' }
  ];

  useEffect(() => {
    dispatch(getAllKamAction());
  }, []);

  useEffect(() => {
    setKamList(
      get(materialsRequest, 'kamList', []).map((item) => ({
        label: `${item.FirstName} ${item.LastName}`,
        value: item.UserID
      }))
    );
    let ROLE = getUserDetail()?.role;
    let filterOptions =
      ROLE === SUPERVISER
        ? [
            ...ReserveRFQFilterData,
            {
              heading: 'Kam',
              type: 'select',
              field: 'u.UserID',
              data: '',
              options: get(materialsRequest, 'kamList', []).map((item) => ({
                label: `${item.FirstName} ${item.LastName}`,
                value: item.UserID
              }))
            }
          ]
        : filterData;
    setFiltersData(filterOptions);
  }, [materialsRequest?.kamList]);

  useEffect(() => {
    dispatch(getAllRfq({ limit: RFQPERPAGELIMIT, page: currentPage, filterData: filters }));
  }, [currentPage, filters]);

  useEffect(() => {
    let materialData = get(materialsRequest, 'data.data', []);
    const totalCount = get(materialsRequest, 'data.count', 0);
    const calculatedTotalPages = Math.ceil(totalCount / LIMIT);
    setTotalPages(calculatedTotalPages > 0 ? calculatedTotalPages : 1);

    let user = getUserDetail();
    let role = get(user, 'role');

    if (materialData && materialData.length > 0) {
      let updatedMaterialData = materialData.reduce((acc, item) => {
        let assign = item?.CurrentStatus === RESERVED || item?.CurrentStatus === QUOTING;

        let secondaryContent = [
          { type: 'text', label: 'Dead Line', value: convertDateToStringFormat(get(item, 'Deadline.value', '')) },
          { type: 'text', label: 'RFQ Date', value: convertDateToStringFormat(get(item, 'RFQ_Date.value', '')) },
          { type: 'img' }
        ];

        const filteredButtons =
          role === KAM ? buttons.filter((button) => button.name !== 'assign' && button.name !== 'nonRelevant') : buttons;
        let buttonsToUse = assign ? getButton(item) : filteredButtons;
        acc.push({ ...item, secondaryContent, buttons: buttonsToUse });

        return acc;
      }, []);
      setMaterialRequestedData(updatedMaterialData);
    } else {
      setMaterialRequestedData([]);
    }
  }, [get(materialsRequest, 'data')]);

  const getButton = (item) => {
    const btn = [];
    btn.push({
      type: 'image',
      src: get(item, 'image', ''),
      name: get(item, 'FirstName') || 'N/A',
      link: ''
    });
    btn.push({ buttonComponent: QuickView, type: 'button', variant: 'outlined' });

    return btn;
  };

  const handlePageChange = (selected) => {
    setCurrentPage(selected);
  };

  const isLoading = () => {
    const { status } = materialsRequest || {};
    return status === 'loading' || get(materialsRequest, 'loading', false) || get(singleRfq, 'status', '') === 'loading';
  };
  const handleFilter = (filters) => {
    setCurrentPage(1);
    if (filters?.length > 0) {
      let filteredData = [];
      filteredData = filters
        ?.map((item) => {
          switch (item?.type) {
            case ARRAY:
              const selectedData = item.data?.filter((data) => data?.select).map((data) => data?.value);
              return { ...item, data: selectedData, heading: item?.payloadHeading || item?.heading };
            case SELECT:
              const { options: selectOptions, ...selectFilter } = item;
              const actualData = item?.data;
              return { type: 'select', heading: item?.payloadHeading || 'Options', data: actualData, field: item?.field };
            case RADIO:
              return { ...item, data: [item?.data], type: 'array', heading: item?.payloadHeading || item?.heading };
            case TEXT:
            case DATE:
              return item;
            case 'search':
              const { options, ...searchFilter } = item;
              return searchFilter;
          }
        })
        ?.filter((item) => {
          if (item === undefined || item === null || item === '') return false;
          if (item?.type === 'array') {
            return item.data.length > 0;
          }
          if (item?.type === 'text' || item?.type === 'date' || item?.type === 'search' || item?.type === 'select') {
            return item.data !== '' && item.data !== null && item.data !== undefined;
          }
          return true;
        });
      if (sortingValues && Object.keys(sortingValues)?.length > 0) {
        filteredData = filteredData || [];
        filteredData.push(sortingValues);
      }
      filteredData = filteredData || [];
      filteredData.push(reservedRfq);
      setFilters(filteredData);
    } else {
      let filteredData = [reservedRfq];

      if (sortingValues && Object.keys(sortingValues)?.length > 0) {
        filteredData.push(sortingValues);
      }
      if (filteredData && filteredData?.length > 0) {
        setFilters([...filteredData]);
      } else {
        setFilters([reservedRfq]);
      }
    }
  };

  const handleSorting = (sorting) => {
    const { heading } = sorting;
    setSortingValues(sorting);

    let updatedFilters = [reservedRfq];

    if (Object.keys(sorting).length === 0) {
      updatedFilters = filters.filter((filter) => filter?.heading !== 'sort');
    } else {
      updatedFilters = filters.map((filter) => (filter?.heading === 'sort' ? sorting : filter));
      const sortExists = filters.some((filter) => filter.heading === 'sort');
      if (!sortExists) {
        updatedFilters.push(sorting);
      }
    }

    updatedFilters = updatedFilters?.filter((item) => item !== undefined && item !== null && item !== '');
    setFilters(updatedFilters || [reservedRfq]);
  };

  return (
    <Grid container spacing={2} mt={4}>
      {isLoading() && <Loader />}

      <div className="rfq-container">
        <Grid container spacing={4}>
          <Grid item xs={12} md={3}>
            <MaterialFilter loading={isLoading()} filterMaterialData={filtersData} handleFilter={(data) => handleFilter(data)} />
          </Grid>
          <Grid item xs={12} md={9}>
            <Grid container justifyContent="space-between" alignItems="center">
              <Typography variant="h4" color="secondary">
                Materials Requested
              </Typography>
              <Grid item>
                <Grid container alignItems="center" spacing={2}>
                  <Grid item>
                    <MaterialSorting handleSorting={(sorting) => handleSorting(sorting)} />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>

            <MainCard content={false} sx={{ mt: 1 }}>
              <List
                component="nav"
                sx={{
                  px: 0,
                  py: 0,
                  '& .MuiListItemButton-root': {
                    '& .MuiAvatar-root': avatarSX,
                    '& .MuiListItemSecondaryAction-root': { ...actionSX, position: 'relative' }
                  }
                }}
              >
                {materialRequestedData && materialRequestedData?.length > 0 ? (
                  materialRequestedData?.map((order, index) => {
                    const nextId = index < materialRequestedData.length - 1 ? materialRequestedData[index + 1].RFQ_ID : null;

                    return (
                      <MaterialCard
                        isReservedRFQ={true}
                        editPageUrl={reserveAddEditRfq}
                        redirectTo={ReservedSingleRfqPageUrl}
                        materialRequestedData={materialRequestedData}
                        nextId={nextId}
                        loading={isLoading()}
                        key={order.id}
                        order={order}
                        buttons={get(order, 'buttons', [])}
                        secondaryContent={get(order, 'secondaryContent', [])}
                        kamList={kamList}
                        currentPage={currentPage}
                      />
                    );
                  })
                ) : (
                  <p>No RFQ found </p>
                )}
              </List>
            </MainCard>
          </Grid>

          {totalPages > 0 && (
            <Grid item xs={12}>
              <Grid container justifyContent="center">
                <Pagination currentPage={currentPage} totalPages={totalPages} handlePageChange={handlePageChange} />
              </Grid>
            </Grid>
          )}
        </Grid>
      </div>
    </Grid>
  );
}
