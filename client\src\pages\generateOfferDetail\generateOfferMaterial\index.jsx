/* eslint-disable no-unused-vars */
import React from 'react';
import { Grid, ListItemButton, Typography, Stack, Button } from '@mui/material';
import MainCard from 'components/MainCard';
import { get } from 'lodash';
import OfferMaterialDetail from './materialDetail';
import Benchmark from 'pages/offer/offerMaterial/benchMark';

const MaterialTable = ({ materials, offer, onChange, loading, supplierList, calculateOffer, updateMaterial, disableUpdateMaterial }) => {
  return (
    <MainCard sx={{ mt: 2 }} content={false}>
      <Grid className="update-material">
        <Typography variant="h4" color="secondary">
          Materials{' '}
        </Typography>
        <Button variant="contained" sx={{ mb: 2 }} onClick={calculateOffer} disabled={disableUpdateMaterial}>
          Calculate Offer
        </Button>
      </Grid>
      <Stack spacing={1}>
        {materials?.map((material, index) => (
          <>
            <Grid elevation={1} className="card-container" p={1}>
              <Grid container spacing={4} alignItems="center">
                <Grid item xs={12} sm={6} className="benchmark-wrapper">
                  <div className="material-name-wrapper">
                    <Typography variant="h5" color="secondary">
                      {get(material, 'Material_Description', '')}
                    </Typography>
                  </div>
                  <Benchmark material={material} disableBtn={loading} />
                </Grid>
              </Grid>
              <Grid display="flex" mt={1}>
                <Typography variant="body2">{`Part Number : ${get(material, 'Part_Number', '')}`}</Typography>
                <Typography variant="body2" ml={3}>
                  {`Qty : ${material?.Quantity_Required}`}
                </Typography>
              </Grid>
              <Grid spacing={2}>
                <OfferMaterialDetail
                  offer={offer}
                  supplierList={supplierList}
                  suppliers={get(material, 'suppliers', [])}
                  material={material}
                  loading={loading}
                  updateMaterial={(supplier) => updateMaterial(material, supplier)}
                  onChange={(e, material, supplier) => onChange(e, material, supplier)}
                />
              </Grid>
            </Grid>
          </>
        ))}
      </Stack>
    </MainCard>
  );
};

export default MaterialTable;
