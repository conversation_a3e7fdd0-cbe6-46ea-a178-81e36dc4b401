import { is<PERSON><PERSON><PERSON>, is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, is<PERSON>ppercase<PERSON><PERSON>, isSpecial<PERSON><PERSON>, minLength } from 'utils/password-validation';

const EMAIL_REGEX = /^[\w-+]+(\.[\w-]+)*@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*(\.[a-zA-Z]{2,})$/;
const URL_REGEX = /^(https?:\/\/)([\w-]+(\.[\w-]+)+)([\/\w-]*)*\/?(\?.*)?$/;

export const validate = (values, rules) => {
  const errors = {};

  for (const field in rules) {
    const value = typeof values[field] === 'string' ? values[field].trim() : values[field];
    const fieldRules = rules[field];

    if (fieldRules.required && !value) {
      errors[field] = `${fieldRules.label} is required`;
    }
    if (fieldRules.isNumber && value <= 0) {
      errors[field] = !value && value !== 0 ? `${fieldRules?.label} is required` : `${fieldRules?.label} must be greater than 0`;
    }
    if (fieldRules?.type === 'email' && value && !EMAIL_REGEX.test(value)) {
      errors[field] = 'Invalid email format';
    }
    if (fieldRules?.type === 'url' && value && !URL_REGEX.test(value)) {
      errors[field] = 'Invalid URL format';
    }
    if (fieldRules?.noSpaces && /\s/.test(values[field])) {
      errors[field] = `${fieldRules.label} should not contain spaces`;
    }
    if (fieldRules?.type === 'password' && value) {
      if (!minLength(value)) {
        errors[field] = `${fieldRules.label} must be at least 8 characters long`;
      }
      if (!isUppercaseChar(value)) {
        errors[field] = `${fieldRules.label} must contain at least one uppercase letter`;
      }
      if (!isLowercaseChar(value)) {
        errors[field] = `${fieldRules.label} must contain at least one lowercase letter`;
      }
      if (!isNumber(value)) {
        errors[field] = `${fieldRules.label} must contain at least one digit`;
      }
      if (!isSpecialChar(value)) {
        errors[field] = `${fieldRules.label} must contain at least one special character`;
      }
    }

    if (fieldRules?.type === 'confirmPassword' && value) {
      if (value !== values[fieldRules?.matchValue || 'password']) {
        errors[field] = 'Passwords do not match';
      }
    }
  }

  return errors;
};

export const validateNested = (values, rules, shouldTest) => {
  const errors = [];
  values?.forEach((value, index) => {
    const elementErrors = {};
    if (value.showValid  || shouldTest){ 

    for (const field in rules) {
      const fieldValue = typeof value[field] === 'string' ? value[field].trim() : value[field]; // Trim the field value to remove spaces
      const fieldRules = rules[field];

      if (fieldRules.required && !fieldValue) {
        elementErrors[field] = `${fieldRules?.label} is required`;
      }
      if (fieldRules.number && fieldValue <= 0) {
        elementErrors[field] = `${fieldRules?.label} must be greater than 0`;
      }
    }

    if (Object.keys(elementErrors).length > 0) {
      errors[index] = elementErrors;
    }
  }
  });

  return errors;
};

export const validateUniqueObject = (data, requiredFields, uniqueKeyFields) => {
  const entries = new Set();
  if (!data || data.length === 0) {
    return { error: true, message: 'No data provided' };
  }
  const errors = data?.map((entry, index) => {
    for (const field of requiredFields) {
      if (!entry[field?.value] || entry[field?.value]?.trim() === '') {
        return { error: true, message: `Invalid CSV file: Please check the format and try again.` };
      }
    }
    const uniqueKey = uniqueKeyFields.map((field) => entry[field?.value]?.trim())?.join('-');

    if (entries?.has(uniqueKey)) {
      return {
        error: true,
        message: `Duplicate entry found at row ${index + 2}: ${uniqueKeyFields?.map((field) => `${field?.label} ${entry[field?.value]}`).join(', ')}`
      };
    }
    entries.add(uniqueKey);
    return null;
  });
  const error = errors.find((err) => err !== null);
  if (error) {
    return error;
  }

  return { error: false, message: 'CSV data is valid.' };
};
