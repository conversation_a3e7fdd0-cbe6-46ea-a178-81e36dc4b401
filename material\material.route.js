const { confirmMaterial, materialHistory, searchMaterial, addMaterial, getSpecsSheet, editMaterial } = require("../material/material.controller")
const { validate } = require("../middlewares/validation.middleware")
const { confirmMaterialSchema, materialHistorySchema, searchMaterialSchema, addMaterialSchema, specsSheetSchema, editMaterialSchema } = require("../material/material.validation")

const router = require("express").Router()

router.post("/search", validate(searchMaterialSchema, "body"), searchMaterial)

router.patch("/confirm", validate(confirmMaterialSchema, "body"), confirmMaterial)

router.post("/add", validate(addMaterialSchema, "body"), addMaterial)

router.patch("/edit", validate(editMaterialSchema, "body"), editMaterial)

router.post("/history/:id", validate(materialHistorySchema, "params"), materialHistory)

router.get("/specs-sheet/:partNumber", validate(specsSheetSchema, "params"), getSpecsSheet)

module.exports = router