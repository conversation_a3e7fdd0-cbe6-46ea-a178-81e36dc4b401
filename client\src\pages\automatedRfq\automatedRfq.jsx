/* eslint-disable no-unused-vars */
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Loader from 'components/Loader';
import MainCard from 'components/MainCard';
import { get } from 'lodash';
import Pagination from 'pages/component/pagination';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getAllKamAction, getAllRfq, getAutomatedRfq, triggerAutomationAction } from 'redux/reducers/RfqReducer';
import { getUserDetail } from 'utils/auth';
import {
  IRRELEVANT,
  KAM,
  QUOTING,
  RESERVED,
  RFQPERPAGELIMIT,
  ReservedSingleRfqPageUrl,
  SUPERVISER,
  addRfqPageUrl,
  automatedRfqPageUrl,
  reserveAddEditRfq
} from 'utils/constant';

const avatarSX = {
  width: 36,
  height: 36,
  fontSize: '1rem'
};

// action style
const actionSX = {
  mt: 0.75,
  ml: 1,
  top: 'auto',
  right: 'auto',
  alignSelf: 'flex-start',
  transform: 'none'
};

// ==============================|| DASHBOARD - DEFAULT ||============================== //
const LIMIT = 5;
import moment from 'moment';
import { Button } from '@mui/material';
import { Link } from 'react-router-dom';
import { ARRAY, DATE, RADIO, SELECT, TEXT } from 'pages/availableRfq/materialFilter.js/component/constant';
// import MaterialSorting from './materialSorting';
import { convertDateToStringFormat, getItemFromLocalStorage, removeItemFromLocalStorage, setItemToLocalStorage } from 'utils/helper';
import MaterialCard from 'pages/availableRfq/materialCard';
import MaterialFilter from 'pages/availableRfq/materialFilter.js';
import Assign from 'pages/availableRfq/materialFilter.js/component/assign';
import ReserveBtn from 'pages/availableRfq/materialFilter.js/component/reserve';
import QuickView from 'pages/availableRfq/materialFilter.js/component/quickView';
import { filterData, ReserveRFQFilterData } from 'pages/availableRfq/materialFilter.js/filterData';
import MaterialSorting from 'pages/availableRfq/materialSorting';
import AutomatedMaterialCard from './automatedMaterialCard';

export const automatedRfq = {
  heading: 'Automated',
  type: 'text',
  field: 'rs.CurrentStatus',
  data: 'AUTOMATED'
};

export default function AutomatedRFQ() {
  const dispatch = useDispatch();
  const materialsRequest = useSelector((state) => state.rfq);
  const singleRfq = useSelector((state) => state.singleRfq);
  const setting = useSelector((state) => state.setting);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(10);
  const [filtersData, setFiltersData] = useState([]);
  const [sortingValues, setSortingValues] = useState({});
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);
  const [filters, setFilters] = useState([automatedRfq]);
  const [materialRequestedData, setMaterialRequestedData] = useState([]);
  const [kamList, setKamList] = useState([]);

  const buttons = [
    { buttonComponent: QuickView, type: 'button', variant: 'contained', name: 'quick' },
    { buttonComponent: ReserveBtn, type: 'button', variant: 'contained', name: 'reserver' }
  ];

  useEffect(() => {
    dispatch(getAllKamAction());
  }, []);

  useEffect(() => {
    setKamList(
      get(materialsRequest, 'kamList', []).map((item) => ({
        label: `${item.FirstName} ${item.LastName}`,
        value: item.UserID
      }))
    );

    setFiltersData(filterData);
  }, [materialsRequest?.kamList]);

  useEffect(() => {
    dispatch(getAutomatedRfq({ limit: LIMIT, page: currentPage, filterData: filters }));
  }, [currentPage, filters]);

  useEffect(() => {
    let materialData = get(materialsRequest, 'automatedRFQ.data', []);
    const totalCount = get(materialsRequest, 'automatedRFQ.count', 0);

    const calculatedTotalPages = Math.ceil(totalCount / LIMIT);
    setTotalPages(calculatedTotalPages > 0 ? calculatedTotalPages : 1);

    let user = getUserDetail();
    let role = get(user, 'role');

    if (materialData && materialData.length > 0) {
      let updatedMaterialData = materialData.reduce((acc, item) => {
        let assign = item?.CurrentStatus === RESERVED || item?.CurrentStatus === QUOTING;
        let materialCount = get(item, 'materialCount', 0);
        let supplierCount = get(item, 'supplierCount', 0);
        let status = materialCount > 0 && supplierCount > 0 && materialCount === supplierCount ? 'YES' : 'NO';
        let secondaryContent = [
          {
            type: 'array',
            data: [
              { type: 'text', label: 'Number of Materials', value: materialCount },
              { type: 'text', label: 'Quotes Received', value: supplierCount }
            ]
          },

          {
            type: 'array',
            data: [
              { type: 'badge', label: 'Ready to quote', value: status },
              { type: 'text', label: 'Last Run', value: convertDateToStringFormat(get(item, 'logDate')) }
            ]
          },
          { type: 'img' }
        ];

        const filteredButtons =
          role === KAM ? buttons.filter((button) => button.name !== 'assign' && button.name !== 'nonRelevant') : buttons;
        let buttonsToUse = assign ? getButton(item) : filteredButtons;
        acc.push({ ...item, secondaryContent, buttons: buttonsToUse });

        return acc;
      }, []);
      setMaterialRequestedData(updatedMaterialData);
    } else {
      setMaterialRequestedData([]);
    }
  }, [get(materialsRequest, 'automatedRFQ'), setting?.data?.systemUser]);

  const getButton = (item) => {
    const btn = [];
    btn.push({
      type: 'image',
      src: get(item, 'image', ''),
      name: get(item, 'FirstName') || 'N/A',
      link: ''
    });
    btn.push({ buttonComponent: QuickView, type: 'button', variant: 'outlined' });
    if (get(setting, 'data.systemUser') === get(item, 'UserID')) {
      btn?.push({ buttonComponent: ReserveBtn, type: 'button', variant: 'contained' });
    }

    return btn;
  };

  const handlePageChange = (selected) => {
    setCurrentPage(selected);
  };

  const isLoading = () => {
    const { status } = materialsRequest || {};
    return (
      status === 'loading' || get(materialsRequest, 'loading', false) || get(singleRfq, 'status', '') === 'loading' || setting?.loading
    );
  };
  const handleFilter = (filters) => {
    setCurrentPage(1);
    if (filters?.length > 0) {
      let filteredData = [];
      filteredData = filters
        ?.map((item) => {
          switch (item?.type) {
            case ARRAY:
              const selectedData = item.data?.filter((data) => data?.select).map((data) => data?.value);
              return { ...item, data: selectedData, heading: item?.payloadHeading || item?.heading };
            case RADIO:
              return { ...item, data: [item?.data], type: 'array', heading: item?.payloadHeading || item?.heading };
            case TEXT:
            case DATE:
              return item;
          }
        })
        ?.filter((item) => {
          if (item === undefined || item === null || item === '') return false;
          if (item?.type === 'array') {
            return item.data.length > 0;
          }
          if (item?.type === 'text' || item?.type === 'date') {
            return item.data !== '' && item.data !== null && item.data !== undefined;
          }
          return true;
        });
      if (sortingValues && Object.keys(sortingValues)?.length > 0) {
        filteredData = filteredData || [];
        filteredData.push(sortingValues);
      }
      filteredData = filteredData || [];
      filteredData.push(automatedRfq);
      setFilters(filteredData);
    } else {
      let filteredData = [automatedRfq];

      if (sortingValues && Object.keys(sortingValues)?.length > 0) {
        filteredData.push(sortingValues);
      }
      if (filteredData && filteredData?.length > 0) {
        setFilters([...filteredData]);
      } else {
        setFilters([automatedRfq]);
      }
    }
  };

  const handleSorting = (sorting) => {
    const { heading } = sorting;
    setSortingValues(sorting);

    let updatedFilters = [automatedRfq];

    if (Object.keys(sorting).length === 0) {
      updatedFilters = filters.filter((filter) => filter?.heading !== 'sort');
    } else {
      updatedFilters = filters.map((filter) => (filter?.heading === 'sort' ? sorting : filter));
      const sortExists = filters.some((filter) => filter.heading === 'sort');
      if (!sortExists) {
        updatedFilters.push(sorting);
      }
    }

    updatedFilters = updatedFilters?.filter((item) => item !== undefined && item !== null && item !== '');
    setFilters(updatedFilters || [automatedRfq]);
  };

  useEffect(() => {
    const nextTriggerTime = getItemFromLocalStorage('automationTriggerTime', '');
    const currentTime = Date.now();

    if (nextTriggerTime && currentTime < nextTriggerTime) {
      const timeLeft = nextTriggerTime - currentTime;
      setIsButtonDisabled(true);
      setRemainingTime(timeLeft);

      const timer = setInterval(() => {
        const timeLeft = nextTriggerTime - Date.now();
        if (timeLeft <= 0) {
          setIsButtonDisabled(false);
          setRemainingTime(0);
          clearInterval(timer);
        } else {
          setRemainingTime(timeLeft);
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, []);

  const handleTriggerAutomation = async () => {
    try {
      const response = await dispatch(triggerAutomationAction());
      if (get(response, 'payload.success')) {
        const estimatedTime = get(response, 'payload.data', 0);
        if (estimatedTime) {
          const currentTime = Date.now();
          const nextTriggerTime = currentTime + estimatedTime;
          setItemToLocalStorage('automationTriggerTime', nextTriggerTime);
          setIsButtonDisabled(true);
          setRemainingTime(estimatedTime);

          const timer = setInterval(() => {
            const timeLeft = nextTriggerTime - Date.now();
            if (timeLeft <= 0) {
              setIsButtonDisabled(false);
              setRemainingTime(0);
              clearInterval(timer);
            } else {
              setRemainingTime(timeLeft);
            }
          }, 1000);
        }
      } else {
        setRemainingTime(0);
        setIsButtonDisabled(false);
        removeItemFromLocalStorage('automationTriggerTime');
      }
    } catch (error) {
      setRemainingTime(0);
      setIsButtonDisabled(false);
      removeItemFromLocalStorage('automationTriggerTime');
      console.error('Failed to trigger automation:', error);
    }
  };

  const formatTime = (time) => {
    const totalSeconds = Math.ceil(time / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <Grid container spacing={2} mt={4}>
      {isLoading() && <Loader />}

      <div className="rfq-container">
        <Grid container spacing={4}>
          <Grid item xs={12} md={3}>
            <MaterialFilter loading={isLoading()} filterMaterialData={filtersData} handleFilter={(data) => handleFilter(data)} />
          </Grid>
          <Grid item xs={12} md={9}>
            <Grid container justifyContent="space-between" alignItems="center">
              <Typography variant="h4" color="secondary">
                Materials Requested
              </Typography>
              <Button disabled={isButtonDisabled || isLoading()} onClick={handleTriggerAutomation} variant="contained">
                Trigger Automation
              </Button>
              {isButtonDisabled && (
                <p>
                  Please wait for <strong>{formatTime(remainingTime)}</strong> before you can proceed.
                </p>
              )}
              <Grid item>
                <Grid container alignItems="center" spacing={2}>
                  <Grid item>
                    <MaterialSorting handleSorting={(sorting) => handleSorting(sorting)} />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>

            <MainCard content={false} sx={{ mt: 1 }}>
              <List
                component="nav"
                sx={{
                  px: 0,
                  py: 0,
                  '& .MuiListItemButton-root': {
                    '& .MuiAvatar-root': avatarSX,
                    '& .MuiListItemSecondaryAction-root': { ...actionSX, position: 'relative' }
                  }
                }}
              >
                {materialRequestedData && materialRequestedData?.length > 0 ? (
                  materialRequestedData?.map((order, index) => {
                    const nextId = index < materialRequestedData.length - 1 ? materialRequestedData[index + 1].RFQ_ID : null;

                    return (
                      <AutomatedMaterialCard
                        isReservedRFQ={false}
                        editPageUrl={reserveAddEditRfq}
                        redirectTo={ReservedSingleRfqPageUrl}
                        materialRequestedData={materialRequestedData}
                        nextId={nextId}
                        pageUrl={automatedRfqPageUrl}
                        loading={isLoading()}
                        key={order.id}
                        order={order}
                        buttons={get(order, 'buttons', [])}
                        secondaryContent={get(order, 'secondaryContent', [])}
                        kamList={kamList}
                        currentPage={currentPage}
                      />
                    );
                  })
                ) : (
                  <p>No RFQ found </p>
                )}
              </List>
            </MainCard>
          </Grid>

          {totalPages > 0 && (
            <Grid item xs={12}>
              <Grid container justifyContent="center">
                <Pagination currentPage={currentPage} totalPages={totalPages} handlePageChange={handlePageChange} />
              </Grid>
            </Grid>
          )}
        </Grid>
      </div>
    </Grid>
  );
}
