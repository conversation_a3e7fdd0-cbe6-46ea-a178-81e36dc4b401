import Grid from '@mui/material/Grid';
// import OrdersTable from './OrdersTable';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { get } from 'lodash';
import './offer.css';
import OfferMaterial from './offerMaterial/offerMaterialTable';
import WarningIcon from '@mui/icons-material/Warning';
import { useNavigate, useParams } from 'react-router';
import { generateOfferPageUrl, offerRfqPageUrl, ReservedSingleRfqPageUrl, singleRfqPageUrl } from 'utils/constant';
import { ALERT_SUCCESS } from 'redux/reducers/alertReducer';
import { noOfferSelectedMessage } from 'utils/validationMessage';
import {
  getOfferDetail,
  getSupplierList,
  selectedOfferAction,
  storeOfferDetail,
  updateQuoteStatusAction
} from 'redux/reducers/offerReducer';
import { getUserDetail } from 'utils/auth';
import Overview from '../singlrRfq/overView';
import Loader from 'components/Loader';
import { Alert, Box, IconButton, Typography } from '@mui/material';
import { Link } from 'react-router-dom';
import { checkIsRfqReserved, isCountryManager } from 'utils/helper';
import ReserveBtn from 'pages/availableRfq/materialFilter.js/component/reserve';
import AlertDialog from 'pages/component/dialogbox';
import AddEmailPriceRequest from 'pages/emailPriceRequest/addEmailPriceRequest';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
// ==============================|| DASHBOARD - DEFAULT ||============================== //

export default function Offer() {
  const [offer, setOffer] = useState({});
  const [sendPriceReqModal, setSendPriceReqModal] = useState(false);
  const [warningModal, setWarningModal] = useState(false);
  const offerDetail = useSelector((state) => state.offer);
  const rfq = useSelector((state) => state.rfq);
  const setting = useSelector((state) => state.setting);
  const requestedQuoteData = useSelector((state) => state.requestedQuote);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const rfqId = useParams()?.id;
  const modalRef = useRef(null);

  const checkPartNumber = () => {
    if (!checkIsRfqReserved(offer?.CurrentStatus)) {
      return {
        isPartNumber: false,
        message: 'Please confirm that the RFQ has been reserved.',
        btnText: 'Go to Reserve RFQ',
        path: `${singleRfqPageUrl}/${rfqId}`
      };
    } else if (offer?.materials?.some((material) => !material?.Part_Number)) {
      return {
        isPartNumber: false,
        message: 'Please ensure that all the part numbers have been confirmed before proceeding.',
        btnText: 'Go to Confirm Part Number',
        path: `${ReservedSingleRfqPageUrl}/${rfqId}`
      };
    }
    return { isPartNumber: true, message: '' };
  };
  const buttonsConfig = [
    {
      type: 'generate',
      label: 'Generate',
      show: true,
      disabled: !checkPartNumber()?.isPartNumber,
      onClick: () => generateOffer()
    },
    {
      type: 'close',
      label: 'Close',
      link: `${ReservedSingleRfqPageUrl}/${rfqId}`
    }
  ];
  const [buttons, setButtons] = useState(buttonsConfig);

  useEffect(() => {
    dispatch(getOfferDetail(rfqId));
    dispatch(getSupplierList());
  }, [rfqId]);

  useEffect(() => {
    if (offerDetail) {
      // Extract user details
      let user = getUserDetail();

      // Extract materials and useQuoteList
      const materials = get(offerDetail, 'data.materials', []);
      const useQuoteList = get(offerDetail, 'useQuoteList', []);

      // Create a map of materialId to quotes
      const quoteMap = useQuoteList.reduce((acc, quote) => {
        if (quote.materialId) {
          acc[quote.materialId] = quote.quotes;
        }
        return acc;
      }, {});

      // Update materials with matching quotes
      const materialsWithSuppliers = materials.map((material) => {
        const matchingQuotes = quoteMap[material?.materialId] || [];

        let selectedMaterial = offer?.materials || offerDetail?.allquote?.materials || [];
        selectedMaterial = selectedMaterial?.find((mat) => mat?.materialId === material?.materialId);
        return {
          ...material,
          exclude: selectedMaterial?.exclude,
          suppliers: [...matchingQuotes, ...material.suppliers]
        };
      });

      // Create new data object with updated materials and user info

      const data = {
        ...get(offerDetail, 'data'),
        materials: materialsWithSuppliers,
        kam:
          (
            (get(offerDetail, 'data.FirstName') || get(offerDetail, 'data.firstName') || '') +
            ' ' +
            (get(offerDetail, 'data.LastName') || get(offerDetail, 'data.lastName') || '')
          ).trim() || 'N/A'
      };
      const systemUser = get(setting, 'data.systemUser', '');
      const isSystemUserMatched = systemUser && data?.UserID && systemUser === data?.UserID;

      if (isSystemUserMatched) {
        const isReserveButtonExist = buttonsConfig.some((button) => button.label === 'Reserve');

        if (!isReserveButtonExist) {
          const reserveButton = {
            type: 'component',
            label: 'Reserve',
            hide: isCountryManager(),
            component: ReserveBtn,
            pageUrl: offerRfqPageUrl
          };

          // Update the buttons state directly
          setButtons([...buttonsConfig, reserveButton]);
        }
      } else {
        setButtons(buttonsConfig);
      }

      setOffer({ ...data });
    }
  }, [offerDetail?.data, setting?.data?.systemUser]);

  const getButtons = () => {
    const systemUser = get(setting, 'data.systemUser', '');
    const data = get(offerDetail, 'data', {});
    const isSystemUserMatched = systemUser && data?.UserID && systemUser === data?.UserID;

    if (isSystemUserMatched) {
      const isReserveButtonExist = buttonsConfig.some((button) => button.label === 'Reserve');
      if (!isReserveButtonExist) {
        return [
          ...buttonsConfig,
          {
            type: 'component',
            hide: isCountryManager(),
            label: 'Reserve',
            component: ReserveBtn,
            pageUrl: offerRfqPageUrl
          }
        ];
      }
    }
    return buttonsConfig;
  };

  const generateOffer = () => {
    let offerSelected = false;
    let materialIncluded = false;
    const filteredMaterials = offer?.materials
      ?.map((material) => {
        if (material?.exclude) {
          return null;
        }
        const filteredSuppliers = material?.suppliers?.filter((supplier) => {
          if (supplier.offer) {
            offerSelected = true;
            return true;
          }
          return false;
        });
        if (filteredSuppliers?.length > 0) {
          materialIncluded = true;
          return {
            ...material,
            suppliers: filteredSuppliers
          };
        }
        return null;
      })
      .filter(Boolean);

    if (!materialIncluded) {
      dispatch({ type: ALERT_SUCCESS, payload: { success: false, message: 'No material is included.', error: true } });
    } else if (!offerSelected) {
      dispatch({ type: ALERT_SUCCESS, payload: { success: false, message: noOfferSelectedMessage, error: true } });
    } else {
      dispatch(selectedOfferAction({ ...offer, materials: filteredMaterials }));
      dispatch(storeOfferDetail(offer));
      navigate(`${generateOfferPageUrl}/${rfqId}`);
    }
  };

  const selectSupplier = async (materialId, quoteId) => {
    const selectedMaterial = offer?.materials?.find((material) => material?.materialId === materialId);
    const selectedSupplier = selectedMaterial?.suppliers?.find((supplier) => supplier?.id === quoteId);

    let payload = {
      quoteId: quoteId?.toString(),
      isOffered: !selectedSupplier?.offer || false,
      rfqId: rfqId
    };
    dispatch(updateQuoteStatusAction(payload));
  };

  const handleExcludeChange = (materialId) => {
    setOffer((prevResponse) => {
      const updatedMaterials = prevResponse?.materials?.map((material) => {
        if (material.materialId === materialId) {
          return { ...material, exclude: !material.exclude };
        }
        return material;
      });
      return { ...prevResponse, materials: updatedMaterials };
    });
  };

  const isLoading = () => {
    return (
      offerDetail?.loading ||
      offerDetail?.status === 'loading' ||
      requestedQuoteData?.loading ||
      get(rfq, 'loading', false) ||
      setting?.loading
    );
  };

  const getPathToRedirect = () => {
    return offer?.materials?.some((material) => !material?.Part_Number) || !checkIsRfqReserved(offer?.CurrentStatus);
  };

  const sendPriceRequestOpenModal = () => {
    setSendPriceReqModal(true);
  };

  const sendPriceRequestCloseModal = () => {
    confirmCloseModal();
  };

  const confirmCloseModal = () => {
    setSendPriceReqModal(false);
    dispatch(getSupplierList());
    setWarningModal(false);
    setWarningModal(false);
  };

  const rejectCloseModal = () => {
    setWarningModal(false);
  };
  const warningButtons = [
    { label: 'cancel', onClick: () => rejectCloseModal(), variant: 'contained', color: 'error' },
    { label: 'confirm', onClick: () => confirmCloseModal(), variant: 'contained' }
  ];
  const { message, btnText, path, isPartNumber } = checkPartNumber() || {};
  return (
    <Grid container spacing={2}>
      {warningModal && (
        <AlertDialog
          buttons={warningButtons || []}
          Component={
            <>
              <Box minWidth={'400px'}>
                <Box display={'flex'} gap={1} mb={2}>
                  <WarningAmberIcon color="warning" sx={{ mt: '4px' }} mt={2} />
                  <Typography variant="h3" sx={{ color: 'orange' }}>
                    Warning
                  </Typography>
                </Box>
                <Typography variant="h6">Closing this dialog will discard any entered information. </Typography>
                <Typography variant="h6">Do you want to proceed? </Typography>
              </Box>
            </>
          }
          cancel={rejectCloseModal}
          open={warningModal}
          borderRadius="20px"
        />
      )}

      {sendPriceReqModal && (
        <AlertDialog
          Component={
            <>
              <Box ref={modalRef} p={1} maxHeight="80vh" overflowY="auto">
                <AddEmailPriceRequest
                  modalRef={modalRef}
                  rfqDetails={offer}
                  handleCancel={() => setWarningModal(true)}
                  handleCloseModal={sendPriceRequestCloseModal}
                  isSingleRFQPage={true}
                />
              </Box>
            </>
          }
          // cancel={sendPriceRequestCloseModal}
          open={sendPriceReqModal}
          fullScreen={true}
          borderRadius="20px"
        />
      )}
      {isLoading() && <Loader />}
      {!isLoading() && !isPartNumber && (
        <Grid item xs={12} style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
          <Alert severity="error">
            <div style={{ display: 'flex', gap: '10px' }}>
              <Typography variant="body1" color="warning">
                {message}
              </Typography>
              <Link to={path} className="link">
                {btnText}
              </Link>
            </div>
          </Alert>
        </Grid>
      )}
      <Grid item xs={12}>
        <Overview rfqDetails={offer} buttonsConfig={getButtons()} />
      </Grid>
      <Grid item xs={12}>
        <OfferMaterial
          sendPriceRequestOpenModal={sendPriceRequestOpenModal}
          offer={offer}
          loader={isLoading() || !isPartNumber}
          selectSupplier={(materialId, supplierId) => selectSupplier(materialId, supplierId)}
          supplierList={get(offerDetail, 'supplierList', [])?.map((supplier) => ({
            label: supplier?.Name,
            value: supplier?.SupplierID,
            Status: supplier?.Status
          }))}
          handleExcludeChange={handleExcludeChange}
        />
      </Grid>
    </Grid>
  );
}
