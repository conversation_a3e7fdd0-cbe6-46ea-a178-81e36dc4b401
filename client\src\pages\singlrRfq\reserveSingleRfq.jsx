import Grid from '@mui/material/Grid';
import Loader from 'components/Loader';
import { get, isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { getSingleRfq } from 'redux/reducers/singleRfq';
import {
  QUOTING,
  ReservedSingleRfqPageUrl,
  automatedRfqPageUrl,
  offerPageUrl,
  reserveRfqPageUrl,
  reservedHistoryPageUrl
} from 'utils/constant';
import { checkIsRfqReserved, getDateWithTime, isCountryManager, showAlert } from 'utils/helper';
import { rfqNotReservedMessage } from 'utils/validationMessage';
import Materials from './material';
import Overview from './overView';
import './singlrRfq.css';
import { assignRfqAction } from 'redux/reducers/RfqReducer';
import Release from './buttons/release/release';
import { Alert, Typography } from '@mui/material';
import ReserveBtn from 'pages/availableRfq/materialFilter.js/component/reserve';

export default function ReserveSingleRfq() {
  const [singleRfq, setSingleRfq] = useState({});
  const singleRfqData = useSelector((state) => state.singleRfq);
  const myoffer = useSelector((state) => state.myOffer);
  const setting = useSelector((state) => state.setting);
  const allRfq = useSelector((state) => state.rfq);
  const quoteOffer = useSelector((state) => state.offer);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const rfqId = useParams()?.id;

  const isLoading = () => {
    const { status } = singleRfqData || {};
    return (
      status === 'loading' ||
      get(singleRfqData, 'loading', false) ||
      get(allRfq, 'loading', false) ||
      myoffer?.status === 'loading' ||
      myoffer?.loading ||
      quoteOffer?.loading ||
      setting?.loading
    );
  };

  const buttonsConfig = [
    {
      type: 'button',
      label: 'Quote',
      show: !isCountryManager(),
      link: `/offer/${rfqId}`,
      disabled: isLoading(),

      onClick: async () => {
        const isAnyOnePartNumberConfirmed = get(singleRfqData, 'data.Materials', [])?.some(
          (material) => material?.ConfirmationStatus === 'Done'
        );

        if (checkIsRfqReserved(singleRfqData?.data?.CurrentStatus)) {
          if (isAnyOnePartNumberConfirmed) {
            const response = await dispatch(
              assignRfqAction({
                rfqId: rfqId?.toString(),
                status: QUOTING
              })
            );
            const { success } = get(response, 'payload', {});
            if (success) {
              navigate(`${offerPageUrl}/${rfqId}`);
            }
          } else {
            showAlert(dispatch, false, 'Please confirm at least one part number before proceeding.', true);
          }
        } else {
          showAlert(dispatch, false, rfqNotReservedMessage, true);
        }
      }
    },
    {
      type: 'close',
      label: 'Close',
      smallBtn: false,
      disabled: isLoading(),
      link: reserveRfqPageUrl
    }
  ];

  const [buttons, setButtons] = useState(buttonsConfig);

  useEffect(() => {
    if (rfqId) {
      dispatch(getSingleRfq(rfqId));
    }
  }, [rfqId]);

  useEffect(() => {
    const singleRfqDetail = get(singleRfqData, 'data', {});
    const systemUser = get(setting, 'data.systemUser', '');
    const userId = get(singleRfqDetail, 'UserID', '');
    const firstName = get(singleRfqDetail, 'FirstName', get(singleRfqDetail, 'firstName', ''));
    const lastName = get(singleRfqDetail, 'LastName', get(singleRfqDetail, 'lastName', ''));
    const kam = `${firstName || ''} ${lastName || ''}`.trim() || 'N/A';

    if (!isEmpty(singleRfqDetail)) {
      setSingleRfq({ ...singleRfqDetail, kam });

      setButtons((prevButtons) => {
        const updatedButtons = prevButtons.map((button) =>
          button.type === 'close'
            ? {
                ...button,
                link: systemUser === userId ? automatedRfqPageUrl : reserveRfqPageUrl
              }
            : button
        );

        if (systemUser === userId) {
          if (!updatedButtons.some((button) => button.label === 'Reserve')) {
            updatedButtons.push({
              type: 'component',
              label: 'Reserve',
              hide: isCountryManager(),
              pageUrl: ReservedSingleRfqPageUrl,
              component: ReserveBtn
            });
          }
        } else {
          if (!updatedButtons.some((button) => button.label === 'Release')) {
            updatedButtons.push({
              type: 'component',
              label: 'Release',
              hide: isCountryManager(),
              component: Release
            });
          }
        }

        return updatedButtons;
      });
    }
  }, [singleRfqData?.data, setting?.data?.systemUser]);

  const getButtons = () => {
    const systemUser = get(setting, 'data.systemUser', '');
    const userId = get(singleRfq, 'UserID', '');

    const updatedButtons = buttonsConfig.map((button) =>
      button.type === 'close'
        ? {
            ...button,
            link: systemUser === userId ? automatedRfqPageUrl : reserveRfqPageUrl
          }
        : button
    );

    if (systemUser === userId && !updatedButtons.some((button) => button.label === 'Reserve')) {
      updatedButtons.push({
        type: 'component',
        label: 'Reserve',
        hide: isCountryManager(),
        pageUrl: ReservedSingleRfqPageUrl,
        component: ReserveBtn
      });
    } else if (!updatedButtons.some((button) => button.label === 'Release')) {
      updatedButtons.push({
        type: 'component',
        label: 'Release',
        hide: isCountryManager(),
        component: Release
      });
    }

    return updatedButtons;
  };

  return (
    <div className="rfq-container">
      <Grid container spacing={2}>
        {isLoading() && <Loader />}

        {!isLoading() && singleRfq?.Status === 'ERROR' && (
          <Grid item xs={12} style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
            <Alert severity="error">
              <div style={{ display: 'flex', gap: '10px' }}>
                <Typography variant="body1" color="warning">
                  {singleRfq?.logMessage}
                </Typography>
              </div>
              
              {singleRfq?.logDate && (
                <Typography variant="body1" color="warning">
                  Automation has been processed on {getDateWithTime(singleRfq?.logDate)}.
                </Typography>
              )}
            </Alert>
          </Grid>
        )}

        <Grid item xs={12}>
          <Overview isAutomatedRFQ={singleRfq?.Status} rfqDetails={singleRfq} buttonsConfig={getButtons() || []} loading={isLoading()} />
        </Grid>

        <Grid item xs={12}>
          <Materials
            loading={isLoading()}
            materials={get(singleRfq, 'Materials', [])}
            rfqId={rfqId}
            rfqStatus={singleRfq?.CurrentStatus || ''}
            showConfirmBtn={true}
            historyPageUrl={reservedHistoryPageUrl}
          />
        </Grid>

        <Grid item md={8} sx={{ display: { sm: 'none', md: 'block', lg: 'none' } }} />
      </Grid>
    </div>
  );
}
