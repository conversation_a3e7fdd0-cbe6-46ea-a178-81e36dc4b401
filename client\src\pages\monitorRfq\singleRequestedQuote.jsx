import { get } from 'lodash';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import { getAllSuppliersRequestedQuotes, getSupplierRequestedQuotes } from 'redux/reducers/supplierPortalReducer';
import MaterialRequestedCard from './materialRquestedCard';
import MainCard from 'components/MainCard';
import { Button, Typography } from '@mui/material';
import BadgeInputComponent from 'pages/component/table/badgeInput';
import Loader from 'components/Loader';
import { convertDateToStringFormat } from 'utils/helper';
import { Link } from 'react-router-dom';
import { monitorRfqPageUrl } from 'utils/constant';

const SingleRequestedQuote = () => {
  const requestedQuoteList = useSelector((state) => state.requestedQuote);
  const dispatch = useDispatch();
  const [requestedQuote, setRequestedQuote] = useState({});
  const supplierId = useParams()?.id;
  useEffect(() => {
    if (supplierId) {
      dispatch(getSupplierRequestedQuotes(supplierId));
    }
  }, []);

  useEffect(() => {
    const quotes = get(requestedQuoteList, 'data', {});
    const updatedQuotes = {
      ...quotes,
      materialRequested: quotes?.materialRequested?.map((quote) => {
        const { FirstName, name, LastName, RFQ_Date, Delivery_Date, Deadline } = quote || {};
        return {
          ...quote,
          kam: name || 'N/A',
          RFQ_Date: typeof RFQ_Date === 'object' ? RFQ_Date?.value || '' : RFQ_Date || '',
          Delivery_Date: typeof Delivery_Date === 'object' ? Delivery_Date?.value || '' : Delivery_Date || '',
          Deadline: typeof Deadline === 'object' ? Deadline?.value || '' : Deadline || ''
        };
      })
    };
    setRequestedQuote({ ...updatedQuotes });
  }, [requestedQuoteList?.data]);

  const SupplierCard = ({ label, value, type }) => {
    const renderValue = () => {
      switch (type) {
        case 'badge':
          return value ? (
            <div className={value === 'PENDING' ? 'status-badge-pending' : 'status-badge-request'}>
              <BadgeInputComponent color={value} badgeContent={value} onClick={() => {}} />
            </div>
          ) : (
            'N/A'
          );
        case 'date':
          return convertDateToStringFormat(value) || 'N/A';
        default:
          return <Typography variant="body1">{value || 'N/A'}</Typography>;
      }
    };
    return (
      <>
        <div className="supplier-card">
          <div className="supplier-card-value">
            <Typography variant="body1" color="secondary" className="supplier-card-label">
              {label}
            </Typography>
          </div>
          <div className="supplier-card-value">{renderValue()}</div>
        </div>
      </>
    );
  };
  const { supplierName, supplierEmail, status, requestDate, respondDate } = requestedQuote || {};
  const isLoading = () => {
    const { loading, status } = requestedQuoteList || {};
    return status === 'loading' || loading;
  };

  return (
    <>
      {isLoading() && <Loader />}
      <div className="supplier-main-container">
        <Typography variant="h4" color="secondary">
          Supplier
        </Typography>
        <Link to={monitorRfqPageUrl}>
          <Button variant="contained">Close</Button>
        </Link>
      </div>

      <MainCard sx={{ mt: 2 }} content={false} className="over-view-container supplier-detail-card">
        <SupplierCard label="Supplier Name" value={supplierName} />
        <SupplierCard label="Supplier Email" value={supplierEmail} />
        <SupplierCard label="Requested Date" value={requestDate?.value} type="date" />
        <SupplierCard label="Responded Date" value={respondDate?.value} type="date" />
        <SupplierCard label="Status" value={status} type="badge" />
      </MainCard>

      <MaterialRequestedCard materials={get(requestedQuote, 'materialRequested', [])} />
    </>
  );
};
export default SingleRequestedQuote;
