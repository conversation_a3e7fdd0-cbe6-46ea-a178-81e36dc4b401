import React, { useEffect, useState } from 'react';
import { Button, TextField, Box, Typography } from '@mui/material';
import TableComponent from 'pages/component/table/table';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import ActionButton from 'pages/component/actionButton';
import InputField from 'pages/component/inputField';
import { validate } from 'pages/component/validation';
import { useDispatch, useSelector } from 'react-redux';
import { get } from 'lodash';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import { addDashboardAction, deleteDashboardAction, getSetting, updateDashboardAction } from 'redux/reducers/settingReducer';
import AlertDialog from 'pages/component/dialogbox';
import CheckBoxInput from 'pages/component/table/checkboxInput';
import { COUNTRY_MANAGER, COUNTRY_MANAGER_LABEL, KAM, SUPERVISER } from 'utils/constant';

const AddDashboards = () => {
  const [dashboards, setDashboards] = useState([]);
  const [allDashboard, setAllDashboard] = useState([]);
  const dispatch = useDispatch();
  const setting = useSelector((state) => state.setting);
  const [errors, setErrors] = useState({});
  const [open, setOpen] = useState(false);
  const [dashboardID, setDashboardID] = useState('');

  useEffect(() => {
    let updatedData = get(setting, 'data.dashboards', [])?.map((dashboard) => ({
      ...dashboard,
      visibleTo: [
        dashboard?.visible_to_supervisor && { name: SUPERVISER, color: '#4cbf4c' },
        dashboard?.visible_to_kam && { name: KAM, color: '#c58246' },
        dashboard?.visible_to_country_manager && { name: COUNTRY_MANAGER_LABEL, color: '#cc6292' }
      ]?.filter(Boolean)
    }));

    setDashboards(updatedData || []);
    setAllDashboard(updatedData || []);
  }, [setting?.data]);

  const isLoading = () => {
    return setting?.loading || setting?.status === 'loading';
  };

  const headers = [
    {
      name: 'ID',
      type: 'text',
      title: 'ID',
      sortingactive: true,
      minWidth: '170px'
    },
    {
      name: 'Date',
      type: 'date',
      title: 'Date',
      sortingactive: true,
      minWidth: '170px'
    },
    {
      name: 'Name',
      type: 'text',
      title: 'Dashboard Name',
      defaultValue: '0',
      sortingactive: true
    },
    {
      name: 'Link',
      type: 'link',
      title: 'Dashboard Link',
      sortingactive: true
    },
    {
      keyName: 'visibleTo',
      name: 'name',
      type: 'array',
      onlyTextColour: true,
      arrayType: 'badge',
      title: 'Visible To',
      sortingactive: true
    },
    {
      name: 'actions',
      btnType: 'multipleButton',
      type: 'actions',
      title: 'Action',
      sortingactive: false,
      component: ActionButton,
      multipleButtons: [
        {
          type: 'icon',
          icon: <EditOutlinedIcon fontSize="16px" />,
          buttonOnClick: (type, rowData) => {
            let seletedSupplier = dashboards?.find((dashboard) => dashboard?.ID === rowData?.ID);
            setErrors({});

            setNewDashboard({ ...seletedSupplier, Name: seletedSupplier?.Name, Link: seletedSupplier?.Link, ID: seletedSupplier?.ID });
          },
          disabledBtn: isLoading(),
          color: 'primary',
          tooltip: 'Edit'
        },
        {
          icon: <DeleteOutlineOutlinedIcon fontSize="16px" />,
          type: 'icon',
          disabledBtn: isLoading(),
          buttonOnClick: (type, rowData) => {
            setDashboardID(rowData?.ID);
            setOpen(true);
          },
          color: 'error',
          tooltip: 'Delete'
        }
      ]
    }
  ];

  const [newDashboard, setNewDashboard] = useState({ Name: '', Link: '' });

  const handleInputChange = (name, value) => {
    setNewDashboard({ ...newDashboard, [name]: value });
  };

  const addDashboard = async () => {
    let rules = {
      Name: { required: true, label: 'Name' },
      Link: { required: true, label: 'Link' }
    };
    let validationError = validate(newDashboard, rules);
    setErrors(validationError);
    if (Object.keys(validationError)?.length === 0) {
      let response = {};
      let payload = {
        ID: newDashboard?.ID,
        Name: newDashboard?.Name,
        Link: newDashboard?.Link,
        visible_to_supervisor: newDashboard?.visible_to_supervisor || false,
        visible_to_kam: newDashboard?.visible_to_kam || false,
        visible_to_country_manager: newDashboard?.visible_to_country_manager || false
      };
      if (newDashboard?.ID) {
        response = await dispatch(updateDashboardAction(payload));
      } else {
        response = await dispatch(addDashboardAction(payload));
      }
      if (get(response, 'payload.success')) {
        dispatch(getSetting());
        setNewDashboard({ Name: '', Link: '' });
      }
    }
  };

  const cancel = () => {
    setErrors({});
    setNewDashboard({});
  };

  const handleDelete = async () => {
    const response = await dispatch(deleteDashboardAction(dashboardID));
    if (get(response, 'payload.success', {})) {
      handleClose();
    }
  };

  const inputFields = [
    { type: 'text', name: 'Name', placeholder: 'Dashboard Name', label: 'Dashboard Name', flex: 1 },
    { type: 'text', name: 'Link', placeholder: 'Dashboard Link', label: 'Dashboard Link', flex: 1 },
    { type: 'checkbox', name: 'visible_to_kam', placeholder: ' KAM ', label: 'Visible To' },
    { type: 'checkbox', name: 'visible_to_supervisor', placeholder: ' Supervisor ' },
    { type: 'checkbox', name: 'visible_to_country_manager', placeholder: ' Country Manager ' }
  ];

  const renderInputField = ({ type, name, placeholder, label }) => {
    const value = newDashboard[name] || '';

    switch (type) {
      case 'text':
        return (
          <>
            <Typography variant="h6" color="secondary" mt={2}>
              {label}
            </Typography>
            <InputField
              type="text"
              name={name}
              placeholder={placeholder}
              errors={errors}
              value={value}
              onChange={(e) => handleInputChange(name, e.target.value)}
              fullWidth
            />
          </>
        );
      case 'checkbox':
        return (
          <>
            {label && (
              <Typography variant="h6" color="secondary" mt={2}>
                {label}
              </Typography>
            )}
            <CheckBoxInput
              checked={value || false}
              value={value || false}
              formLabel={placeholder}
              onChange={(e) => handleInputChange(name, e.target.checked)}
            />
          </>
        );
      default:
        return null;
    }
  };

  const handleClose = () => {
    setOpen(false);
    setDashboardID('');
  };

  const handleSearch = (filteredData) => {
    setDashboards(filteredData || []);
  };
  const buttons = [
    { label: 'Cancel', onClick: () => handleClose(), variant: 'outlined', color: 'error', disabled: isLoading() },
    { label: 'Yes', onClick: () => handleDelete(), variant: 'contained', color: 'primary', disabled: isLoading() }
  ];
  return (
    <Box sx={{ p: 2 }}>
      <AlertDialog
        Component={
          <Typography variant="body1" color="secondary">
            {'Are you sure you want to delete this Dashboard'}
          </Typography>
        }
        open={open}
        showCard={false}
        borderRadius="20px"
        buttons={buttons}
      />
      <Typography variant="h5" color="secondary" mb={2}>
        {newDashboard?.ID ? 'Update Dashboard' : 'Add New Dashboard'}
      </Typography>
      <Box maxWidth={'600px'}>
        {inputFields
          .filter((field) => field.type !== 'checkbox')
          .map((field, index) => (
            <Box key={index}>{renderInputField(field)}</Box>
          ))}

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          {inputFields
            .filter((field) => field.type === 'checkbox')
            .map((field, index) => (
              <Box key={`checkbox-${index}`}>{renderInputField(field)}</Box>
            ))}
        </Box>
        <Box display={'flex'} justifyContent={'flex-end'} gap={1}>
          <Button variant="contained" color="error" onClick={cancel} disabled={isLoading()}>
            Cancel
          </Button>
          <Button variant="contained" color="primary" onClick={addDashboard} disabled={isLoading()}>
            {newDashboard?.ID ? 'Update' : 'Add'}
          </Button>
        </Box>
      </Box>

      <Box mt={3}>
        <TableComponent
          columns={headers}
          maxHeight="100%"
          rows={dashboards || []}
          enablePagination={true}
          allRows={allDashboard || []}
          placeActionButtonsIn="header"
          enableSearch={true}
          handleSearch={handleSearch}
        />
      </Box>
    </Box>
  );
};

export default AddDashboards;
