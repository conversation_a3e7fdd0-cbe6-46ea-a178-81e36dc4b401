/* eslint-disable no-unused-vars */
import React from 'react';
import { Grid, ListItemButton, Typography, Stack, Button } from '@mui/material';
import MainCard from 'components/MainCard';
import { get } from 'lodash';
import OfferMaterialDetail from './materialDetail';
import OfferMaterialForm from 'pages/offer/offerMaterial/offerMaterialForm';
import { calculateQuoteAction } from 'redux/reducers/offerReducer';
import '../../offer/offer.css';
import { useDispatch } from 'react-redux';
import { getMySingleOfferDetail } from 'redux/reducers/myOffersReducer';
import Benchmark from 'pages/offer/offerMaterial/benchMark';
const initialValues = {
  date: '',
  supplier: '',
  unitPrice: 0,
  quantity: 0,
  weight: 0,
  currency: '',
  leadTime: '',
  notes: '',
  isTax: true
};
const MaterialTable = ({ materials, setOffer, offer, onChange, onBlur, supplierList, rfqId, offerId, loading }) => {
  const dispatch = useDispatch();

  const addSupplier = async (newSupplier, materialId, resetForm) => {
    const { supplier, currency, leadTime, unitPrice, quantity, isTax, weight, notes } = newSupplier || {};
    let initialPayload = {
      materialId: materialId?.toString(),
      rfqId: rfqId,
      isEdit: false,
      leadTime: leadTime,
      supplierId: supplier?.toString(),
      unitPrice: Number(unitPrice),
      currency: currency?.toString(),
      weight: Number(weight),
      quantity: Number(quantity),
      isTax: isTax,
      isOffered: false,
      notes
    };
    const response = await dispatch(calculateQuoteAction(initialPayload));
    const { success, error, data } = get(response, 'payload', {});
    if (success) {
      if (offerId) {
        dispatch(getMySingleOfferDetail(offerId));
      }
      resetForm();
    }
  };

  const showForm = (selectedMaterial, showAddBtn) => {
    let updatedMaterials = materials?.map((material) => {
      if (material?.Material_ID === selectedMaterial?.Material_ID) {
        return { ...material, showAddBtn: showAddBtn };
      } else {
        return material;
      }
    });
    setOffer({ ...offer, materials: updatedMaterials });
  };
  const showAddBtn = (selectedMaterial) => {
    showForm(selectedMaterial, true);
  };
  const removeAddButton = (selectedMaterial) => {
    showForm(selectedMaterial, false);
  };

  return (
    <MainCard sx={{ mt: 2 }} content={false}>
      <Typography variant="h4" color="secondary">
        Materials{' '}
      </Typography>

      <Stack spacing={1}>
        {materials && materials?.length > 0 ? (
          materials?.map((material, index) => (
            <>
              <Grid elevation={1} className="card-container" p={1}>
                <Grid container spacing={4} alignItems="center">
                  <Grid className="request-quote-wrapper" item xs={12} sm={6} sx={{ width: '100%', display: 'flex', gap: '20px' }}>
                    <div className="material-description-wrapper">
                      <Typography variant="h5" color="secondary">
                        {get(material, 'Material_Description', '')}{' '}
                      </Typography>
                    </div>
                    <div style={{ display: 'flex', gap: '10px' }}>
                      <Button
                        className="material-request-btn"
                        variant="contained"
                        color={material?.showAddBtn ? 'error' : 'primary'}
                        size="small"
                        onClick={() => (material?.showAddBtn ? removeAddButton(material) : showAddBtn(material))}
                      >
                        {material?.showAddBtn ? 'Cancel' : 'Add quotes'}
                      </Button>
                      <div style={{ marginLeft: '10px' }}>
                        <Benchmark material={material} />
                      </div>
                    </div>
                  </Grid>
                </Grid>

                <Grid display="flex" mt={1} mb={2}>
                  <Typography variant="body2">{`Part Number : ${get(material, 'Part_Number', '')}`}</Typography>
                  <Typography variant="body2" ml={3}>
                    {`Qty : ${material?.Quantity_Required}`}
                  </Typography>
                </Grid>

                {material?.showAddBtn && (
                  <OfferMaterialForm
                    quantity={material?.Quantity_Required}
                    loading={loading}
                    supplierList={supplierList}
                    suppliers={material?.suppliers}
                    addSupplier={(data, resetForm) => addSupplier(data, material?.Material_ID, resetForm)}
                    editSupplier={(data, resetForm) => editSupplier(data, material?.materialId, data?.id, resetForm)}
                    editMode={material?.isEditing}
                    initialValues={material?.selectedSupplier || initialValues}
                  />
                )}

                <Grid spacing={2}>
                  <OfferMaterialDetail
                    supplierList={supplierList}
                    suppliers={get(material, 'suppliers', [])}
                    material={material}
                    onChange={(e, material, supplier) => onChange(e, material, supplier)}
                    onBlur={onBlur}
                  />
                </Grid>
              </Grid>
            </>
          ))
        ) : (
          <p>No Material Found</p>
        )}
      </Stack>
    </MainCard>
  );
};

export default MaterialTable;
