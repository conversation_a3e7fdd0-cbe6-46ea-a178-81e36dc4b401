import React, { useState } from 'react';
import { FormControlLabel, Checkbox } from '@mui/material';
import { styled } from '@mui/system';
import InputField from 'pages/component/inputField';

const CustomCheckbox = styled(Checkbox)(({ theme }) => ({
  '& .MuiSvgIcon-root': {
    fontSize: 14,
    borderRadius: '100px'
  }
}));

const UseQuote = ({ quoteInfoState }) => {
  const [useQuoteInfo, setUseQuoteInfo, errors] = quoteInfoState;
  const handleInputChange = (name, value) => {
    setUseQuoteInfo((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const inputFields = [
    { type: 'number', name: 'weight', placeholder: 'Unit Weight (kg)', value: useQuoteInfo.weight || '' },
    { type: 'number', name: 'quantity', placeholder: 'Qty', value: useQuoteInfo.quantity || '' },
    { type: 'number', name: 'leadTime', placeholder: 'Lead Time', value: useQuoteInfo.leadTime || '' },
    { type: 'checkbox', name: 'isTax', label: 'AV', checked: useQuoteInfo.isTax || false }
  ];

  const renderInputField = ({ type, name, placeholder, value, label, checked }) => {
    switch (type) {
      case 'number':
        return (
          <InputField
            type="number"
            name={name}
            placeholder={placeholder}
            errors={errors}
            value={value}
            onChange={(e) => handleInputChange(name, name === 'quantity' ? parseInt(e.target.value) : Number(e.target.value))}
            fullWidth
          />
        );
      case 'text':
        return (
          <InputField
            type="text"
            name={name}
            placeholder={placeholder}
            errors={errors}
            value={value}
            onChange={(e) => handleInputChange(name, e.target.value)}
            fullWidth
          />
        );
      case 'checkbox':
        return (
          <FormControlLabel
            sx={{ marginLeft: '-5px', borderRadius: '8px', mt: 1 }}
            control={
              <CustomCheckbox
                checked={checked}
                onChange={() => handleInputChange(name, !checked)}
                name={name}
              />
            }
            label={label}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className='use_quote_popup'>
      {inputFields.map((field, index) => (
        <div key={index} style={{ margin: '10px' }} >
          {renderInputField(field)}
        </div>
      ))}
    </div>
  );
};

export default UseQuote;
