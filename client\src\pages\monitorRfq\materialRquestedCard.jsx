import { <PERSON><PERSON>, Box, Button, Grid, Stack, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import { Link } from 'react-router-dom';
import { badgeColor } from 'utils/helper';
import './style.css';
import BadgeInputComponent from 'pages/component/table/badgeInput';

const MaterialRequestedCard = ({ materials }) => {
  const labels = [
    { name: 'RFQ_ID', label: 'RFQ ID', type: 'text' },
    { name: 'RFQ_Number', label: 'RFQ Number', type: 'text' },
    { name: 'RFQ_Name', label: 'RFQ Name', type: 'text' },
    { name: 'RFQ_Date', label: 'RFQ Date', type: 'text' },
    { name: 'Delivery_Date', label: 'Delivery Date', type: 'text' },
    { name: 'Deadline', label: 'Deadline Date', type: 'text' },
    { name: 'brand', label: 'Brand', type: 'text' },
    { name: 'productCondition', label: 'Condition', type: 'text' },
    { name: 'unitPrice', label: 'Unit Price', type: 'text' },
    { name: 'quantity', label: 'Quantity', type: 'text' },
    { name: 'currency', label: 'Currency', type: 'text' },
    { name: 'URL', label: 'Url', type: 'link' },
    { name: 'kam', label: 'Kam', type: 'text' },
    { name: 'leadTime', label: 'Lead Time', type: 'text' },
    { name: 'notes', label: 'Notes', type: 'text' }
  ];
  const csvlabels = [
    { name: 'partNumber', label: 'Part Number' },
    { name: 'brand', label: 'Brand' },
    { name: 'quantity', label: 'Quantity' }
  ];

  // Function to convert materials data to CSV
  const convertToCSV = (materials) => {
    const headers = csvlabels.map((label) => label.label).join(',');
    const rows = materials.map((material) =>
      csvlabels
        .map(({ name }) => {
          let value = material[name];
          if (typeof value === 'object') {
            value = JSON.stringify(value);
          }
          return `"${value || 'N/A'}"`;
        })
        .join(',')
    );
    return `${headers}\n${rows.join('\n')}`;
  };

  // Function to download CSV file using native browser functionality
  const downloadCSV = () => {
    const csvData = convertToCSV(materials);
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'materials.csv');
    document.body.appendChild(link);

    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  const renderKeyValuePairs = (entries) => {
    return (
      <Box className="over-view-box material-requested">
        {entries.map((entry, index) => (
          <Box key={index} className="requests-inner-container" mr={1} mb={3}>
            <div style={{ width: '100px' }}>
              <Typography variant="body1" className="over-view-label">
                {entry.label}
              </Typography>
            </div>
            <Typography variant="body1" className="over-view-value" mt={2}>
              <span className="material-value">
                {entry.label === 'Url' && entry?.value ? (
                  <Link to={entry?.value || '#'} className="link" target="_blank" rel="noopener noreferrer">
                    Link
                  </Link>
                ) : (
                  entry.value || 'N/A'
                )}
              </span>
            </Typography>
          </Box>
        ))}
      </Box>
    );
  };

  const labelValuePairs = (material) => {
    return material
      ? labels.map(({ name, label, type }) => {
          let value = material[name];
          let defaultValue = type === 'link' ? '' : 'N/A';
          if (type === 'date' && value) {
            value = convertDateToStringFormat(value?.value);
          }
          if (type === 'badge' && value) {
            value = <BadgeInputComponent className="status-badge" color={value} badgeContent={value} onClick={() => {}} />;
          }
          return {
            label,
            value: value || defaultValue
          };
        })
      : [];
  };

  return (
    <MainCard sx={{ mt: 2 }} content={false}>
      <div className="download-csv">
        <Typography variant="h4" color="secondary">
          Materials
        </Typography>

        <Button variant="contained" onClick={downloadCSV}>
          Download CSV
        </Button>
      </div>

      <Stack spacing={1} mt={1}>
        {materials && materials?.length > 0 ? (
          materials?.map((material, index) => {
            const { description, partNumber, notes } = material || {};
            return (
              <div key={index} className="over-view-container">
                <Grid container spacing={1} alignItems="center">
                  <Grid item xs={12} sm={12} className="material-title-wrapper">
                    {description && (
                      <Typography variant="h5" color="secondary">
                        {description || ''}
                      </Typography>
                    )}
                    <div className="material-title">
                      <Typography variant="body2" fontWeight={600}>
                        Part Number :
                      </Typography>
                      <div className="material-part-number">
                        <Badge badgeContent="" color={badgeColor('Done')} className="part-number-badge" />
                        <span className="material-value"> {partNumber || 'N/A'}</span>
                      </div>
                    </div>
                  </Grid>
                </Grid>

                <Grid className="secondary-contatiner-material">
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Box className="secondary-container">{renderKeyValuePairs(labelValuePairs(material))}</Box>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            );
          })
        ) : (
          <p>No Materials Available</p>
        )}
      </Stack>
    </MainCard>
  );
};

export default MaterialRequestedCard;
