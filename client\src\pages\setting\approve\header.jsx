import { Divider, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import './approver.css'
import logo from '../../../assets/images/logo/logo.png';
import { capitalize } from 'lodash';
const Header = ({ approverName }) => {
  return (
    <>
      {' '}
      <MainCard className="supplier-portal-header" boxShadow={true}>
        <div className="header-wrapper">
          <img src={logo} alt="Company Logo" style={{ maxWidth: '100px', }} />

          <Typography variant="h5" color="secondary">
            {capitalize(approverName)}
          </Typography>
        </div>
        <Divider sx={{ my: 2, bgcolor: 'divider', }} />
      </MainCard>
    </>
  );
};
export default Header;
