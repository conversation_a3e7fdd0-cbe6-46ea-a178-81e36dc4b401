import React, { useEffect } from 'react';
import { <PERSON>po<PERSON>, Button, Container, Box, Paper, Grid, Divider, CardContent, CircularProgress } from '@mui/material';
import { useParams } from 'react-router-dom';
import { CheckCircleOutline } from '@mui/icons-material';
import Loader from 'components/Loader';
import { Person, LocationOn, Business, AccountCircle, CalendarToday } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import './supplierApprover.css';
import SupplierDetailTable from './supplierDetailTable';
import { getSingleSupplier, updateSupplierApprovalAction } from 'redux/reducers/supplierMaintenance';
import ThankyouMessage from '../thankyou';
import { APPROVED, REJECTED } from 'utils/constant';
import Header from '../header';
import EmailIcon from '@mui/icons-material/Email';
import LanguageIcon from '@mui/icons-material/Language';

const SupplierApprovalPage = () => {
  const { id: supplierId, token } = useParams();
  const supplierData = useSelector((state) => state.supplier);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getSingleSupplier(supplierId));
  }, [supplierId]);

  const handleApprove = async () => {
    try {
      dispatch(updateSupplierApprovalAction({ ID: supplierId, token, status: 'APPROVED' }))
    } catch (error) {

    }
  };

  const fields = [
    { label: 'Name', key: 'Name', icon: <Person /> },
    { label: 'Country', key: 'Country', icon: <LocationOn /> },
    { label: 'Shipping', key: 'Shipping', icon: <LocationOn /> },
    { label: 'Email', key: 'Email', icon: <EmailIcon /> },
    { label: 'Web', key: 'Web', type: 'link', icon: <LanguageIcon /> },
    { label: 'Created By', key: 'Created_By', icon: <AccountCircle /> },
    { label: 'Updated By', key: 'Updated_By_Name', icon: <AccountCircle /> ,show: 'Updated_By_Name',},
    { label: 'Created Date', key: 'Created_At', type: 'date', icon: <CalendarToday /> },
    { label: 'Updated Date', key: 'Updated_At', show: 'Updated_By_Name', type: 'date', icon: <CalendarToday /> },
  ];
  const { loading, singleSupplier } = supplierData || {}
  const { Status, approverName, lastUpdatedStatus } = singleSupplier || {}

  if (Status === REJECTED || Status === APPROVED) {
    return <ThankyouMessage status={lastUpdatedStatus} name="Supplier" color='success' />
  }

  return (<>
    <Header approverName={approverName || ''} />
    <Container maxWidth="md" >
      {loading && <Loader />}
      <Box textAlign="center">
        <CheckCircleOutline sx={{ fontSize: 80, color: 'green' }} />

        <Typography variant="h3" gutterBottom color="primary" sx={{ fontWeight: 'bold' }}>
          Approve Supplier
        </Typography>

        <Divider sx={{ my: 2, bgcolor: 'primary.main', height: 2 }} />

        <Typography variant="body1" gutterBottom color="textSecondary" sx={{ mb: 4 }}>
          You are about to approve this  supplier. Review the details below .
        </Typography>

        {singleSupplier && (
          <div style={{ marginBottom: '20px' }}>
            <Typography variant="h5" gutterBottom color="secondary" sx={{ mb: 3 }}>
              Supplier Details
            </Typography>
            <Grid container spacing={2} justifyContent="center">
              <SupplierDetailTable fields={fields} supplierData={singleSupplier} />
            </Grid>
          </div>
        )}

        <Button
          variant="contained"
          color="success"
          onClick={handleApprove}
          disabled={loading}
        >
          Confirm Approval
        </Button>
      </Box>
    </Container>
  </>
  );
};

export default SupplierApprovalPage;
