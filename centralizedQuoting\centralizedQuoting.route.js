const {
  addCentralizedRequest,
  getAllCentralizedRequests,
  bulkRequestSupplier,
  useQuote,
  addManualPrice,
  bulkRequestEmailSupplier,
  getAllEmailSupplierRequests,
  getSupplierReplies,
  updateEmailStatus,
} = require("./centralizedQuoting.controller");
const {
  addCentralizedRequestSchema,
  bulkRequestSupplierSchema,
  useQuoteSchema,
  getAllCentralizedRequestsSchema,
  addManualPriceSchema,
  bulkRequestEmailSupplierSchema,
  updateEmailRequestStatusSchema,
} = require("./centralizedQuoting.validation");

const router = require("express").Router();
const { validate } = require("../middlewares/validation.middleware");

router.post(
  "/add-centralized-request",
  validate(addCentralizedRequestSchema, "body"),
  addCentralizedRequest
);

router.patch(
  "/update-email-status",
  validate(updateEmailRequestStatusSchema, "body"),
  updateEmailStatus
);

router.post(
  "/get-all-centralized-requests",
  validate(getAllCentralizedRequestsSchema, "body"),
  getAllCentralizedRequests
);

router.post(
  "/bulk-request-supplier",
  validate(bulkRequestSupplierSchema, "body"),
  bulkRequestSupplier
);

router.post(
  "/bulk-request-email-supplier",
  validate(bulkRequestEmailSupplierSchema, "body"),
  bulkRequestEmailSupplier
);

router.get("/get-email-supplier-requests", getAllEmailSupplierRequests);

router.get("/get-supplier-replies/:RFQID", getSupplierReplies);

router.post("/use-quote", validate(useQuoteSchema, "body"), useQuote);

router.post(
  "/addManualPrice",
  validate(addManualPriceSchema, "body"),
  addManualPrice
);

module.exports = router;
