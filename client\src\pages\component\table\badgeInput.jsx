import React, { useState } from 'react';
import Badge from '@mui/material/Badge';
import { NOPRICEAVAILABLEQUOTE, REGISTEREDQUOTE, REQUESTEDQUOTE } from 'pages/offer/offerMaterial/constant';
import { EXPIREDQUOTE, PENDINGQUOTE, RECIEVEDQUOTE, RESPONDEDDQUOTE } from 'pages/monitorRfq/constant';
import { Typography } from '@mui/material';
import { APPROVED, KAM, REJECTED, SUPERVISER } from 'utils/constant';

const BadgeInputComponent = ({ badgeContent, color, onClick, id, className, btnBadge, width, disableBtn, onlyTextColour }) => {
  const [clicked, setClicked] = useState(false);

  const badgeColor = () => {
    if (color === 'won') return 'won';
    if (color === 'success') return 'success';
    if (color === 'primary') return 'primary';
    if (color === PENDINGQUOTE) return 'primary';
    if (color === REQUESTEDQUOTE) return 'warning';
    if (color === NOPRICEAVAILABLEQUOTE) return 'error';
    if (color === RECIEVEDQUOTE) return 'success';
    if (color === RESPONDEDDQUOTE) return 'success';
    if (color === EXPIREDQUOTE) return 'error';
    if (color === REJECTED) return 'error';
    if (color === APPROVED) return 'success';
    if (color === 'NO') return 'error';
    if (color === 'YES') return 'success';
    if (color === 'SENT') return 'primary';
    if (color === 'PENDING') return 'primary';
    if (color === 'REPLIED') return 'success';
    if (color === 'PROCESSED') return 'warning';
  };

  const textColor = () => {
    if (color === REQUESTEDQUOTE) return '#faad14';
    if (color === NOPRICEAVAILABLEQUOTE) return '#ff4d4f';
    if (color === RESPONDEDDQUOTE) return '#52c41a';
    if (color === REGISTEREDQUOTE) return '#1677ff';
    if (color === RECIEVEDQUOTE) return '#52c41a';
    if (color === EXPIREDQUOTE) return 'error';
    if (color === EXPIREDQUOTE) return 'error';
    return typeof color === 'string' && color;
  };

  const handleBadgeClick = () => {
    setClicked(!clicked);
    onClick(id);
  };

  return (
    <>
      {btnBadge ? (
        <button onClick={handleBadgeClick} className={`badge-btn ${className}`} disabled={disableBtn}>
          <Badge
            badgeContent={badgeContent}
            color={badgeColor()}
            sx={{
              '& .MuiBadge-badge': {
                fontSize: 10,
                padding: '10px',
                whiteSpace: 'nowrap'
              }
            }}
          />
        </button>
      ) : onlyTextColour ? (
        <Typography sx={{ color: textColor() }} color="error">
          {badgeContent === RECIEVEDQUOTE ? 'RECEIVED' : badgeContent}
        </Typography>
      ) : (
        <Badge
          badgeContent={badgeContent}
          color={badgeColor()}
          className={className}
          sx={{
            '& .MuiBadge-badge': {
              fontSize: 10,
              padding: '10px ',
              width: width,
              whiteSpace: 'nowrap'
            }
          }}
        />
      )}
    </>
  );
};

export default BadgeInputComponent;
