import React from 'react';
import { Card, CardContent, Grid, Typography } from '@mui/material';

const MyOfferCard = ({ title, content, Icon, iconBg ,padding}) => {
  const iconStyle = {
    backgroundColor: iconBg,
    display: 'flex',
    justifyContent: 'center',
padding:padding
  };

  return (
    <Grid>
      <Card sx={{backgroundColor:'#f0f0f0'}}>
        <CardContent className='offer-card-content'>
          <Grid display="flex" justifyContent="center">
            <Grid className="card-icon-container" style={iconStyle}>
              <Icon className="card-icon" />
            </Grid>
          </Grid>
          <Grid textAlign="center">
            <Typography variant="body2" color='secondary' >
              {content}
            </Typography>
          </Grid>
          <Grid textAlign="center">
            <Typography variant="h5" component="Grid" color='secondary'>
               {title}
            </Typography>
          </Grid>
        </CardContent>
      </Card>
    </Grid>
  );
};

export default MyOfferCard;
