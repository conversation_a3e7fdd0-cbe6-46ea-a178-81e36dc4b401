import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { deleletRequest, getRequest, post, putRequest } from 'utils/axios';
import {
  updateTimeSettingApiUrl,
  getSettingApiUrl,
  updateApproverSettingApiUrl,
  getTimeSlotApiUrl,
  addTimeSlotSettingApiUrl,
  addDashboardApiUrl,
  updateDashboardApiUrl,
  deleteDashboardApiUrl,
  updateLegalTermsApiUrl,
  getLegalTermsApiUrl
} from 'utils/constant';
import { notification } from 'utils/helper';

export const getSetting = createAsyncThunk(getSettingApiUrl, async (data, { dispatch }) => {
  try {
    const response = await getRequest(getSettingApiUrl, data, true, dispatch);
    if (response) {
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        return data;
      }
      if (!success) {
        return {};
      }
    }
  } catch (error) {
    return {};
  }
});
export const getTimeSlots = createAsyncThunk('getTimeSlots/fetch', async (supplierId, { rejectWithValue, dispatch }) => {
  const { setLoading, setTimeSlot } = settingSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await post(getTimeSlotApiUrl(supplierId), supplierId, true, dispatch);

    if (response) {
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(setLoading(false));
        dispatch(setTimeSlot(data || []));
        return { success, data: data };
      }
      if (!success) {
        dispatch(setTimeSlot([]));
        dispatch(setLoading(false));
        return { success: false, data: [], message };
      }
    }
  } catch (error) {
    dispatch(setTimeSlot([]));
    dispatch(setLoading(false));
    return { success: false, data: [] };
  }
});

export const updateSetting = createAsyncThunk(updateTimeSettingApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  const { navigate, ...rest } = data || {};
  const payload = { ...rest };

  const { setLoading } = settingSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await post(updateTimeSettingApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, false));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});
export const addTimeSlotAction = createAsyncThunk(addTimeSlotSettingApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;

  const { setLoading } = settingSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await post(addTimeSlotSettingApiUrl, data, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        dispatch(notification(success, message, false));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

export const updateApproverConfigAction = createAsyncThunk(
  'updateApproverConfigAction/fetch',
  async (approverEmail, { rejectWithValue, dispatch }) => {
    const { setLoading } = settingSlice.actions || {};
    try {
      dispatch(setLoading(true));

      const response = await post(updateApproverSettingApiUrl(approverEmail), '', true, dispatch);

      if (response) {
        dispatch(setLoading(false));
        const { error, success, message, data } = get(response, 'data', {});

        if (success) {
          dispatch(notification(true, message, false));
          return { success: true, data };
        }

        if (!success) {
          dispatch(notification(false, message, true));
          return { success: false, data: [] };
        }
      } else {
        dispatch(setLoading(false));
      }
    } catch (error) {
      dispatch(setLoading(false));
      return rejectWithValue({ success: false, data: [] });
    }
  }
);

export const addDashboardAction = createAsyncThunk(addDashboardApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;

  const { setLoading } = settingSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await post(addDashboardApiUrl, data, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        dispatch(notification(success, message, false));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    console.error('Error in addDashboard thunk:', error);
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});
export const updateDashboardAction = createAsyncThunk(updateDashboardApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;

  const { setLoading } = settingSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await putRequest(updateDashboardApiUrl, data, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        dispatch(notification(success, message, false));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    console.error('Error in addDashboard thunk:', error);
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});
export const deleteDashboardAction = createAsyncThunk('deleteDashboar/delete', async (dashboardID, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;

  const { setLoading } = settingSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await deleletRequest(deleteDashboardApiUrl(dashboardID), dashboardID, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        dispatch(getSetting());
        dispatch(notification(success, message, false));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    console.error('Error in addDashboard thunk:', error);
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

export const getLegalTerms = createAsyncThunk('getLegalTerms/fetch', async (language, { dispatch, rejectWithValue }) => {
  const { setLoading } = settingSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await getRequest(getLegalTermsApiUrl(language), {}, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        return { success: true, data, language };
      } else {
        dispatch(notification(false, message, true));
        return { success: false, data: null };
      }
    }
  } catch (error) {
    console.error('Error fetching legal terms:', error);
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: null });
  }
});

export const updateLegalTerms = createAsyncThunk('updateLegalTerms/update', async (data, { dispatch, rejectWithValue }) => {
  const { setLoading } = settingSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await post(updateLegalTermsApiUrl, data, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data: responseData } = get(response, 'data', {});

      if (success) {
        dispatch(notification(true, message, false));
        return { success: true, data: responseData, language: data.Language };
      } else {
        dispatch(notification(false, message, true));
        return { success: false, data: null };
      }
    }
  } catch (error) {
    console.error('Error updating legal terms:', error);
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: null });
  }
});

const settingSlice = createSlice({
  name: 'setting',
  initialState: {
    data: {},
    status: 'idle',
    error: null,
    loading: false,
    legalTerms: {
      EN: '',
      ES: ''
    }
  },
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setTimeSlot: (state, action) => {
      state.timeSlot = action.payload;
    },
    setLegalTerms: (state, action) => {
      const { language, content } = action.payload;
      state.legalTerms[language] = content;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSetting.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getSetting.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload;
      })
      .addCase(getSetting.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })
      .addCase(getLegalTerms.fulfilled, (state, action) => {
        if (action.payload.success) {
          const { language, data } = action.payload;
          state.legalTerms[language] = data || '';
        }
      })
      .addCase(updateLegalTerms.fulfilled, (state, action) => {
        if (action.payload.success) {
          const { language, data } = action.payload;
          state.legalTerms[language] = data || '';
        }
      });
  }
});
export const { setLoading, setTimeSlot, setLegalTerms } = settingSlice.actions;
export default settingSlice.reducer;
