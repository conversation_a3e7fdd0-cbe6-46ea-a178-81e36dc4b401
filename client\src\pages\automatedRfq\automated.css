.secondary-contatiner {
    display: flex;
    margin-top: 10px;
    margin-left:10px
  }
  .material-dates {
    margin-left: 25px;
  }
  .material-icon {
    /* width: 100px !important; */
  }
  .material-card {
    display: flex;
    border: 1px solid #dedbdb;
    border-radius: 8px;
    margin: 10px 0;
    box-shadow: rgba(0, 0, 0, 0.08) 0px 0px 0px;
    align-items: flex-start;
  }
  .company-image{
    width: 100px;
    height: 100px;
    object-fit: contain;
  }
  .material-request-btn {
    width: 100px;
  }
  .clear-filters-btn {
    cursor: pointer;
    color: var(--primary-color);
    font-size: 12px;
    font-weight: 500;
    border: none;
    background-color: transparent;
    padding: 4px 6px;
    border-radius: 4px;
  }
  .clear-filters-btn:hover {
    background-color: #dcf0ef;
  }
  .option-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  
  .pagination-container {
    list-style: none;
    text-align: center;
    padding-top: 24px;
    justify-content: flex-end;
    gap: 4px;
    display: flex;
    align-items: center;
    color: var(--primary-color);
  }
  .pagination-container .pagination-paginationMeta {
    color: var(--primary-color);
    font-size: 14px;
    float: left;
    margin-top: 6px;
    margin-right: 10px;
    cursor: default;
    font-weight: 400;
  }
  .pagination-container li {
    display: inline-block;
    margin-right: 9px;
    font-weight: 700;
    cursor: pointer;
    padding: 6px 8px;
    border-radius: 2px;
  }
  .pagination-container .pagination-prev {
    height: 42px;
    border-radius: 4px;
    border: 1px solid #d4d5d9;
    text-align: center;
    margin-right: 64px;
    padding: 10px 22px;
  }
  .pagination-container .pagination-number {
    background-color: #fff;
    border: 1px solid transparent;
  }
  .pagination-container .pagination-dots {
    margin-left: 5px;
    margin-right: 5px;
  }
  .pagination-container .pagination-next {
    height: 42px;
    border-radius: 4px;
    border: 1px solid #d4d5d9;
    text-align: center;
    margin-left: 64px;
    padding: 10px 22px;
  }
  .pagination-container button {
    background-color: unset;
    border: unset;
    color: #636363;
  }
  .pagination-container button.page-number {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-content: center;
    justify-content: center;
  }
  .pagination-container button.page-number.active {
    background-color: rgba(22, 119, 255, 0.9);
    color: #fff;
  }
  .active {
    /* background-color: var(--primary-color); */
    color: #fff;
    border-color: var(--primary-color);
  }
  .pagination-prev-link {
    color: var(--primary-color);
  }
  .pagination-prev a {
    color: var(--primary-color);
  }
  .pagination-next a {
    color: var(--primary-color);
  }
  .pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .pagination button {
    border: 1px solid #ddd;
    background-color: #fff;
    cursor: pointer;
  }
  .pagination button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  .pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
  }
  .search-btn {
    width: 100%;
  }
  .material-dialogbox {
    display: flex;
  }
  .link {
    text-decoration: none;
    color: inherit; 
  }
  
  
  .link:hover {
    text-decoration: none; /* Removes underline on hover */
    color: inherit; /* Keeps the text color as it is */
  }
  .category-filter-container {
    padding: 20px;
    border-radius: 10px;
    border: 0.5px solid #dedbdb;
  }
  .filter-section {
    margin-bottom: 12px;
  }
  
  .filter-input {
    margin-top: 10px;
  }
  .filter-input .MuiFormControl-root, 
  .search-field {
    width: 100%;
  }
  .filter-title{
    margin-bottom: 20px;
    font-weight: 500;
  }
  
  .add-rfq-delivery-date{
    width: 100%;
  }
  .add-rfq-deadline-date{
    width: 100%;
  }
  
  .search-part-Number{
    display: flex;
    
  }
  .automated-status-badge {
    padding-left: 30px;
    margin-left: 45px;
  }