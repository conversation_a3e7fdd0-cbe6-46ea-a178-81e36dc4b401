import { Box, Typography } from "@mui/material"
import DynamicAutocomplete from "pages/component/autoComplete"
import SelectComponent from "pages/component/selectComponent"
import PageTitle from "pages/pageTitle"
import { useState } from "react"


const SelectSupplier = ({ setSelectedValue, selectedValue, supplierList }) => {

  const handleOptionChange = (event, newValue) => {
    setSelectedValue({ ...selectedValue, supplier: newValue })
  };
  return (
    <>
      <Box className="main-container">
        <PageTitle title='Price Maintenance' />
        <Box className="inner-container">
          <Typography
            variant="h5"
            color="secondary"
            className="header"
          >
            Select Supplier
          </Typography>

          <div className='auto-complete'>
            <DynamicAutocomplete
              options={supplierList}
              value={selectedValue?.supplier}
              onChange={handleOptionChange}
              getOptionLabel={(option) => option.label}
            />
          </div>
        </Box>
      </Box>

    </>
  )
}
export default SelectSupplier