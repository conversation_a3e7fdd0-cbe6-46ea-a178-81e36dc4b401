/* eslint-disable no-unused-vars */
import Grid from '@mui/material/Grid';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { get } from 'lodash';
// import { fetchSupplierList } from 'redux/reducers/supplierPortalReducer';
import { Card, CardContent, Divider, IconButton, Typography } from '@mui/material';
import TableComponent from 'pages/component/table/table';
import ActionButton from 'pages/component/actionButton';
import { useNavigate } from 'react-router';
import { addUserPageUrl, COUNTRY_MANAGER, COUNTRY_MANAGER_LABEL, updateUserPageUrl } from 'utils/constant';
import Loader from 'components/Loader';
import { deleteUserAction, getAllUserAction } from 'redux/reducers/userReducer';
import AlertDialog from 'pages/component/dialogbox';
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined';
import AddEditClient from 'pages/clients/addEditClient';
import { tabsData } from './constant';
import { getUserDetail } from 'utils/auth';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';

export default function Users() {
  const [users, setUsers] = useState([]);
  const [allUser, setAllUsers] = useState([]);
  const userList = useSelector((state) => state.users);
  const [open, setOpen] = useState(false);
  const [userId, setUserId] = useState('');

  const dispatch = useDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    const currentUser = getUserDetail();
    const usersRows = get(userList, 'data', []).map((item) => {
      const { FirstName, Lastname, Email, UserID, Role } = item || {};
      return {
        name: `${FirstName || ''} ${Lastname || ''}`.trim(),
        id: UserID,
        email: Email,
        role: Role === COUNTRY_MANAGER ? COUNTRY_MANAGER_LABEL : Role,
        isNotCurrentUser: currentUser?.userId !== UserID
      };
    });

    setUsers(usersRows);
    setAllUsers(usersRows);
  }, [userList?.data]);

  useEffect(() => {
    dispatch(getAllUserAction());
  }, []);

  const headers = [
    {
      name: 'id',
      type: 'RFQ Id',
      title: 'User ID',
      sortingactive: true,
      minWidth: '150px'
    },
    {
      name: 'name',
      type: 'text',
      title: 'Name',
      sortingactive: true,
      maxWidth: '100px'
    },

    {
      name: 'email',
      type: 'email',
      title: 'Email',
      sortingactive: true
    },
    {
      name: 'role',
      type: 'text',
      title: 'Role',
      sortingactive: true
    },

    {
      name: 'actions',
      btnType: 'multipleButton',
      type: 'actions',
      btnName: 'use',
      title: 'Action',
      sortingactive: false,
      minWidth: '100px',

      component: ActionButton,

      multipleButtons: [
        {
          type: 'icon',
          icon: <EditOutlinedIcon fontSize="16px" />,
          buttonOnClick: (type, rowData) => {
            navigate(`${updateUserPageUrl}/${rowData?.id}`);
          },
          color: 'primary',
          tooltip: 'Edit'
        },
        {
          icon: <DeleteOutlineOutlinedIcon fontSize="16px" />,
          type: 'icon',
          buttonOnClick: (type, rowData) => {
            setUserId(rowData?.id);
            setOpen(true);
          },
          color: 'error',
          tooltip: 'Delete',
          showButton: 'isNotCurrentUser'
        }
      ]
    }
  ];

  const handleSearch = (filteredRows, searchValue) => {
    setUsers(filteredRows);
  };

  const actionBtns = [{ title: 'Add User', onClick: () => navigate(addUserPageUrl) }];
  const isLoading = () => {
    return userList?.loading || userList?.status === 'loading';
  };

  const handleClose = () => {
    setOpen(false);
    setUserId('');
  };
  const handleDelete = async () => {
    const response = await dispatch(deleteUserAction(userId));
    const { success } = get(response, 'payload', {});
    if (success) {
      handleClose();
    }
  };

  const buttons = [
    { label: 'Cancel', onClick: () => handleClose(), variant: 'outlined', color: 'error' },
    { label: 'Yes', onClick: () => handleDelete(), variant: 'contained', color: 'primary' }
  ];

  return (
    <Grid container spacing={2}>
      <AlertDialog
        Component={
          <Typography variant="body1" color="secondary">
            Are you sure you want to delete this User
          </Typography>
        }
        open={open}
        showCard={false}
        borderRadius="20px"
        buttons={buttons}
      />
      {isLoading() && <Loader />}
      <Grid item xs={12}>
        <div>
          <Card>
            <Divider />

            <CardContent sx={{ width: '100%', overflow: 'hidden' }}>
              <TableComponent
                maxHeight={'100%'}
                enablePagination={true}
                columns={headers}
                rows={users || []}
                enableSearch={true}
                handleSearch={(data, searchValue) => handleSearch(data, searchValue)}
                allRows={allUser || []}
                actionBtns={actionBtns}
                placeActionButtonsIn="search"
                isBadgeFilter={true}
                badgeFilterData={tabsData || []}
                showDeleteIcon={true}
              />
            </CardContent>
          </Card>
        </div>
      </Grid>
    </Grid>
  );
}
