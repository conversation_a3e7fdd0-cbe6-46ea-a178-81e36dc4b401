import Loader from 'components/Loader';
import React, { useEffect, useState } from 'react';

const Dashboard1 = () => {
  const [height, setHeight] = useState(window.innerHeight);
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const handleResize = () => {
      setHeight(window.innerHeight);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleLoad = () => {
    setLoading(false)
  }

  return (
    <>
      {loading && <Loader />}
      <iframe
        width="100%"
        height={height}
        src="https://lookerstudio.google.com/embed/reporting/a6553388-2890-46ca-8f7a-6f655231b752/page/p_fccmjp5uid"
        frameBorder="0"
        style={{ border: 0 }}
        allowFullScreen
        onLoad={handleLoad}
        sandbox="allow-storage-access-by-user-activation allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
      ></iframe>
    </>
  );
};

export default Dashboard1;
