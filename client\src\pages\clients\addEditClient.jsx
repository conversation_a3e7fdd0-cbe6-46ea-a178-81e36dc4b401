import React, { useEffect, useState } from 'react';
import { Button, Grid, Typography, Box } from '@mui/material';
import ErrorMessage from 'pages/component/errorMessage';
import MainCard from 'components/MainCard';
import InputField from 'pages/component/inputField';
import SelectComponent from 'pages/component/selectComponent';
import TextAreaComponent from 'pages/component/textArea';
import { clientPageUrl, getStateList } from 'utils/constant';
import { useDispatch, useSelector } from 'react-redux';
import { addClientMaintenanace, getSingleClient, setSingleClientData, updateClientMaintenanace } from 'redux/reducers/clientReducer';
import Loader from 'components/Loader';
import { validate } from 'pages/component/validation';
import { useNavigate, useParams } from 'react-router';
import countriesList from '../supplier/countriesList';
import { getCountriesAction, getRegionsAction, getStatesAction, setStateAction } from 'redux/reducers/countryReducer';
import { isArray, isEmpty } from 'lodash';
import { ConsoleSqlOutlined } from '@ant-design/icons';
import DynamicAutocomplete from 'pages/component/autoComplete';

// Define styles
const styles = {
  formContainer: {
    padding: '16px'
  },
  card: {
    margin: '16px 0'
  },
  buttonGroup: {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '10px'
  },
  inputField: {
    width: '100%'
  },
  textarea: {
    width: '100%',
    padding: '4px'
  },
  errorText: {
    color: 'red',
    marginTop: '4px'
  }
};

const initialState = {
  clientId: '',
  name: '',
  municipality: '',
  businessActivity: '',
  address: ''
};

const AddEditClient = () => {
  const [countries, setCountries] = useState([]);
  const [regionList, setRegionList] = useState([]);
  const [states, setStates] = useState([]);
  const clientData = useSelector((state) => state.client);
  const countriesData = useSelector((state) => state.countries);
  const [clientDetails, setClientDetails] = useState(initialState);
  const [errors, setErrors] = useState({});
  const clientID = useParams()?.id;
  const dispatch = useDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    dispatch(getCountriesAction());
    dispatch(getRegionsAction());
  }, []);

  useEffect(() => {
    setCountries(countriesData?.countries?.countries?.map((country) => ({ label: country?.Country, value: country?.CountryId })) || []);
  }, [countriesData?.countries]);

  useEffect(() => {
    if (countriesData?.regions?.regions?.length > 0) {
      setRegionList(countriesData?.regions?.regions?.map((region) => ({ label: region, value: region })) || []);
    } else {
      setRegionList([]);
    }
  }, [countriesData?.regions]);

  useEffect(() => {
    if (clientDetails?.country) {
      setStates(countriesData?.states?.states?.map((state) => ({ label: state?.State, value: state?.StateId })) || []);
    } else {
      setStates([]);
    }
  }, [countriesData?.states]);

  const fieldConfigurations = [
    { type: 'text', name: 'clientId', placeholder: 'Enter client ID', label: 'Client ID', showInput: clientID ? false : true },
    { type: 'text', name: 'name', placeholder: 'Enter client name', label: 'Client Name', showInput: true },
    {
      type: 'text',
      name: 'municipality',
      placeholder: 'Enter Municipality/Suburb/City',
      label: ' Municipality/Suburb/City',
      showInput: true
    },
    { type: 'text', name: 'businessActivity', placeholder: 'Enter legal business activity', label: 'Business Activity', showInput: true },
    {
      type: 'textarea',
      name: 'address',
      placeholder: 'Enter client Street number and name',
      label: 'Street number and name',
      rows: 2,
      showInput: true
    },
    { type: 'subHeading', label: 'Office', showInput: true },
    {
      type: 'autocomplete',
      label: 'Country',
      name: 'country',
      placeholder: 'Select Country',
      items: countries || [],
      showInput: true
    },
    {
      type: 'autocomplete',
      label: 'State',
      name: 'state',
      placeholder: 'Select State',
      items: states || [],
      showInput: true
    },
    {
      type: 'text',
      label: 'Post Code',
      name: 'postalCode',
      placeholder: 'Enter Postal Code',
      showInput: true
    },
    { type: 'subHeading', label: 'Shipping', showInput: true },
    {
      type: 'autocomplete',
      label: 'Region',
      name: 'region',
      placeholder: 'Select Region',
      items: regionList || [],
      showInput: true
    }
  ];

  useEffect(() => {
    if (clientID) {
      dispatch(getSingleClient(clientID));
    }
  }, [clientID]);

  useEffect(() => {
    if (clientID && clientData?.singleClient) {
      const {
        Name: name,
        Address: address,
        BusinessActivity: businessActivity,
        Municipality: municipality,
        State_1: state,
        Country_1: country,
        Region: region,
        Postal_Code: postalCode,
        ClientID: clientId
      } = clientData.singleClient || {};
      if (country) {
        dispatch(getStatesAction(country));
      }

      setClientDetails({
        clientId,
        name,
        state,
        country,
        postalCode,
        address,
        region,
        businessActivity,
        municipality
      });
    } else {
      setClientDetails(initialState);
    }
  }, [clientData?.singleClient]);

  const handleChange = (name, value) => {
    setClientDetails({ ...clientDetails, [name]: value });
    setErrors({ ...errors, [name]: '' });
  };

  const handleSelectChange = (name, value) => {
    if (name === 'country') {
      setClientDetails({ ...clientDetails, [name]: value, state: '' });
      setErrors({ ...errors, [name]: '' });
      dispatch(getStatesAction(value));
    } else {
      setClientDetails({ ...clientDetails, [name]: value });
      setErrors({ ...errors, [name]: '' });
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const rules = {
      clientId: { required: true, label: 'Client Id', noSpaces: true },
      name: { required: true, label: 'Name' },
      country: { required: true, label: 'Country' },
      state: { required: true, label: 'State' },
      postalCode: { required: true, label: 'Postal Code' },
      region: { required: true, label: 'Region' }
    };
    const validation = validate(clientDetails, rules);
    setErrors(validation);

    if (!Object.keys(validation).length) {
      const { name, clientId, municipality, businessActivity, address, region } = clientDetails || {};
      const payload = {
        Name: name,
        Address: address,
        State: clientDetails?.state,
        Country: clientDetails?.country,
        Postal_Code: clientDetails?.postalCode,
        BusinessActivity: businessActivity,
        Municipality: municipality,
        ClientID: clientId,
        Region: region,
        navigate: navigate
      };
      if (clientID) {
        dispatch(updateClientMaintenanace(payload));
      } else {
        dispatch(addClientMaintenanace(payload));
      }
    }
  };

  const handleAutocompleteChange = (name) => (event, newValue) => {
    const value = newValue ? newValue.value : '';
    handleSelectChange(name, value);
  };

  const renderField = (fieldConfig) => {
    const { type, name, placeholder, label, value, rows, items, showInput } = fieldConfig || {};

    if (!showInput) return null;
    switch (type) {
      case 'text':
      case 'email':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="subtitle1" gutterBottom>
              {label}
            </Typography>
            <InputField
              type={type}
              name={name}
              placeholder={placeholder}
              value={clientDetails[name] || ''}
              onChange={(e) => handleChange(name, e?.target?.value)}
              errors={errors}
              style={styles.inputField}
              fullWidth
            />
          </Grid>
        );

      case 'select':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="subtitle1" gutterBottom>
              {label}
            </Typography>
            <SelectComponent
              name={name}
              onSelectChange={(e) => handleSelectChange(name, e?.target?.value)}
              value={clientDetails[name] || 'placeholder'}
              items={items}
              placeholder={placeholder}
              error={errors[name]}
              style={{ color: !clientDetails[name] && 'rgb(168, 166, 166)' }}
            />
            {errors[name] && <ErrorMessage message={errors[name]} style={styles.errorText} />}
          </Grid>
        );

      case 'autocomplete':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="subtitle1" gutterBottom>
              {label}
            </Typography>
            <DynamicAutocomplete
              options={items || []}
              // label={label}
              placeholder={placeholder}
              value={items?.find((item) => item.value === clientDetails[name]) || null}
              onChange={handleAutocompleteChange(name)}
              getOptionLabel={(option) => option?.label || ''}
              isLoading={name === 'country' ? countriesData?.loading : name === 'state' ? countriesData?.statesLoading : false}
            />
            {errors[name] && <ErrorMessage message={errors[name]} style={styles.errorText} />}
          </Grid>
        );

      case 'textarea':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="subtitle1" gutterBottom>
              {label}
            </Typography>
            <TextAreaComponent
              name={name}
              placeholder={placeholder}
              value={clientDetails[name] || ''}
              onChange={(name, e) => handleChange(name, e?.target?.value)}
              minRows={rows}
              className="name-text-field"
              style={styles.textarea}
              error={errors[name]}
            />
            {errors[name] && <ErrorMessage message={errors[name]} style={styles.errorText} />}
          </Grid>
        );

      case 'subHeading':
        return (
          <Grid item xs={12} key={name}>
            <Box>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  display: 'inline-block',
                  color: 'primary.main'
                }}
              >
                {label}
              </Typography>
            </Box>
          </Grid>
        );

      default:
        return null;
    }
  };

  const cancel = () => {
    dispatch(setSingleClientData({}));
    navigate(clientPageUrl);
  };
  const { loading } = clientData || {};
  return (
    <Grid>
      {(loading || countriesData?.loading) && <Loader />}
      <Box component="form" onSubmit={handleSubmit} style={styles.formContainer}>
        <MainCard className="maincard-boder" boxShadow={true} style={styles.card}>
          <Typography variant="h4" component="h1" gutterBottom color="secondary">
            {clientID ? 'Update Client' : 'New Client'}
          </Typography>
          <Grid container spacing={3} mt={1}>
            {fieldConfigurations.map((config) => renderField(config))}
          </Grid>
        </MainCard>

        <MainCard sx={{ mt: 3 }} className="maincard-boder" boxShadow={true} style={styles.card}>
          <Grid item style={styles.buttonGroup}>
            <Button variant="outlined" color="secondary" onClick={() => cancel()}>
              Cancel
            </Button>
            <Button variant="contained" color="primary" type="submit" disabled={loading}>
              Save
            </Button>
          </Grid>
        </MainCard>
      </Box>
    </Grid>
  );
};

export default AddEditClient;
