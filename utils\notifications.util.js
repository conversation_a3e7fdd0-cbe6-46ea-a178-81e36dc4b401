const {
  DATASET_ID_SCRAPE,
  DATASET_ID_MAIN,
  DATASET_ID_LANDINGZONE,
} = require("../constants");
const {
  mainDataset,
  bigQueryClient,
  scrapedDataset,
  landingZoneDataset,
} = require("../db");
const {
  setDate,
  isEmpty,
  generateID,
  getCurrentDateTime,
} = require("./misc.util");

/*
notification = {
sender : null
message : "The RFQ (RFQ ID) was assigned to you, please check."
link : "link to the RFQ"
date : setDate()
priority : "MEDIUM"
}
*/

async function sendNotification({
  userID,
  message = null,
  priority = "MEDIUM",
  sender = "REMIEX",
  link = null,
  RFQID,
}) {
  let notification = {};
  notification.ID = generateID();
  notification.sender = sender;
  notification.message = message;
  notification.link = link;
  notification.date = getCurrentDateTime();
  notification.priority = priority;
  notification.RFQID = RFQID;
  notification.read = false;

  try {
    if (isEmpty(message) || isEmpty(userID)) {
      return console.error(`Error : Message or UserID is missing`);
    }

    const [userNotifications] = await mainDataset.query({
      query: `
        SELECT *
        FROM notifications
        WHERE userID = @userID
      `,
      params: {
        userID: userID.toString(),
      },
    });

    if (isEmpty(userNotifications[0])) {
      let notifications = [];

      notifications.push(notification);

      let notificationsString = JSON.stringify(notifications);

      const [sendNotification] = await mainDataset.query({
        query: `
          INSERT INTO notifications
          (userID, messages, read)
          VALUES
          (@userID, @messages, @read)
        `,
        params: {
          userID: userID.toString(),
          messages: notificationsString,
          read: false,
        },
      });
    } else {
      let existingNotifications = JSON.parse(userNotifications[0].messages);
      existingNotifications.push(notification);
      let existingNotificationsString = JSON.stringify(existingNotifications);
      const [sendNotification] = await mainDataset.query({
        query: `
          UPDATE notifications SET 
          messages = @messages, read = @read
          WHERE userID = @userID
        `,
        params: {
          userID: userID.toString(),
          messages: existingNotificationsString,
          read: false,
        },
      });
    }
  } catch (error) {
    // console.log(`Error occurred while sending notification  - ${error}`);
  }
}

module.exports = {
  sendNotification,
};
