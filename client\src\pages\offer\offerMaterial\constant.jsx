import ActionButton from 'pages/component/actionButton';
import OfferMaterialActionButton from './offerMaterialActionButton';
import CalculateMargin from './calculateMargin/calculateMargin';
import { isCountryManager } from 'utils/helper';
export const REGISTEREDQUOTE = 'REGISTERED';
export const RECIEVEDQUOTE = 'RECIEVED';
export const REQUESTEDQUOTE = 'REQUESTED';
export const NOPRICEAVAILABLEQUOTE = 'NOPRICE';

export const offerMaterialTableColumn = [
  {
    name: 'date',
    type: 'date',
    title: 'Date',
    sortingactive: true
  },
  {
    name: 'supplierName',
    type: 'text',
    title: 'Supplier',
    hide: isCountryManager(),
    sortingactive: true,
    minWidth: '170px'
  },
  {
    name: 'unitPrice',
    type: 'text',
    title: 'Unit Cost',
    defaultValue: '0',
    minWidth: '100px',
    sortingactive: true
  },
  {
    name: 'quantity',
    type: 'text',
    title: 'Quantity',
    defaultValue: '0',
    sortingactive: true
  },
  {
    name: 'weight',
    type: 'text',
    title: 'Weight',
    defaultValue: '0',
    sortingactive: true
  },

  {
    name: 'sh',
    type: 'text',
    title: 'Shipping & Handling',
    minWidth: '200px',
    defaultValue: '0',
    sortingactive: true
  },
  {
    name: 'totalCost',
    type: 'text',
    title: 'Total Cost',
    defaultValue: '0',
    minWidth: '150px',
    sortingactive: true
  },
  {
    name: 'currency',
    type: 'text',
    title: 'Currency',
    sortingactive: true
  },
  {
    name: 'leadTime',
    type: 'text',
    title: 'Lead Time',
    minWidth: '120px',
    defaultValue: '0',
    sortingactive: true
  },
  {
    name: 'estimatedOfferCost',
    type: 'text',
    title: 'Est. Offer (USD)',
    defaultValue: '0',
    sortingactive: true,
    minWidth: '150px'
  },
  {
    name: 'unitOfferPrice',
    type: 'text',
    minWidth: '130px',
    title: 'Est. Unit Offer price (USD)',
    defaultValue: '0',
    sortingactive: true
  },
  {
    name: 'status',
    type: 'badge',
    minWidth: '200px',
    title: 'Status',
    onlyTextColour: true
  }
  // {
  //   name: 'calculatedMargin',
  //   type: 'text',
  //   minWidth: '200px',
  //   title: 'Calculated Margin',
  // }
];

export const getColumnAction = (loader, getSingleSupplier, deleteSupplier, selectSupplier, showNotes, setMarginDetail, ClientID) => {
  const columnActions = [
    {
      name: 'actions',
      btnType: 'checkbox',
      type: 'actions',
      checkboxName: 'offer',
      title: 'Offer',
      sortingactive: false,
      disableBtn: loader || false,
      isDisable: 'isDisabled',
      component: OfferMaterialActionButton,
      buttonOnClick: (type, supplierId, materialId) => {
        selectSupplier(materialId, supplierId);
      }
    },
    // {
    //   name: 'actions',
    //   btnType: 'update',
    //   type: 'actions',
    //   minWidth: '200px',
    //   buttonTitle: 'Calculate',
    //   title: 'Calculate Margin',
    //   sortingactive: false,
    //   hideDeleteBtn: true,
    //   component: CalculateMargin,
    //   disableBtn: loader || false,
    //   buttonOnClick: (type, id, materialId, index, data) => {
    //     setMarginDetail({ ...data });
    //   }
    // },
    {
      name: 'actions',
      btnType: 'update',
      type: 'actions',
      minWidth: '200px',
      title: 'Action',
      sortingactive: false,
      component: OfferMaterialActionButton,
      disableBtn: loader || false,
      buttonOnClick: (type, id, materialId, index, data) => {
        if (type === 'edit' || type === 'use') {
          getSingleSupplier(materialId, id, type);
        } else if (type === 'delete' || type === 'deleteUse') {
          deleteSupplier(materialId, id, type, data);
        }
      }
    },
    {
      name: 'actions',
      btnType: 'pdf',
      type: 'actions',
      title: '',
      sortingactive: false,
      component: OfferMaterialActionButton,
      disableBtn: loader || false,
      buttonOnClick: (type, id, materialId, index, data) => {
        if (type === 'pdf') {
          showNotes(data);
        }
      }
    }
  ];
  return columnActions;
};
