import { Button, Grid, Typography } from '@mui/material';
import { get } from 'lodash';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { convertDateToStringFormat } from 'utils/helper';
import { ARRAY, DATE, RADIO, SEARCH, SELECT, SLIDER, TEXT } from './component/constant';
import DateFilter from './component/dateFilter';
import FilterCheckbox from './component/filterCheckbox';
import TextFilter from './component/textfilter';
import FilterRadioButtons from './component/filterRadio';
import FilterSelect from './component/filterSelect';
import FilterSlider from './component/filterSlider';
import TextAutocomplete from './component/textAutoComplete';
const MaterialFilter = ({ filterMaterialData, handleFilter, loading, label }) => {
  const [filterData, setFilteredData] = useState([]);
  useEffect(() => {
    setFilteredData(filterMaterialData);
  }, [filterMaterialData]);

  const handleFilterChange = (value, index, type) => {
    let currentValue = {};
    let updatedSelect = {};
    let updatedFilterData = [];
    let updatedData = [];
    let val = '';
    switch (type) {
      case ARRAY:
        currentValue = filterData[index];
        updatedSelect = get(currentValue, 'select', []);
        updatedData = get(currentValue, 'data', [])?.map((item) => {
          if (item?.value === value) {
            return { ...item, select: !item?.select };
          } else {
            return item;
          }
        });
        updatedFilterData = [...filterData];
        updatedFilterData[index] = {
          ...currentValue,
          data: updatedData
        };
        setFilteredData(updatedFilterData);
        break;

      case TEXT:
        val = get(value, 'target.value', '');
        currentValue = filterData[index];
        updatedFilterData = [...filterData];
        updatedFilterData[index] = {
          ...currentValue,
          data: val
        };
        setFilteredData(updatedFilterData);
        break;

      case DATE:
        val = convertDateToStringFormat(new Date(value || null), true);
        currentValue = filterData[index];
        updatedFilterData = [...filterData];
        updatedFilterData[index] = {
          ...currentValue,
          data: val
        };
        setFilteredData(updatedFilterData);
        break;

      case RADIO:
        val = value;
        currentValue = filterData[index];
        updatedFilterData = [...filterData];
        updatedFilterData[index] = {
          ...currentValue,
          data: val
        };
        setFilteredData(updatedFilterData);
        break;

      case SELECT:
        val = get(value, 'target.value', value);
        currentValue = filterData[index];
        updatedFilterData = [...filterData];
        updatedFilterData[index] = {
          ...currentValue,
          data: val
        };
        setFilteredData(updatedFilterData);
        break;

      case SLIDER:
        val = value;
        currentValue = filterData[index];
        updatedFilterData = [...filterData];
        updatedFilterData[index] = {
          ...currentValue,
          data: val
        };
        setFilteredData(updatedFilterData);
        break;

      case SEARCH:
        val = get(value, 'target.value', '');
        currentValue = filterData[index];
        updatedFilterData = [...filterData];
        updatedFilterData[index] = {
          ...currentValue,
          data: val
        };
        setFilteredData(updatedFilterData);
        break;

      default:
        break;
    }
  };
  const clearFilters = () => {
    const updatedFilterData = filterData.map((filter) => {
      if (filter.type === ARRAY) {
        const updatedData = filter?.data?.map((item) => ({ ...item, select: false }));
        return { ...filter, data: updatedData };
      }
      if (filter.type === DATE) {
        return { ...filter, data: '' };
      }
      if (filter.type === TEXT || filter.type === SELECT || filter.type === SEARCH) {
        return { ...filter, data: '' };
      }
      if (filter.type === RADIO) {
        return { ...filter, data: 'noFilter' };
      }
      if (filter.type === SLIDER) {
        return { ...filter, data: 0 };
      }
      return filter;
    });
    handleFilter([]);
    setFilteredData(updatedFilterData);
  };
  const clearFilter = () => {
    const updatedFilterData = filterData.map((filter) => {
      if (filter.type === ARRAY) {
        const updatedData = filter.data.map((item) => ({ ...item, select: false }));
        return { ...filter, data: updatedData };
      }
      if (filter.type === SLIDER) {
        return { ...filter, data: 0 };
      }
      return filter;
    });
    setFilteredData(updatedFilterData);
  };

  const searchRfq = () => {
    handleFilter(filterData);
  };
  const isClearFiltersVisible = () => {
    return filterData.some((filter) => {
      switch (filter?.type) {
        case ARRAY:
          return filter?.data?.some((item) => item.select);
        case DATE:
        case TEXT:
        case SELECT:
        case SEARCH:
          return filter.data !== '' && filter?.data !== null;
        case RADIO:
          return filter.data !== '' && filter?.data !== null && filter?.data !== 'noFilter';
        case SLIDER:
          return filter.data !== '' && filter?.data !== null && filter?.data !== 0;
        default:
          return false;
      }
    });
  };

  return (
    <div className="category-filter-container">
      <Grid item xs={12} display="flex" justifyContent="space-between">
        <Typography variant="body1" fontSize="20px" className="filter-title" color="secondary">
          {label || 'RFQ Information'}
        </Typography>
        {isClearFiltersVisible() && (
          <Button className="clear-filters-btn" onClick={clearFilters} disabled={loading}>
            Clear Filters
          </Button>
        )}
      </Grid>
      {filterData?.length > 0 &&
        filterData?.map((filter, index) => (
          <div key={index} className="filter-section">
            {(() => {
              let data = get(filter, 'data');
              switch (get(filter, 'type')) {
                case ARRAY:
                  return (
                    data?.length > 0 && (
                      <>
                        <div className="option-container">
                          {get(filter, 'heading') && (
                            <Typography variant="h6" fontWeight={500} color="secondary">
                              {get(filter, 'heading')}
                            </Typography>
                          )}

                          {filter?.showClearBtn && (
                            <button className="clear-filters-btn" onClick={clearFilter}>
                              Clear
                            </button>
                          )}
                        </div>
                        <FilterCheckbox data={filter} handleFilterChange={(e) => handleFilterChange(e, index, get(filter, 'type'))} />
                      </>
                    )
                  );
                case TEXT:
                  return (
                    <>
                      <Typography variant="subTitle1" color="secondary">
                        {get(filter, 'heading')}
                      </Typography>
                      <TextFilter data={filter} handleFilterChange={(e) => handleFilterChange(e, index, get(filter, 'type'))} />
                    </>
                  );
                case DATE:
                  return (
                    <>
                      <Typography variant="subTitle1" color="secondary">
                        {get(filter, 'heading')}
                      </Typography>
                      <DateFilter
                        data={filter}
                        handleFilterChange={(e) => handleFilterChange(e, index, get(filter, 'type'))}
                        label={get(filter, 'heading')}
                      />
                    </>
                  );
                case RADIO:
                  return (
                    <>
                      <Typography variant="subTitle1" color="secondary">
                        {get(filter, 'heading')}
                      </Typography>
                      <FilterRadioButtons
                        data={filter}
                        handleFilterChange={(e) => handleFilterChange(e, index, get(filter, 'type'))}
                        label={get(filter, 'heading')}
                      />
                    </>
                  );
                case SELECT:
                  if (filter.options && filter.options.length > 0) {
                    return (
                      <>
                        <Typography variant="subTitle1" color="secondary">
                          {get(filter, 'heading')}
                        </Typography>
                        <TextAutocomplete data={filter} handleFilterChange={(e) => handleFilterChange(e, index, get(filter, 'type'))} />
                      </>
                    );
                  }
                  return (
                    <>
                      <Typography variant="subTitle1" color="secondary">
                        {get(filter, 'heading')}
                      </Typography>
                      <FilterSelect
                        data={filter}
                        handleFilterChange={(e) => handleFilterChange(e, index, get(filter, 'type'))}
                        label={get(filter, 'heading')}
                      />
                    </>
                  );
                case SLIDER:
                  return (
                    <>
                      <Typography variant="subTitle1" color="secondary">
                        {get(filter, 'heading')}
                      </Typography>
                      <FilterSlider
                        data={filter}
                        handleFilterChange={(e) => handleFilterChange(e, index, get(filter, 'type'))}
                        label={get(filter, 'heading')}
                      />
                    </>
                  );
                default:
                  return null;
              }
            })()}
          </div>
        ))}
      <div className="filter-section">
        <div className="option-container">
          <Button variant="contained" className="search-btn" onClick={searchRfq} disabled={loading}>
            Search
          </Button>
        </div>
      </div>
    </div>
  );
};
MaterialFilter.propTypes = {
  handleFilter: PropTypes.func,
  filterCategoryData: PropTypes.array
};

export default MaterialFilter;
