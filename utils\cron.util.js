const { <PERSON>ronJ<PERSON> } = require("cron");
const {
  scrapedDataset,
  mainDataset,
  bigQueryClient,
  automationDataset,
} = require("../db");
const {
  setDate,
  generateID,
  isEmpty,
  pickAutomatedRFQ,
  fetchRFQInformation,
  generateRequestSupplierPayload,
  filterReadyToQuoteMaterials,
  RequestMaterialToSupplier,
  processAutomatedRFQs,
  reserveAutomatedRFQ,
  checkReplies,
} = require("./misc.util");
const {
  DATASET_ID_SCRAPE,
  DATASET_ID_MAIN,
  BASE_URL,
  DATASET_ID_AUTOMATION,
} = require("../constants");
const { sendMail } = require("../utils/email.util");
const { sendNotification } = require("../utils/notifications.util");
const {
  automatedCRONStartedTemplate,
  automatedCRONEndedTemplate,
  supplierMaterialRequestTemplate,
} = require("../utils/templates");

const updateRFQStatusCron = async () => {
  new CronJob(
    "0 21 * * *", // At 09:00 PM (Santiago)
    async () => {
      const [existingRFQs] = await bigQueryClient.query({
        query: `
          SELECT * 
          FROM ${DATASET_ID_SCRAPE}.RFQ AS r
          LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status AS rs 
          ON r.RFQ_ID = rs.RFQID
          WHERE Deadline < CURRENT_DATE()
          AND (
          rs.CurrentStatus IN ("NEW", "IRRELEVANT") OR rs.CurrentStatus IS NULL
          OR 
          ( rs.AssignedUser IS NOT NULL AND (rs.CurrentStatus = "RESERVED" OR rs.CurrentStatus = "QUOTING"))
          );
        `,
      });
      if (!isEmpty(existingRFQs)) {
        for (const rfq of existingRFQs) {
          const { RFQ_ID } = rfq;
          const { AssignedUser, CurrentStatus } = rfq;

          let statusToExpire = ["NEW", "IRRELEVANT"];
          if (isEmpty(CurrentStatus)) {
            await mainDataset.query({
              query: `
                INSERT INTO RFQ_Status (RFQID, CurrentStatus, StatusChangeDate, timestamp_date)
                VALUES (@rfqId, @status, @date, CURRENT_TIMESTAMP())
              `,
              params: {
                rfqId: parseInt(RFQ_ID),
                status: "EXPIRED",
                date: setDate(),
              },
            });
          } else if (
            !isEmpty(AssignedUser) &&
            (CurrentStatus === "RESERVED" || CurrentStatus === "QUOTING")
          ) {
            await mainDataset.query({
              query: `
                UPDATE RFQ_Status
                SET CurrentStatus = @status, StatusChangeDate=CURRENT_DATE()
                WHERE RFQID = @rfqId
              `,
              params: {
                rfqId: parseInt(RFQ_ID),
                status: "FORGOTTEN",
              },
            });
          } else if (statusToExpire.includes(CurrentStatus)) {
            await mainDataset.query({
              query: `
                UPDATE RFQ_Status
                SET CurrentStatus = @status, StatusChangeDate=CURRENT_DATE()
                WHERE RFQID = @rfqId
              `,
              params: {
                rfqId: parseInt(RFQ_ID),
                status: "EXPIRED",
              },
            });
          }
        }
      }
    },
    null,
    true,
    "America/Santiago"
  );
};

const updateNoRFQStatusCron = async () => {
  new CronJob(
    "16 */1 * * *", // Every 1 hours after 1 minute
    async () => {
      const [noStatusRFQ] = await bigQueryClient.query({
        query: `
        SELECT * 
        FROM ${DATASET_ID_SCRAPE}.RFQ as r
        LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status as rs ON r.RFQ_ID = rs.RFQID
        WHERE r.Deadline > CURRENT_DATE() AND r.isManuallyAdded is NULL AND (rs.CurrentStatus not in ("NEW", "RESERVED", "QUOTING", "ANSWERED", "FORGOTTEN", "WON", "LOST", "EXPIRED", "IRRELEVANT", "VOID") OR rs.CurrentStatus is NULL)
        `,
      });

      for (const rfq of noStatusRFQ) {
        const { RFQ_ID } = rfq;
        if (!isEmpty(RFQ_ID)) {
          await mainDataset.query({
            query: `
            INSERT INTO RFQ_Status (RFQID, CurrentStatus, StatusChangeDate, timestamp_date)
            VALUES (@rfqId, @status, @date, CURRENT_TIMESTAMP())
            `,
            params: {
              rfqId: RFQ_ID,
              status: "NEW",
              date: setDate(),
            },
          });
        }
      }
    },
    null,
    true,
    "America/Santiago"
  );
};

const mailSuppliersCron = async () => {
  new CronJob(
    "0 19 * * *", // At 07:00 PM (Santiago)
    async () => {
      const [existingSupplierRequests] = await mainDataset.query({
        query: `
          SELECT *
          FROM Supplier_Requests
          WHERE status="PENDING"
        `,
      });

      if (!isEmpty(existingSupplierRequests)) {
        for (const supplier of existingSupplierRequests) {
          const [updateSupplierRequests] = await mainDataset.query({
            query: `
              UPDATE Supplier_Requests SET
              status=@status, requestDate=@date
              WHERE ID=@ID
            `,
            params: {
              status: "REQUESTED",
              ID: supplier.ID,
              date: setDate(),
            },
          });

          const name = supplier.supplierName;
          const email = supplier.supplierEmail;
          const ID = supplier.ID.toString();
          const supplierID = supplier.supplierID.toString();

          const link = `${BASE_URL}/supplier-portal/${ID}`;

          const data = {
            name,
            link,
          };

          let option = {
            to: email,
            subject: supplierMaterialRequestTemplate.emailSubject,
            html: supplierMaterialRequestTemplate.htmlTemplate(data),
          };

          try {
            await sendMail(option);
          } catch (error) {
            console.log("Error Occured While Sending Mail - ", error);
          }
        }
      }
    },
    null,
    true,
    "America/Santiago"
  );
};

const expireRequestCron = async () => {
  new CronJob(
    "0 20 * * *", // At 08:00 PM (Santiago)
    async () => {
      const [SupplierRequests] = await mainDataset.query({
        query: `
          UPDATE Supplier_Requests
          SET status = "EXPIRED"
          WHERE status="REQUESTED" AND requestDate <= DATE_SUB(CURRENT_DATE(), INTERVAL 5 DAY) AND respondDate is NULL
        `,
      });
    },
    null,
    true,
    "America/Santiago"
  );
};

const lostOfferCron = async () => {
  new CronJob(
    "0 22 * * *", // At 10:00 PM (Santiago)
    async () => {
      const [lostOffers] = await mainDataset.query({
        query: `
          SELECT rs.RFQID, rs.AssignedUser
          FROM RFQ_Status AS rs 
          LEFT JOIN Offers AS o ON o.RFQID = rs.RFQID
          WHERE rs.CurrentStatus = "ANSWERED" AND o.status NOT IN ("WON", "LOST")
          AND o.OfferDate <= DATE_SUB(CURRENT_DATE(), INTERVAL 45 DAY)
          ORDER BY rs.RFQID ASC
          LIMIT 10;
        `,
      });

      if (!isEmpty(lostOffers)) {
        const groupedOffers = lostOffers.reduce((acc, offer) => {
          if (!acc[offer.AssignedUser]) {
            acc[offer.AssignedUser] = [];
          }
          acc[offer.AssignedUser].push(offer.RFQID);
          return acc;
        }, {});

        const userNotifications = Object.keys(groupedOffers).map((user) => ({
          AssignedUser: user,
          RFQIDs: groupedOffers[user],
        }));

        for (const user of userNotifications) {
          let message = `The RFQ - ${user.RFQIDs.join(
            ", "
          )} have been marked as LOST`;

          await sendNotification({
            userID: user.AssignedUser,
            message: message,
          });
        }

        const [updateLostOffers] = await mainDataset.query({
          query: `
                  UPDATE Offers AS o 
                  SET o.status = 'LOST'
                  WHERE o.RFQID IN (
                                    SELECT rs.RFQID
                                    FROM RFQ_Status AS rs
                                    WHERE rs.CurrentStatus = 'ANSWERED'
                                    ORDER BY o.RFQID ASC
                  )
                  AND o.status NOT IN ("WON", "LOST")
                  AND o.OfferDate <= DATE_SUB(CURRENT_DATE(), INTERVAL 45 DAY)
          `,
        });

        const [updateLostRFQs] = await mainDataset.query({
          query: `
                  UPDATE RFQ_Status AS rs SET
                  rs.CurrentStatus = 'LOST' 
                  WHERE rs.RFQID IN (
                                    SELECT o.RFQID FROM Offers AS o WHERE o.status NOT IN ("WON")
                                    AND 
                                    o.OfferDate <= DATE_SUB(CURRENT_DATE(), INTERVAL 45 DAY)
                                    ORDER BY o.RFQID ASC
                  )
                  AND rs.CurrentStatus = "ANSWERED"
          `,
        });
      }
    },
    null,
    true,
    "America/Santiago"
  );
};

const reserveAutomatedRFQCron = async () => {
  new CronJob(
    "30 */2 * * *", // Every 1 hours after 1 minute
    async () => {
      await reserveAutomatedRFQ();
    },
    null,
    true,
    "America/Santiago"
  );
};

const automatedRFQRequest = async () => {
  new CronJob(
    "0 2,11 * * *",
    async () => {
      const cronStartedOption = {
        to: "<EMAIL>",
        subject: automatedCRONStartedTemplate.emailSubject(),
        html: automatedCRONStartedTemplate.htmlTemplate(),
      };
      await sendMail(cronStartedOption);

      const RFQIDs = await pickAutomatedRFQ();
      await reserveAutomatedRFQ();
      await processAutomatedRFQs(RFQIDs);

      const cronEndedOption = {
        to: "<EMAIL>",
        subject: automatedCRONEndedTemplate.emailSubject(),
        html: automatedCRONEndedTemplate.htmlTemplate(),
      };
      await sendMail(cronEndedOption);
    },
    null,
    true,
    "America/Santiago"
  );
};

const updateRepliedRFQRequestStatusCron = async () => {
  new CronJob(
    "*/30 * * * *", // Every Half Hour
    async () => {
      try {
        const replies = await checkReplies();
        if (!isEmpty(replies)) {
          const repliedRFQIDs = Array.from(
            new Set(replies?.map((reply) => reply.RFQID))
          );

          const [existingEmailSupplierRequests] = await mainDataset.query({
            query: `
              UPDATE ${DATASET_ID_MAIN}.Email_Supplier_Requests SET
              Email_Status = @emailStatus, Email_Response_Timestamp = @emailResponseTimestamp
              WHERE RFQID IN UNNEST(@repliedRFQIDs);
        
            `,
            params: {
              repliedRFQIDs,
              emailStatus: "REPLIED",
              emailResponseTimestamp: new Date(),
            },
          });
        }
      } catch (error) {
        console.log("error", error);
      }
    },
    null,
    true,
    "America/Santiago"
  );
};

module.exports = {
  updateRFQStatusCron,
  updateNoRFQStatusCron,
  mailSuppliersCron,
  expireRequestCron,
  lostOfferCron,
  reserveAutomatedRFQCron,
  automatedRFQRequest,
  updateRepliedRFQRequestStatusCron,
};
