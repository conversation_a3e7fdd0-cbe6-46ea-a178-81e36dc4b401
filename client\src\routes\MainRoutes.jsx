import { lazy } from 'react';

import Loadable from 'components/Loadable';
import Dashboard from 'layout/Dashboard';
import {
  addClientPageUrl,
  addEmailPricesRequestsPageUrl,
  addRfqPageUrl,
  addSupplierUrl,
  addUserPageUrl,
  automatedRfqPageUrl,
  availableAddEditRfq,
  centralizedPageUrl,
  clientPageUrl,
  dashboardPageUrl,
  editProfilePageUrl,
  emailPricesRequestsPageUrl,
  emailResponsesPageUrl,
  forgotPasswordPageUrl,
  historyPageUrl,
  monitorRfqPageUrl,
  myOfferPageUrl,
  priceMaitenancePageUrl,
  priceTablePageUrl,
  reserveAddEditRfq,
  reservedHistoryPageUrl,
  ReservedSingleRfqPageUrl,
  reserveRfqPageUrl,
  resetPasswordPageUrl,
  rfqListPageUrl,
  settingPageUrl,
  showAllNotificationPageUrl,
  singleRequestedQuotePageUrl,
  singleRfqPageUrl,
  SUPERVISER,
  supplierUrl,
  updateClientPageUrl,
  updateGenerateOfferPageUrl,
  updateSupplierPageUrl,
  updateUserPageUrl,
  userPageUrl,
  viewOfferPageUrl
} from 'utils/constant';
import { Navigate } from 'react-router';
import { getUserDetail } from 'utils/auth';
import Setting from 'pages/setting';
import PriceTable from 'pages/centralizedQuotes/priceTable';
import EmailPriceRequest from 'pages/emailPriceRequest';
import AddEmailPriceRequest from 'pages/emailPriceRequest/addEmailPriceRequest';
import EmailResponse from 'pages/emailPriceRequest/emailResponse';
const DashboardDefault = Loadable(lazy(() => import('pages/availableRfq/index')));
const ReservedRFQ = Loadable(lazy(() => import('pages/availableRfq/reservedRfq')));
const AutomatedRFQ = Loadable(lazy(() => import('pages/automatedRfq/automatedRfq')));
const SingleRfq = Loadable(lazy(() => import('pages/singlrRfq/index')));
const PriceMaitenance = Loadable(lazy(() => import('pages/priceMaintainence')));
const ReserveSingleRfq = Loadable(lazy(() => import('pages/singlrRfq/reserveSingleRfq')));
const Vovier = Loadable(lazy(() => import('pages/vovier/index')));
const Offer = Loadable(lazy(() => import('pages/offer/index')));
const MyOffers = Loadable(lazy(() => import('pages/myOffers/index')));
const GenerateOffer = Loadable(lazy(() => import('pages/generateOfferDetail/index')));
const PageNotFound = Loadable(lazy(() => import('pages/pageNotFound/index')));
const AvailableAddEditRfq = Loadable(lazy(() => import('pages/availableRfq/addEditRfq/availableAddEditRfq')));
const ReserveAddEditRfq = Loadable(lazy(() => import('pages/availableRfq/addEditRfq/reserveAddEditRfq')));
const MonitorQuotes = Loadable(lazy(() => import('pages/monitorRfq')));
const SingleRequestedQuote = Loadable(lazy(() => import('pages/monitorRfq/singleRequestedQuote')));
const ShowAllNotification = Loadable(lazy(() => import('layout/Dashboard/Header/HeaderContent/showAllNotifications')));
const Clients = Loadable(lazy(() => import('pages/clients')));
const Supplier = Loadable(lazy(() => import('pages/supplier')));
const AddEditSupplier = Loadable(lazy(() => import('pages/supplier/addEditSupplier')));
const AddEditClient = Loadable(lazy(() => import('pages/clients/addEditClient')));
const ViewOffer = Loadable(lazy(() => import('../pages/viewOffer')));
const Users = Loadable(lazy(() => import('pages/users')));
const AddEditUser = Loadable(lazy(() => import('pages/users/addEditUser')));
const EditProfile = Loadable(lazy(() => import('layout/Dashboard/Header/HeaderContent/Profile/editProfile')));
const ForgotPassword = Loadable(lazy(() => import('layout/Dashboard/Header/HeaderContent/Profile/forgotPassword')));
const ResetPassword = Loadable(lazy(() => import('layout/Dashboard/Header/HeaderContent/Profile/resetpassword')));
const CentralizedQuotes = Loadable(lazy(() => import('pages/centralizedQuotes')));
const DynamicDashboards = Loadable(lazy(() => import('pages/dashboard')));

// ==============================|| MAIN ROUTING ||============================== //
const routesConfig = [
  {
    path: '/',
    element: <DashboardDefault />
  },
  {
    path: '/404',
    element: <PageNotFound />
  },
  {
    path: '*',
    element: <Navigate to={rfqListPageUrl} />
  },
  {
    path: rfqListPageUrl,
    element: <DashboardDefault />
  },
  {
    path: `${ReservedSingleRfqPageUrl}/:id`,
    element: <ReserveSingleRfq />
  },
  {
    path: `${dashboardPageUrl}/:id`,
    element: <DynamicDashboards />
  },
  {
    path: `${singleRfqPageUrl}/:id`,
    element: <SingleRfq />
  },
  // {
  //   path: `${offerSingleRfqPageUrl}/:id`,
  //   element: <OfferSingleRfq />
  // },
  {
    path: `${historyPageUrl}/:id/:rfqId`,
    element: <Vovier />
  },
  {
    path: `${reservedHistoryPageUrl}/:id/:rfqId`,
    element: <Vovier />
  },
  {
    path: supplierUrl,
    element: <Supplier />
  },
  {
    path: addSupplierUrl,
    element: <AddEditSupplier />
  },
  {
    path: emailPricesRequestsPageUrl,
    element: <EmailPriceRequest />
  },
  {
    path: `${emailResponsesPageUrl}/:id`,
    element: <EmailResponse />
  },
  {
    path: addEmailPricesRequestsPageUrl,
    element: <AddEmailPriceRequest />
  },
  {
    path: `${updateSupplierPageUrl}/:id`,
    element: <AddEditSupplier />
  },
  {
    path: clientPageUrl,
    element: <Clients />
  },
  {
    path: addClientPageUrl,
    element: <AddEditClient />
  },
  {
    path: `${updateClientPageUrl}/:id`,
    element: <AddEditClient />
  },
  {
    path: '/offer/:id',
    element: <Offer />
  },
  {
    path: '/generate-offer/:id',
    element: <GenerateOffer />
  },

  {
    path: `${updateGenerateOfferPageUrl}/:id/:offerId`,
    element: <GenerateOffer />
  },
  {
    path: `${viewOfferPageUrl}/:id/:offerId`,
    element: <ViewOffer />
  },
  {
    path: myOfferPageUrl,
    element: <MyOffers />
  },
  {
    path: availableAddEditRfq,
    element: <AvailableAddEditRfq />
  },
  {
    path: `${availableAddEditRfq}/:id`,
    element: <AvailableAddEditRfq />
  },
  {
    path: `${reserveAddEditRfq}/:id`,
    element: <ReserveAddEditRfq />
  },
  {
    path: reserveRfqPageUrl,
    element: <ReservedRFQ />
  },
  {
    path: automatedRfqPageUrl,
    element: <AutomatedRFQ />
  },
  {
    path: monitorRfqPageUrl,
    element: <MonitorQuotes />
  },
  {
    path: `${singleRequestedQuotePageUrl}/:id`,
    element: <SingleRequestedQuote />
  },
  {
    path: showAllNotificationPageUrl,
    element: <ShowAllNotification />
  },
  {
    path: priceMaitenancePageUrl,
    element: <PriceMaitenance />
  },
  {
    path: forgotPasswordPageUrl,
    element: <ForgotPassword />
  },
  {
    path: `${resetPasswordPageUrl}/:id`,
    element: <ResetPassword />
  },

  {
    path: editProfilePageUrl,
    element: <EditProfile />
  },
  {
    path: userPageUrl,
    element: <Users />
  },
  {
    path: addUserPageUrl,
    element: <AddEditUser />
  }
];

export const supervisorRoutes = [
  {
    path: `${updateUserPageUrl}/:id`,
    element: <AddEditUser />
  },
  {
    path: settingPageUrl,
    element: <Setting />
  },
  {
    path: centralizedPageUrl,
    element: <CentralizedQuotes />
  },
  {
    path: `${priceTablePageUrl}/:supplierIds`,
    element: <PriceTable />
  },
  {
    path: `${centralizedPageUrl}/:data`,
    element: <CentralizedQuotes />
  }
];

const MainRoutes = {
  path: '/',
  element: <Dashboard />,
  children: routesConfig
};

export default MainRoutes;
