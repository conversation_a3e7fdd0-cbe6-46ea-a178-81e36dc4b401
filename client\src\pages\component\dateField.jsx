/* eslint-disable no-empty */
import TextField from '@mui/material/TextField';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs from 'dayjs';

export default function DatePickerComponent({
  startDateHandleChange,
  startDate,
  classname,
  label,
  error,
  disablePastDate = false, // default to false
}) {
  let dayjsDate = null;

  if (startDate !== null && startDate !== undefined) {
    try {
      const parsedDate = dayjs(startDate);
      if (parsedDate.isValid()) {
        dayjsDate = parsedDate;
      }
    } catch (e) {}
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <div className={classname}>
        <DatePicker
          label={label || ''}
          value={dayjsDate || null}
          onChange={(newValue) => {
            startDateHandleChange(newValue);
          }}
          disablePast={disablePastDate} // ⛔ disable past if true
          slotProps={{
            textField: {
              error,
              fullWidth: true,
            },
          }}
          sx={{
            width: '100%',
            borderRadius: '8px',
            '& .MuiOutlinedInput-root': {
              borderRadius: '8px',
              width: '100%',
              '& fieldset': {
                borderColor: error ? '#ff4d4f' : '',
              },
              '&:hover fieldset': {
                borderColor: error ? '#ff4d4f' : '',
              },
              '&.Mui-focused fieldset': {
                borderColor: error ? '#ff4d4f' : '',
              },
            },
          }}
        />
      </div>
    </LocalizationProvider>
  );
}
