import { Badge, Button, Grid, Modal, Stack, Typography, Card, CardActions, CardContent, CardMedia, Box } from '@mui/material';
import MainCard from 'components/MainCard';
import { get } from 'lodash';
import { Link } from 'react-router-dom';
import { badgeColor } from 'utils/helper';
import Confirm from './buttons/confirm';
import ConvertHtml from './convertHtmlContent';
import PDFList from 'pages/myOffers/myOfferTable/pdfList';
import { fetchAutomationInfo, googleLink } from 'utils/constant';
import { useDispatch } from 'react-redux';
import { useEffect, useState } from 'react';
import DynamicTabs from 'pages/component/tabs';
import { post } from 'utils/axios';
import pdfImg from '../../assets/images/pdfimg.png';
import './singlrRfq.css';
import Benchmark from 'pages/offer/offerMaterial/benchMark';

const Materials = ({ materials, rfqStatus, showConfirmBtn, historyPageUrl, rfqId, loading }) => {
  return (
    <MainCard sx={{ mt: 2 }} content={false}>
      <Typography variant="h4" color="secondary">
        Materials
      </Typography>
      <Stack spacing={1} mt={1}>
        {materials && materials?.length > 0 ? (
          materials?.map((material, index) => (
            <div key={index} className="over-view-container">
              <Grid container spacing={1} alignItems="center">
                <Grid item xs={12} sm={6} sx={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
                  <Typography variant="h5" color="secondary">
                    {get(material, 'Material_Description', '')}
                  </Typography>
                  <Benchmark material={material} disableBtn={loading} />
                </Grid>

                <Grid item xs={12} sm={6} sx={{ textAlign: 'right' }}>
                  <div>
                    <div className="history-btn">
                      {material?.history && (
                        <div>
                          <Link to={`${historyPageUrl}/${material?.Material_ID}/${rfqId}`} className="link">
                            <Button disabled={loading} variant="outlined" className="material-request-btn">
                              History
                            </Button>
                          </Link>
                        </div>
                      )}
                      <div style={{ marginTop: '10px' }}>
                        <Link to={`${googleLink}${material?.Part_Number}`} className="link" target="_blank" rel="noopener noreferrer">
                          <Button variant="outlined" disabled={loading}>
                            Search Part Number
                          </Button>
                        </Link>
                      </div>
                      {material?.specsSheetAvailable && <PDFList isSingleRfqPage={true} material={material} loading={loading} />}
                    </div>
                  </div>
                </Grid>
              </Grid>

              <Grid className="secondary-contatiner">
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                  <Typography variant="body2" fontWeight={600}>
                    Part Number :{' '}
                  </Typography>

                  <div style={{ display: 'inline-flex', justifyContent: 'center', alignItems: 'center' }}>
                    <Badge badgeContent="" color={badgeColor(material?.ConfirmationStatus)} sx={{ marginLeft: 3, marginRight: 2 }} />

                    <span className="material-value"> {get(material, 'Part_Number', 'N/A') || 'N/A'}</span>
                  </div>
                </div>

                <Typography variant="body2" className="material-brand" fontWeight={600}>
                  Brand :<span className="material-value"> {get(material, 'Brand', 'N/A') || 'N/A'}</span>
                </Typography>

                <div className="material-brand">
                  {showConfirmBtn && <Confirm variant="outlined" order={material} rfqStatus={rfqStatus} loading={loading} />}
                </div>
              </Grid>

              <Grid className="secondary-contatiner" sx={{ mb: 2, mt: 1 }}>
                <Typography variant="body2" fontWeight={600}>
                  Quantity Requested <span className="material-value"> {get(material, 'Quantity_Required', '')}</span>
                </Typography>
              </Grid>

              <Grid className="secondary-contatiner">
                <ConvertHtml content={get(material, 'Notes', '')} />
              </Grid>
            </div>
          ))
        ) : (
          <p>No Materials Available</p>
        )}
      </Stack>
    </MainCard>
  );
};

export default Materials;
