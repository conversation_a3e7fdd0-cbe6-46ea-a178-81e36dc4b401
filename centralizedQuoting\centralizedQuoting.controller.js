const {
  mainDataset,
  landingZoneDataset,
  scrapedDataset,
  bigQueryClient,
} = require("../db");
const {
  DATASET_ID_MAIN,
  DATASET_ID_SCRAPE,
  JWT_SECRET,
  CUSTOMS_TAX,
} = require("../constants");
const { apiHandler, apiError, apiResponse } = require("../utils/api.util");
const {
  NOT_FOUND,
  FETCH,
  ADD_SUCCESS,
  SUCCESS,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  EXISTS,
  CUSTOM_ERROR,
  SENT_SUCCESS,
  INVALID,
  CUSTOM_SUCCESS,
  STATUS_SUCCESS,
} = require("../utils/message.util");
const {
  isEmpty,
  setDate,
  generateID,
  round,
  checkReplies,
} = require("../utils/misc.util");
const { sendNotification } = require("../utils/notifications.util");
const { sendMail } = require("../utils/email.util");
const { logger } = require("@google-cloud/bigquery/build/src/logger");
const {
  emailSupplierRequestTemplate,
  emailSpanishSupplierRequestTemplate,
} = require("../utils/templates");

const addCentralizedRequest = apiHandler(async (req, res) => {
  const { Notes, RFQ_ID, Material_ID } = req.body;

  const KAM = req.user.userId.toString();
  const date = setDate();

  const [existingMaterial] = await bigQueryClient.query({
    query: `
    SELECT pc.ConfirmedBrand as brand, pc.ConfirmedPartNumber as partNumber, Quantity_Required as quantity, Material_Description, RFQ_Number, RFQ_Name, RFQ_Date, Deadline, Portal, URL, Delivery_Date  
    FROM ${DATASET_ID_SCRAPE}.Material as m 
    LEFT JOIN ${DATASET_ID_SCRAPE}.RFQ as r ON r.RFQ_ID = m.RFQ_ID
    LEFT JOIN ${DATASET_ID_MAIN}.Predictions_Confirmations as pc ON pc.MaterialID = m.Material_ID
    WHERE TRIM(CAST(m.Material_ID as String)) = @Material_ID`,
    params: {
      Material_ID: Material_ID.trim(),
    },
  });

  if (isEmpty(existingMaterial)) {
    return apiError(NOT_FOUND, "Material", null, res);
  }

  const [existingCentralizedRequest] = await mainDataset.query({
    query: `SELECT RFQ_ID FROM Centralized_Requests WHERE Brand = @Brand AND PartNumber = @PartNumber`,
    params: {
      Brand: existingMaterial[0].brand.toUpperCase().trim(),
      PartNumber: existingMaterial[0].partNumber.trim(),
    },
  });

  if (!isEmpty(existingCentralizedRequest)) {
    return apiError(
      EXISTS,
      "Centralized Request with this brand and Part Number",
      null,
      res
    );
  }

  const Material_Information = {
    ...existingMaterial[0],
  };

  const [newCentralizedRequest] = await mainDataset.query({
    query: `INSERT INTO Centralized_Requests
    (Notes, RFQ_ID, Material_Information, KAM, date, ID, Brand, PartNumber, Material_ID)
    VALUES
    (@Notes, @RFQ_ID, @Material_Information, @KAM, @date, @ID, @Brand, @PartNumber, @Material_ID)`,
    params: {
      Notes: Notes || "",
      RFQ_ID,
      Material_ID,
      Material_Information: JSON.stringify(Material_Information),
      KAM,
      date,
      ID: generateID().toString(),
      Brand: existingMaterial[0].brand.toUpperCase().trim(),
      PartNumber: existingMaterial[0].partNumber.trim(),
    },
  });

  return apiResponse(ADD_SUCCESS, "Centralized Request", null, res);
});

const getAllCentralizedRequests = apiHandler(async (req, res) => {
  let [centralizedRequests] = await bigQueryClient.query({
    query: `
SELECT DISTINCT 
    cr.ID,
    cr.*,
    sr.materialRequested,
    sr.supplierID,
    sr.supplierName,
    u.FirstName,
    u.LastName,
    sr.respondDate,
    sr.requestDate,
    sp.recommended,
    sp.supplierID AS recommendedSupplier,
    r.RFQ_Number
FROM 
    ${DATASET_ID_MAIN}.Centralized_Requests AS cr
LEFT JOIN 
    ${DATASET_ID_MAIN}.Suppliers_Brand AS sb 
    ON TRIM(UPPER(cr.Brand)) = TRIM(UPPER(sb.Brand))
LEFT JOIN 
    ${DATASET_ID_MAIN}.Supplier_Requests AS sr 
    ON CAST(sr.supplierID AS STRING) = CAST(sb.SupplierID AS STRING)
LEFT JOIN 
    ${DATASET_ID_MAIN}.Users AS u 
    ON CAST(u.UserID AS STRING) = CAST(cr.KAM AS STRING)
LEFT JOIN 
    ${DATASET_ID_MAIN}.supplier_price AS sp
    ON CAST(sp.supplierID AS STRING) = CAST(sb.SupplierID AS STRING) 
      AND sp.brand = sb.Brand 
      AND sp.partNumber = cr.PartNumber 
      AND sp.recommended = TRUE
LEFT JOIN 
    ${DATASET_ID_MAIN}.Offers AS o 
    ON CAST(o.RFQID AS STRING) = cr.RFQ_ID 
LEFT JOIN 
    ${DATASET_ID_SCRAPE}.RFQ AS r
    ON CAST(r.RFQ_ID AS STRING) = cr.RFQ_ID 
WHERE 
    o.OfferID IS NULL;
    `,
  });

  if (isEmpty(centralizedRequests)) {
    return apiResponse(NOT_FOUND, "Centralized Requests", null, res);
  }

  const IDs = new Set();
  let CentralizedRequests = [];

  for (const request of centralizedRequests) {
    const materialRequested = JSON.parse(request.materialRequested || "[]");
    request.Material_Information = JSON.parse(
      request.Material_Information || "{}"
    );

    // Assign the supplierID to each requestedMaterial
    for (const requestedMaterial of materialRequested) {
      requestedMaterial.supplierID = request.supplierID;
      requestedMaterial.respondDate = request?.respondDate?.value;
      requestedMaterial.requestDate = request?.requestDate?.value;
      requestedMaterial.supplierName = request?.supplierName;

      if (
        request?.recommendedSupplier?.toString() ===
        requestedMaterial?.supplierID?.toString()
      ) {
        requestedMaterial.recommended = true;
      }
    }

    const filteredExistingRequests = materialRequested.filter(
      (material) =>
        request.PartNumber === material.partNumber &&
        request.Brand.trim().toUpperCase() ===
          material.brand.trim().toUpperCase()
    );

    if (!isEmpty(filteredExistingRequests)) {
      request.filteredExistingRequests = filteredExistingRequests;
    }

    request.UserID = request.KAM;
    request.KAM = `${request?.FirstName?.[0] || ""}${
      request?.LastName?.[0] || ""
    }`;
    request.firstName = request.FirstName;
    request.lastName = request.LastName;

    delete request.materialRequested;
    delete request.supplierID;
    delete request.ID_1;
    delete request.FirstName;
    delete request.LastName;
    delete request.recommended;
    delete request.recommendedSupplier;
    delete request.supplierName;
    delete request.respondDate;
    delete request.requestDate;

    if (!IDs.has(request.ID)) {
      // Add the request to the CentralizedRequests array if the ID is not already included
      CentralizedRequests.push(request);
      IDs.add(request.ID);
    } else {
      // Find the existing request with the same ID and merge filteredExistingRequests
      const existingRequest = CentralizedRequests.find(
        (r) => r.ID === request.ID
      );
      if (existingRequest && request.filteredExistingRequests) {
        existingRequest.filteredExistingRequests = [
          ...(existingRequest.filteredExistingRequests || []),
          ...request.filteredExistingRequests,
        ];
      }
    }
  }

  for (const request of CentralizedRequests) {
    let respondedCount = 0;
    let requestedCount = 0;

    if (!isEmpty(request.filteredExistingRequests)) {
      for (const material of request.filteredExistingRequests) {
        if (material.status === "RESPONDED") {
          respondedCount++;
          requestedCount++;
        } else {
          requestedCount++;
        }
      }
    }

    request.respondedCount = respondedCount;
    request.requestedCount = requestedCount;
  }

  let { page, limit, filterData } = req.body;

  limit = limit || 10;
  page = page || 1;

  let offset = (page - 1) * limit;

  let filteredCentralizedRequests = CentralizedRequests;
  let revisedCount = 0;
  if (!isEmpty(filterData)) {
    for (const filter of filterData) {
      if (filter?.heading === "Brand" && filter?.data) {
        filteredCentralizedRequests = filteredCentralizedRequests.filter(
          (request) =>
            request?.Brand?.toUpperCase().trim() ===
            filter.data.toUpperCase().trim()
        );
        revisedCount++;
      }

      if (filter?.heading === "RFQ_ID" && filter?.data) {
        filteredCentralizedRequests = filteredCentralizedRequests.filter(
          (request) =>
            request?.RFQ_ID?.toUpperCase().trim() ===
            filter.data.toUpperCase().trim()
        );
        revisedCount++;
      }

      if (filter?.heading === "KAM" && filter?.data) {
        filteredCentralizedRequests = filteredCentralizedRequests.filter(
          (request) =>
            request?.UserID?.toUpperCase().trim() ===
            filter.data.toUpperCase().trim()
        );
        revisedCount++;
      }

      if (filter?.heading === "Deadline" && filter?.data) {
        filteredCentralizedRequests = filteredCentralizedRequests.filter(
          (request) =>
            request?.Material_Information?.Deadline.value
              .toUpperCase()
              .trim() === filter.data.toUpperCase().trim()
        );
        revisedCount++;
      }

      if (filter?.heading === "RFQ_Date" && filter?.data) {
        filteredCentralizedRequests = filteredCentralizedRequests.filter(
          (request) =>
            request?.Date.value.toUpperCase().trim() ===
            filter.data.toUpperCase().trim()
        );
        revisedCount++;
      }

      if (filter?.heading === "respondedCount" && filter?.data) {
        filteredCentralizedRequests = filteredCentralizedRequests.filter(
          (request) =>
            request?.respondedCount.toString() === filter.data.toString()
        );
        revisedCount++;
      }

      if (filter?.heading === "requestedCount" && filter?.data) {
        filteredCentralizedRequests = filteredCentralizedRequests.filter(
          (request) =>
            request?.requestedCount.toString() === filter.data.toString()
        );
        revisedCount++;
      }

      if (filter?.heading === "IDs" && filter?.data) {
        filteredCentralizedRequests = filteredCentralizedRequests.filter(
          (request) => filter?.data?.includes(request?.ID)
        );
        revisedCount++;
      }

      if (filter?.heading === "supplierBrandList" && filter?.data) {
        filteredCentralizedRequests = filteredCentralizedRequests.filter(
          (request) => filter?.data?.includes(request?.Brand)
        );
        revisedCount++;
      }

      if (filter?.heading === "search" && filter?.data) {
        const filterData = filter.data.toString().toUpperCase();
        filteredCentralizedRequests = filteredCentralizedRequests.filter(
          (request) =>
            request?.KAM?.toUpperCase().includes(filterData) ||
            request?.PartNumber?.toString().includes(filterData) ||
            request?.Brand?.toUpperCase().includes(filterData) ||
            request?.Material_Information?.quantity
              ?.toString()
              .includes(filterData) ||
            request?.Material_Information?.Material_Description?.toString().includes(
              filterData
            )
        );
        revisedCount++;
      }
    }
  }
  const count =
    revisedCount > 0
      ? filteredCentralizedRequests.length
      : CentralizedRequests.length;

  filteredCentralizedRequests = filteredCentralizedRequests.slice(
    offset,
    offset + limit
  );

  const data = {
    count,
    filteredCentralizedRequests,
  };

  return apiResponse(FETCH, "Centralized Requests", data, res);
});

const bulkRequestSupplier = apiHandler(async (req, res) => {
  const { supplier, materials } = req.body;

  const [supplierInformation] = await mainDataset.query({
    query: `
        SELECT Name, Email, SupplierID
        FROM Suppliers
        WHERE SupplierID = @SupplierID
      `,
    params: {
      SupplierID: supplier,
    },
  });

  if (isEmpty(supplierInformation)) {
    return apiError(NOT_FOUND, "Supplier Information", null, res);
  }

  for (const [key, value] of Object.entries(supplierInformation[0])) {
    if (isEmpty(value)) {
      return apiError(CUSTOM_ERROR, `Supplier does not have ${key}`, null, res);
    }
  }

  const materialsWithInfo = materials.map((material) => ({
    description: material.Material_Description || "",
    RFQ_ID: material.rfqID,
    materialID: material.materialID,
    partNumber: material.partNumber || "",
    brand: material.brand || "",
    quantity: material.quantity || 0,
    status: "REQUESTED",
    unitPrice: material.unitPrice || "",
    productCondition: material.productCondition || "",
    notes: material.notes || "",
    currency: material.currency || "",
    RFQ_Number: material.RFQ_Number,
    RFQ_Name: material.RFQ_Name,
    RFQ_Date: material.RFQ_Date,
    Delivery_Date: material.Delivery_Date || null,
    Created_By: req.user.userId.toString(),
    Deadline: material.Deadline || null,
    Portal: material.Portal || "",
    URL: material.URL || "",
    leadTime: material.leadTime || "",
    name: `${material.FirstName || ""} ${material.LastName || ""}`,
  }));

  const [existingSupplier] = await mainDataset.query({
    query: `
      SELECT materialRequested FROM Supplier_Requests
      WHERE supplierID = @SupplierID
      `,
    params: {
      SupplierID: supplierInformation[0].SupplierID.toString(),
    },
  });

  if (isEmpty(existingSupplier)) {
    const ID = generateID();

    const [newSupplier] = await mainDataset.query({
      query: `
          INSERT INTO Supplier_Requests
          (ID, supplierName, supplierID, supplierEmail, materialRequested, status)
          values (@ID, @supplierName, @supplierID, @supplierEmail, @materialRequested, @status)
          `,
      params: {
        ID,
        supplierName: supplierInformation[0].Name,
        supplierID: supplierInformation[0].SupplierID.toString(),
        supplierEmail: supplierInformation[0].Email,
        materialRequested: JSON.stringify(materialsWithInfo),
        status: "PENDING",
      },
    });
  } else {
    const existingMaterialRequestString = existingSupplier[0].materialRequested;
    const existingMaterialRequestArray = JSON.parse(
      existingMaterialRequestString
    );

    for (const newMaterial of materialsWithInfo) {
      const existingMaterial = existingMaterialRequestArray.find(
        (existingMaterial) =>
          existingMaterial.partNumber.trim() ===
            newMaterial.partNumber.trim() &&
          existingMaterial.brand.trim().toUpperCase() ===
            newMaterial.brand.trim().toUpperCase()
      );

      if (!existingMaterial) {
        existingMaterialRequestArray.push(newMaterial);
      } else {
        existingMaterial.status = "REQUESTED";
      }
    }

    const [updatingSupplier] = await mainDataset.query({
      query: `
      UPDATE Supplier_Requests SET
      materialRequested = @materialRequested, respondDate = NULL, status = "PENDING"
      WHERE supplierID=@supplierID

    `,
      params: {
        materialRequested: JSON.stringify(existingMaterialRequestArray),
        supplierID: supplierInformation[0].SupplierID.toString(),
      },
    });
  }

  return apiResponse(ADD_SUCCESS, "Suppliers Requests", null, res);
});

const useQuote = apiHandler(async (req, res) => {
  const {
    supplierID,
    partNumber,
    rfqId,
    materialId,
    unitPrice,
    quantity,
    weight,
    currency,
    isTax,
    leadTime,
    isEdit,
    notes,
    UserID,
    firstName,
    lastName,
  } = req.body;

  if (!isEmpty(leadTime)) {
    const kam = `${firstName}.${lastName}`;

    const showSuppliers = [parseInt(supplierID)];

    // First query: Update supplier_price, setting recommended to null
    await mainDataset.query({
      query: `
      UPDATE supplier_price SET
      recommended = null
      WHERE recommended = true AND partNumber = @partNumber;`,
      params: { partNumber },
    });

    // Second query: Update supplier_price, setting recommended to true for the given supplier
    await mainDataset.query({
      query: `
      UPDATE supplier_price SET
      recommended = true
      WHERE partNumber = @partNumber AND supplierID = @supplierID;`,
      params: { partNumber, supplierID },
    });

    // Third query: Update the showSuppliers field in the Material table
    // await scrapedDataset.query({
    //   query: `
    //     UPDATE Material SET
    //     showSuppliers = @showSuppliers
    //     WHERE Part_Number = @partNumber;`,
    //   params: { partNumber, showSuppliers: JSON.stringify(showSuppliers) },
    // });
    let selectExpression = "sr.Rate as shippingRate, er.Value as exchangeRate";
    let whereExpression =
      "WHERE s.SupplierID = @supplierId AND sr.Currency = @currency AND er.To = 'USD'";
    if (currency === "USD") {
      selectExpression = selectExpression.replace(
        ", er.Value as exchangeRate",
        ""
      );
      whereExpression = whereExpression.replace(" AND er.To = 'USD'", "");
    }
    const [calculationRates] = await mainDataset.query({
      query: `
      SELECT ${selectExpression}
      FROM Suppliers as s
      LEFT JOIN Shipping_Rates as sr ON UPPER(sr.Origin) = UPPER(s.Shipping)
      ${
        currency !== "USD"
          ? `LEFT JOIN Exchange_Rates as er ON er.From = @currency`
          : ``
      }
      ${whereExpression}
    `,
      params: {
        supplierId: parseInt(supplierID),
        currency,
      },
    });
    if (isEmpty(calculationRates)) {
      return apiError(NOT_FOUND, "Exchange Rates for Supplier", null, res);
    }
    const { shippingRate, exchangeRate } = calculationRates[0];
    const shippingCost = round(weight * shippingRate, 2);
    const unitPriceBeforeTax = round(unitPrice + shippingCost, 2);
    const customsTax = round(isTax ? CUSTOMS_TAX * unitPriceBeforeTax : 0, 2);
    const unitPriceAfterTax = round(unitPriceBeforeTax + customsTax, 2);
    const totalCost = round(unitPriceAfterTax * quantity, 2);
    const estimatedOfferCost = round(
      exchangeRate ? (totalCost / 0.7) * exchangeRate : totalCost / 0.7,
      2
    );
    const calculationDetails = {
      shippingCost,
      unitPriceBeforeTax,
      customsTax,
      unitPriceAfterTax,
      totalCost,
      estimatedOfferCost,
    };
    await mainDataset.query({
      query: `
      INSERT INTO Quotes (QuoteID, RFQID, MaterialID, SupplierID, QuoteDate, UnitPrice,
                          UnitCurrency, Quantity, ShippingCost, Tax, TotalCost, OfferedPrice, 
                          DeliveryDate, KAM, Offered, Notes, LeadTime, Weight)
      VALUES (@quoteId, @rfqId, @materialId, @supplierId, @quoteDate, @unitPrice,
              @currency, @quantity, @shippingCost, @tax, @totalCost, @offerPrice,
              @deliveryDate, @kam, @offered, @notes, @leadTime, @weight)
    `,
      params: {
        quoteId: generateID(),
        rfqId: parseInt(rfqId),
        materialId: parseInt(materialId),
        supplierId: parseInt(supplierID),
        quoteDate: setDate(),
        unitPrice,
        currency,
        quantity,
        shippingCost,
        tax: customsTax,
        totalCost,
        weight,
        offerPrice: estimatedOfferCost,
        deliveryDate: setDate(leadTime),
        kam,
        offered: false,
        leadTime,
        notes: notes || "",
      },
    });

    let message = `The Prices for Material - ${partNumber} have been received as recommendation`;

    sendNotification({
      userID: UserID,
      message: message,
    });
  } else {
    const showSuppliers = [parseInt(supplierID)];

    // First query: Update supplier_price, setting recommended to null
    await mainDataset.query({
      query: `
        UPDATE supplier_price SET
        recommended = null
        WHERE recommended = true AND partNumber = @partNumber;`,
      params: { partNumber },
    });

    // Second query: Update supplier_price, setting recommended to true for the given supplier
    await mainDataset.query({
      query: `
        UPDATE supplier_price SET
        recommended = true
        WHERE partNumber = @partNumber AND supplierID = @supplierID;`,
      params: { partNumber, supplierID },
    });

    // Third query: Update the showSuppliers field in the Material table
    await scrapedDataset.query({
      query: `
        UPDATE Material SET
        showSuppliers = @showSuppliers
        WHERE Part_Number = @partNumber;`,
      params: { partNumber, showSuppliers: JSON.stringify(showSuppliers) },
    });
  }

  return apiResponse(SENT_SUCCESS, "Quote Recommendation", null, res);
});

const addManualPrice = apiHandler(async (req, res) => {
  const { suppliers, data } = req.body;

  const brand = data.brand;

  // Initialize suppliers information array
  const suppliersInformation = [];
  const material = [
    {
      ...data,
      Created_By: req.user.userId.toString(),
      name: `${req.user.firstName || ""} ${req.user.lastName || ""}`,
    },
  ];

  // Loop through suppliers and fetch information
  for (const supplierID of suppliers) {
    const [existingSupplier] = await mainDataset.query({
      query: `
          SELECT Name, Email, SupplierID
          FROM Suppliers
          WHERE SupplierID = @supplierID
        `,
      params: { supplierID },
    });

    if (!existingSupplier || existingSupplier.length === 0) {
      return apiError(
        CUSTOM_ERROR,
        `Supplier with ID ${supplierID} not found`,
        null,
        res
      );
    }

    suppliersInformation.push(existingSupplier[0]);

    const [existingBrand] = await mainDataset.query({
      query: `
          SELECT * FROM Suppliers_Brand 
          WHERE SupplierID = @supplierID AND TRIM(UPPER(Brand)) = @brand
        `,
      params: { supplierID, brand: brand.toUpperCase().trim() },
    });

    if (isEmpty(existingBrand[0])) {
      const [addSupplierBrand] = await mainDataset.query({
        query: `
            INSERT INTO Suppliers_Brand 
            (Brand, SupplierID)
            VALUES 
            (@brand, @supplierID)
          `,
        params: { supplierID, brand: brand.toUpperCase().trim() },
      });
    }
  }

  const ID = generateID();

  // Handle Supplier_Requests
  for (const supplierInfo of suppliersInformation) {
    const [existingSupplierRequest] = await mainDataset.query({
      query: `
          SELECT * FROM Supplier_Requests 
          WHERE supplierID = @supplierID
        `,
      params: { supplierID: supplierInfo.SupplierID.toString() },
    });

    if (!existingSupplierRequest || existingSupplierRequest.length === 0) {
      for (const [key, value] of Object.entries(supplierInfo)) {
        if (isEmpty(value)) {
          return apiError(
            CUSTOM_ERROR,
            `Supplier does not have ${key}`,
            null,
            res
          );
        }
      }

      await mainDataset.query({
        query: `
            INSERT INTO Supplier_Requests 
            (ID, supplierName, supplierID, supplierEmail, materialRequested, requestDate, respondDate, status)
            VALUES (@ID, @supplierName, @supplierID, @supplierEmail, @materialRequested, @date, @date, @status)
          `,
        params: {
          ID,
          supplierName: supplierInfo.Name,
          supplierID: supplierInfo.SupplierID.toString(),
          supplierEmail: supplierInfo.Email,
          materialRequested: JSON.stringify(material),
          status: "RESPONDED",
          date: setDate(),
        },
      });
    } else {
      const existingMaterialRequestArray = JSON.parse(
        existingSupplierRequest[0].materialRequested
      );

      const foundExistingMaterial = existingMaterialRequestArray.some(
        (existingMaterial) =>
          existingMaterial.partNumber === material[0].partNumber &&
          existingMaterial.brand === material[0].brand
      );

      if (foundExistingMaterial) {
        existingMaterialRequestArray.forEach((existingMaterial, index) => {
          if (
            existingMaterial.partNumber === material[0].partNumber &&
            existingMaterial.brand === material[0].brand
          ) {
            existingMaterialRequestArray[index] = material[0];
          }
        });
      } else {
        existingMaterialRequestArray.push(material[0]);
      }

      await mainDataset.query({
        query: `
            UPDATE Supplier_Requests 
            SET materialRequested = @materialRequested, requestDate = @date, respondDate = @date
            WHERE supplierID = @supplierID
          `,
        params: {
          materialRequested: JSON.stringify(existingMaterialRequestArray),
          supplierID: supplierInfo.SupplierID.toString(),
          date: setDate(),
        },
      });
    }
  }

  const supplierID = suppliers[0].toString();
  const RequestID = ID.toString();

  const [existingSupplier] = await mainDataset.query({
    query: `
        SELECT *
        FROM supplier_price
        WHERE supplierID = @supplierID AND partNumber=@partNumber AND UPPER(brand)=UPPER(@brand)`,
    params: {
      supplierID,
      partNumber: data.partNumber,
      brand: data.brand.toUpperCase(),
    },
  });

  if (isEmpty(existingSupplier)) {
    const id = generateID();
    const [newSupplier] = await mainDataset.query({
      query: `
          INSERT INTO supplier_price 
          (partNumber, brand, description, quantity, unitPrice, totalPrice, notes, date, supplierID, productCondition, ID, requestID, currency)
          Values
          (@partNumber, @brand, @description, @quantity, @unitPrice, @totalPrice, @notes, @date, @supplierID, @productCondition, @id, @RequestID, @currency)
    
        `,
      params: {
        partNumber: data.partNumber,
        brand: data.brand.toUpperCase(),
        description: data.description,
        quantity: parseInt(data.quantity),
        unitPrice: parseFloat(data.unitPrice),
        totalPrice: parseFloat(data.totalPrice),
        notes: data.notes || "",
        date: setDate(),
        supplierID,
        productCondition: data.productCondition,
        id: id.toString(),
        RequestID,
        currency: data.currency,
      },
    });
  } else {
    const [oldSupplier] = await mainDataset.query({
      query: `
          UPDATE supplier_price SET
          description=@description, quantity=@quantity, unitPrice=@unitPrice, totalPrice=@totalPrice, notes=@notes, date=@date, productCondition=@productCondition, requestID = @RequestID, currency = @currency
          WHERE supplierID = @supplierID AND partNumber=@partNumber AND UPPER(brand)=UPPER(@brand)
    
        `,
      params: {
        partNumber: data.partNumber,
        brand: data.brand.toUpperCase(),
        description: data.description,
        quantity: parseInt(data.quantity),
        unitPrice: parseFloat(data.unitPrice),
        totalPrice: parseFloat(data.totalPrice),
        notes: data.notes || "",
        date: setDate(),
        supplierID,
        productCondition: data.productCondition,
        RequestID,
        currency: data.currency,
      },
    });
  }

  return apiResponse(ADD_SUCCESS, "Supplier Price", null, res);
});

const batchInsertEmailSupplier = async (KAM_ID, RFQs, supplierData) => {
  let emailInsertValues = RFQs.map((RFQ, index) => {
    return {
      ID: generateID(),
      RFQID: Number(RFQ.rfqID),
      Supplier_ID: Number(supplierData.SupplierID),
      Materials: JSON.stringify(RFQ.materials),
      Email_Sent_Timestamp: new Date(),
      Email_Status: "SENT",
      Created_By: Number(KAM_ID),
    };
  });

  let emailQuery = `
    INSERT INTO Email_Supplier_Requests (ID, RFQID, Supplier_ID, Materials, Email_Sent_Timestamp, Email_Status, Created_By)
    VALUES 
  `;

  let emailParams = {};
  emailInsertValues.forEach((email, index) => {
    let suffix = index === emailInsertValues.length - 1 ? ";" : ",";
    emailQuery += `(@ID${index}, @RFQID${index}, @Supplier_ID${index}, @Materials${index}, @Email_Sent_Timestamp${index}, @Email_Status${index}, @Created_By${index})${suffix}`;

    emailParams[`ID${index}`] = email.ID;
    emailParams[`RFQID${index}`] = email.RFQID;
    emailParams[`Supplier_ID${index}`] = email.Supplier_ID;
    emailParams[`Materials${index}`] = email.Materials;
    emailParams[`Email_Sent_Timestamp${index}`] = email.Email_Sent_Timestamp;
    emailParams[`Email_Status${index}`] = email.Email_Status;
    emailParams[`Created_By${index}`] = email.Created_By;
  });

  await mainDataset.query({
    query: emailQuery,
    params: emailParams,
  });
};

const EmailSuppliers = async (ccEmail, supplierData, RFQ) => {
  const emailTemplate =
    supplierData.Language === "ES"
      ? emailSpanishSupplierRequestTemplate
      : emailSupplierRequestTemplate;

  const option = {
    to: supplierData.Email,
    cc: ccEmail,
    subject: emailTemplate.emailSubject(RFQ),
    html: emailTemplate.htmlTemplate(RFQ),
  };

  await sendMail(option);
};

const getSuppliersInformation = async (res, suppliers) => {
  const [suppliersInformation] = await mainDataset.query({
    query: `
      SELECT Name, Email, SupplierID, Language
      FROM Suppliers
      WHERE SupplierID IN UNNEST(@SupplierIDs)
    `,
    params: {
      SupplierIDs: suppliers,
    },
  });

  if (suppliersInformation.length === 0) {
    apiError(NOT_FOUND, "No Supplier Information found", null, res);
    return null;
  }

  for (const supplier of suppliersInformation) {
    for (const [key, value] of Object.entries(supplier)) {
      if (isEmpty(value)) {
        apiError(
          CUSTOM_ERROR,
          `Supplier - ${supplier.Name} does not have ${key}`,
          null,
          res
        );
        return null;
      }
    }
  }

  return suppliersInformation;
};

const bulkRequestEmailSupplier = apiHandler(async (req, res) => {
  const { suppliers, RFQs, ccEmail } = req.body;
  const { userId, email } = req.user;

  if (suppliers.length > 5) {
    return apiError(
      CUSTOM_ERROR,
      "You cannot select more than 5 suppliers at a time",
      null,
      res
    );
  }

  try {
    const suppliersData = await getSuppliersInformation(res, suppliers);
    if (!suppliersData) return;
    await Promise.all(
      suppliersData.map(async (supplierData) => {
        await batchInsertEmailSupplier(userId, RFQs, supplierData);
        await Promise.all(
          RFQs.map((RFQ) => EmailSuppliers(ccEmail, supplierData, RFQ))
        );
      })
    );
    return apiResponse(ADD_SUCCESS, "Supplier Requests", null, res);
  } catch (error) {
    return apiResponse(
      CUSTOM_ERROR,
      "Error processing supplier requests",
      error,
      res
    );
  }
});

const updateEmailStatus = apiHandler(async (req, res) => {
  const { id, status } = req.body;

  await mainDataset.query({
    query: `
        UPDATE Email_Supplier_Requests
        SET Email_Status = @Email_Status
        WHERE ID = @ID
      `,
    params: {
      ID: id,
      Email_Status: status,
    },
  });

  return apiResponse(UPDATE_SUCCESS, "Email Status", null, res);
});

const getAllEmailSupplierRequests = apiHandler(async (req, res) => {
  let whereExpression =
    req.user.role !== "SUPERVISOR"
      ? ` WHERE esr.Created_By = ${req.user.userId} `
      : "";

  const [emailSupplierRequests] = await mainDataset.query({
    query: `
        SELECT esr.*, s.Name as Supplier_Name, s.Email as Supplier_Email, u.Email as KAM_Email, CONCAT(u.firstName, ' ', u.lastName) as KAM_Name , u.UserID as KAM_ID
        FROM Email_Supplier_Requests as esr
        LEFT JOIN Suppliers as s ON s.SupplierID = esr.Supplier_ID
        LEFT JOIN Users as u ON esr.Created_By = u.UserID
        ${whereExpression}
      `,
  });

  return apiResponse(
    FETCH,
    "Email Suppliers Requests",
    emailSupplierRequests,
    res
  );
});

const getSupplierReplies = apiHandler(async (req, res) => {
  const { RFQID } = req.params;
  const replies = await checkReplies(RFQID);
  apiResponse(FETCH, "Replies", replies, res);
});

module.exports = {
  addCentralizedRequest,
  getAllCentralizedRequests,
  bulkRequestSupplier,
  useQuote,
  updateEmailStatus,
  addManualPrice,
  bulkRequestEmailSupplier,
  getAllEmailSupplierRequests,
  getSupplierReplies,
};
