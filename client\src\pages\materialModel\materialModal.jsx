import { Divider } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import PropTypes from 'prop-types';
import SearchComponent from './materialSearchTable';
import MaterialForm from './materialform';
import './model.css';
export default function MaterialModal({
  addMaterial,
  open,
  isEdit,
  handleClose,
  confirmButton,
  material,
  searchMaterial,
  similarMaterials,
  singlePartNumber,
  loading,
  useMaterial
}) {
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      PaperProps={{
        sx: {
          borderRadius: '10px'
        }
      }}
    >
      <DialogContent sx={{ borderRadius: 10 }}>
        <Grid container spacing={2} pt={2}>
          <Grid item xs={12} sm={6} sx={{ borderRight: '2px solid #dedbdb' }}>
            <Typography variant="h5" color="secondary" gutterBottom>
              Material
            </Typography>

            <div style={{ maxHeight: '450px', overflowY: 'auto' }}>
              <MaterialForm
                isEdit={isEdit}
                loading={loading}
                addMaterial={(updatedValues) => addMaterial(updatedValues)}
                singlePartNumber={singlePartNumber}
                confirmButton={(updatedValues) => confirmButton(updatedValues)}
                material={material}
                searchMaterial={(partNumber, isSimilar) => searchMaterial(partNumber, isSimilar)}
              />
            </div>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography variant="h5" color="secondary" gutterBottom>
              Search
            </Typography>
            <Divider orientation="vertical" flexItem />

            <SearchComponent
              searchMaterial={(partNumber, isSimilar) => searchMaterial(partNumber, isSimilar)}
              material={material}
              similarMaterials={similarMaterials}
              loading={loading}
              useMaterial={(material) => useMaterial(material)}
            />
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  );
}

MaterialModal.propTypes = {
  open: PropTypes.bool.isRequired,
  handleClose: PropTypes.func.isRequired
};
