import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getLegalTerms } from 'redux/reducers/settingReducer';

const DynamicLegalTerms = ({ language }) => {
  const dispatch = useDispatch();
  const { legalTerms } = useSelector((state) => state.setting);
  const [content, setContent] = useState('');

  useEffect(() => {
    if (language) {
      const lang = language === 'English' ? 'EN' : 'ES';
      setContent(legalTerms[lang] || '');
    }
  }, [legalTerms, language]);

  const styles = {
    container: {
      padding: '100px',
      fontFamily: 'sans-serif',
      color: '#000000'
    }
  };

  return (
    <div style={styles.container}>
      <div dangerouslySetInnerHTML={{ __html: content }} style={{ color: '#000000' }} />
    </div>
  );
};

export default DynamicLegalTerms;
