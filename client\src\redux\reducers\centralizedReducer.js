import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { getRequest, patchRequest, post } from 'utils/axios';
import {
  addMaualPriceApiUrl,
  bulkEmailPriceApiUrl,
  getAllCentralizedRequestApiUrl,
  getAllEmailPriceRequestApiUrl,
  getEmailResponsesByRFQIDApiUrl,
  sentBulkQuteRequesApiUrl,
  updatedEmailPriceReqStatusApiUrl,
  usePriceApiUrl
} from 'utils/constant';
import { logoutUser, notification, showAlert } from 'utils/helper';
import { getAllKamAction } from './RfqReducer';
import { getUserDetail } from 'utils/auth';

export const getAllCentralizedRequestAction = createAsyncThunk(getAllCentralizedRequestApiUrl, async (data, { dispatch }) => {
  try {
    const response = await post(getAllCentralizedRequestApiUrl, data, true, dispatch);

    if (response) {
      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        return { data: data || [], success: success || false };
      }
      if (!success) {
        return { data: data || [], success: success || false };
      }
    } else {
      return { data: [], success: false };
    }
  } catch (error) {
    return { data: [], success: false };
  }
});

export const getAllEmailPriceRequestAction = createAsyncThunk(getAllEmailPriceRequestApiUrl, async (data, { dispatch }) => {
  const { setLoading, setEmailRequestsAction } = requestedPriceSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await getRequest(getAllEmailPriceRequestApiUrl, data, true, dispatch);

    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        dispatch(setEmailRequestsAction(data));
        return { data: data || [], success: success || false };
      }
      if (!success) {
        dispatch(setEmailRequestsAction([]));
        return { data: data || [], success: success || false };
      }
    } else {
      dispatch(setLoading(false));
      dispatch(setEmailRequestsAction([]));
      return { data: [], success: false };
    }
  } catch (error) {
    dispatch(setLoading(false));
    dispatch(setEmailRequestsAction([]));
    return { data: [], success: false };
  }
});

export const getEmailResponsesByRFQIDAction = createAsyncThunk(getEmailResponsesByRFQIDApiUrl, async (data, { dispatch }) => {
  const { setLoading, setEmailResponsesAction } = requestedPriceSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await getRequest(`${getEmailResponsesByRFQIDApiUrl}/${data}`, data, true, dispatch);

    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        dispatch(setEmailResponsesAction(data));
        return { data: data || [], success: success || false };
      }
      if (!success) {
        dispatch(setEmailResponsesAction([]));
        return { data: data || [], success: success || false };
      }
    } else {
      dispatch(setLoading(false));
      dispatch(setEmailResponsesAction([]));
      return { data: [], success: false };
    }
  } catch (error) {
    dispatch(setLoading(false));
    dispatch(setEmailResponsesAction([]));
    return { data: [], success: false };
  }
});

export const sendBulkQuoteRequest = createAsyncThunk(sentBulkQuteRequesApiUrl, async (data, { dispatch }) => {
  const { rfqID } = data || {};
  try {
    dispatch(requestedPriceSlice.actions.setLoading(true));
    const response = await post(sentBulkQuteRequesApiUrl, data, true, dispatch);
    if (response) {
      dispatch(requestedPriceSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(requestedPriceSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(requestedPriceSlice.actions.setLoading(false));
    return { succes: false };
  }
});

export const usePriceQuoteRequest = createAsyncThunk(usePriceApiUrl, async (data, { dispatch }) => {
  try {
    dispatch(requestedPriceSlice.actions.setLoading(true));
    const response = await post(usePriceApiUrl, data, true, dispatch);
    if (response) {
      dispatch(requestedPriceSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);

        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(requestedPriceSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(requestedPriceSlice.actions.setLoading(false));
    return { succes: false };
  }
});
export const addManualPriceAction = createAsyncThunk(addMaualPriceApiUrl, async (data, { dispatch }) => {
  const { setLoading } = requestedPriceSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await post(addMaualPriceApiUrl, data, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);

        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return { succes: false };
  }
});

export const bulkEmailPriceRequestAction = createAsyncThunk(bulkEmailPriceApiUrl, async (data, { dispatch }) => {
  const { setLoading } = requestedPriceSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await post(bulkEmailPriceApiUrl, data, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return { succes: false };
  }
});

export const updateEmailStatusAction = createAsyncThunk('updateMailStatusAction', async (data, { dispatch }) => {
  const { setLoading } = requestedPriceSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await patchRequest(updatedEmailPriceReqStatusApiUrl, data, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return { succes: false };
  }
});

const requestedPriceSlice = createSlice({
  name: 'requestedPrice',
  initialState: {
    data: [],
    emailPriceRequests: [],
    singleUser: {},
    status: 'idle',
    error: null,
    loading: false
  },
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setSingleUserData: (state, action) => {
      state.singleUser = action.payload;
    },
    setEmailRequestsAction: (state, action) => {
      state.emailPriceRequests = action.payload;
    },
    setEmailResponsesAction: (state, action) => {
      state.emailResponses = action.payload;
    },
    setShippingData: (state, action) => {
      state.allShipping = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllCentralizedRequestAction.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getAllCentralizedRequestAction.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload?.data;
      })
      .addCase(getAllCentralizedRequestAction.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      });
  }
});

export const { setLoading, setSingleUserData, setEmailResponsesAction, setEmailRequestsAction } = requestedPriceSlice.actions;
export default requestedPriceSlice.reducer;
