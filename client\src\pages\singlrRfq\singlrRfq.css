.secondary-contatiner {
  display: flex;
}

.material-brand {
  margin-left: 30px;
  margin-top: 5px;
}

.material-brand button {
  margin-top: -5px;
}

.over-view-container {
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
}

.history-btn {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  gap: 10px
}

.benchmark-card-wrapper {
  display: grid;
  grid-template-columns: 3fr 1fr;
}

.flex-container {
  display: flex;
  justify-content: space-between;
}