/* eslint-disable no-unused-vars */
import Grid from '@mui/material/Grid';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { get } from 'lodash';
// import { fetchSupplierList } from 'redux/reducers/supplierPortalReducer';
import { Card, CardContent, Divider, IconButton, Typography } from '@mui/material';
import TableComponent from 'pages/component/table/table';
import ActionButton from 'pages/component/actionButton';
import { useNavigate } from 'react-router';
import { addSupplierUrl, APPROVED, REJECTED, updateSupplierPageUrl } from 'utils/constant';
import Loader from 'components/Loader';
import { getSupplierList } from 'redux/reducers/offerReducer';
import { deleteSupplierAction } from 'redux/reducers/supplierMaintenance';
import AlertDialog from 'pages/component/dialogbox';
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined';
import AddEditClient from 'pages/clients/addEditClient';
import { tabsData } from './constant';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';

export default function Supplier() {
  const [suppliers, setSuppliers] = useState([]);
  const [allSuppliers, setAllSuppliers] = useState([]);
  const supplierList = useSelector((state) => state.offer);
  const suppliersData = useSelector((state) => state.supplier);
  const [open, setOpen] = useState(false);
  const [supplierId, setSupplierId] = useState('');

  const dispatch = useDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    const supplierRows = get(supplierList, 'supplierList', []).map((item) => {
      const { Name, ContactName, ContactLastname, Status, Reject_Reason, Email, Country, SupplierID, Web } = item || {};
      return {
        name: Name,
        id: SupplierID,
        contactName: `${ContactName || ''} ${ContactLastname || ''}`.trim(),
        email: Email,
        country: Country,
        url: Web,
        status: Status,
        reason: Status === REJECTED ? Reject_Reason : '',
        isApprovedOrReject: Status === REJECTED || Status === APPROVED
      };
    });

    setSuppliers(supplierRows);
    setAllSuppliers(supplierRows);
  }, [supplierList?.supplierList]);

  useEffect(() => {
    dispatch(getSupplierList());
  }, []);

  const headers = [
    {
      name: 'id',
      type: 'RFQ Id',
      title: 'Supplier ID',
      sortingactive: true,
      minWidth: '150px'
    },
    {
      name: 'name',
      type: 'text',
      title: 'Name',
      sortingactive: true,
      maxWidth: '100px'
    },
    {
      name: 'contactName',
      type: 'text',
      title: 'Contact Name',
      sortingactive: true,
      maxWidth: '100px'
    },
    {
      name: 'email',
      type: 'email',
      title: 'Email',
      sortingactive: true
    },
    {
      name: 'country',
      type: 'text',
      title: 'Country',
      sortingactive: true
    },
    {
      name: 'url',
      type: 'link',
      title: 'Website',
      sortingactive: true
    },
    {
      name: 'status',
      type: 'badge',
      title: 'Status',
      sortingactive: true,
      className: 'status-badge'
    },
    {
      name: 'actions',
      btnType: 'infoIcon',
      type: 'actions',
      toolTipName: 'reason',
      title: 'Reason',
      sortingactive: false,
      component: ActionButton,
      buttonOnClick: (type, id, materialId, index, data) => {
      }
    },
    {
      name: 'actions',
      btnType: 'multipleButton',
      type: 'actions',
      btnName: 'use',
      title: 'Action',
      sortingactive: false,
      minWidth: '100px',

      component: ActionButton,

      multipleButtons: [
        {
          type: 'icon',
          icon: <EditOutlinedIcon fontSize="16px" />,
          buttonOnClick: (type, rowData) => {
            navigate(`${updateSupplierPageUrl}/${rowData?.id}`);
          },
          color: 'primary',
          tooltip: 'Edit',
          showButton: 'isApprovedOrReject'
        },
        {
          icon: <DeleteOutlineOutlinedIcon fontSize="16px" />,
          type: 'icon',
          buttonOnClick: (type, rowData) => {
            setSupplierId(rowData?.id);
            setOpen(true);
          },
          color: 'error',
          tooltip: 'Delete'
        }
      ]
    }
  ];

  const handleSearch = (filteredRows, searchValue) => {
    setSuppliers(filteredRows)
  }

  const actionBtns = [
    { title: 'Add Supplier', onClick: () => navigate(addSupplierUrl) }
  ]
  const isLoading = () => {
    return supplierList?.loading || suppliersData?.loading
  }

  const handleClose = () => {
    setOpen(false)
    setSupplierId('')
  }
  const handleDelete = async () => {
    const response = await dispatch(deleteSupplierAction(supplierId))
    const { success } = get(response, 'payload', {})
    if (success) {
      handleClose()
    }
  }

  const buttons = [
    { label: 'Cancel', onClick: () => handleClose(), variant: 'outlined', color: 'error' },
    { label: 'Yes', onClick: () => handleDelete(), variant: 'contained', color: 'primary' },

  ]

  return (
    <Grid container spacing={2}>
      <AlertDialog
        Component={< Typography variant='body1' color='secondary'>Are you sure you want to delete this supplier</Typography>}
        open={open}
        showCard={false}
        borderRadius='20px'
        buttons={buttons}
      />
      {isLoading() && <Loader />}
      <Grid item xs={12}>
        <div>
          <Card>
            <Divider />
            <CardContent sx={{ width: '100%', overflow: 'hidden' }}>
              <TableComponent
                maxHeight={'100%'}
                enablePagination={true}
                columns={headers}
                rows={suppliers || []}
                title="MySupplierTable"
                enableSearch={true}
                handleSearch={(data, searchValue) => handleSearch(data, searchValue)}
                allRows={allSuppliers || []}
                actionBtns={actionBtns}
                placeActionButtonsIn='search'
                isBadgeFilter={true}
                badgeFilterData={tabsData || []}
                showDeleteIcon={true}
              />
            </CardContent>
          </Card>
        </div>
      </Grid>
    </Grid>
  );
}
