// assets
import { DashboardOutlined } from '@ant-design/icons';
import {
  addEmailPricesRequestsPageUrl,
  automatedRfqPageUrl,
  emailPricesRequestsPageUrl,
  myOfferPageUrl,
  reserveRfqPageUrl,
  rfqListPageUrl,
  settingPageUrl
} from 'utils/constant';
import ArticleOutlinedIcon from '@mui/icons-material/ArticleOutlined';
import SaveAsOutlinedIcon from '@mui/icons-material/SaveAsOutlined';
import SettingsIcon from '@mui/icons-material/Settings';
import { Lock } from '@mui/icons-material';
import productManager from './productManager';
import masterData from './masterData';
import AutoModeIcon from '@mui/icons-material/AutoMode';
import MarkEmailReadIcon from '@mui/icons-material/MarkEmailRead';
import ForwardToInboxIcon from '@mui/icons-material/ForwardToInbox';
// icons

const icons = {
  DashboardOutlined
};

// ==============================|| MENU ITEMS - DASHBOARD ||============================== //
const sideBarItems = [
  {
    id: 'dashboard',
    title: 'Available RFQ',
    type: 'item',
    url: rfqListPageUrl,
    icon: SaveAsOutlinedIcon,
    breadcrumbs: false
  },
  {
    id: 'reservedRfq',
    title: 'Reserved RFQ',
    type: 'item',
    url: reserveRfqPageUrl,
    icon: Lock,
    breadcrumbs: false
  },
  {
    id: 'automatedRfq',
    title: 'Automated RFQ',
    type: 'item',
    url: automatedRfqPageUrl,
    icon: AutoModeIcon,
    breadcrumbs: false
  },
  {
    id: 'myOffers',
    title: 'My Offers',
    type: 'item',
    url: myOfferPageUrl,
    icon: ArticleOutlinedIcon,
    breadcrumbs: false
  },
  {
    id: 'emailPrice',
    title: 'Email Prices Requests',
    type: 'item',
    url: emailPricesRequestsPageUrl,
    icon: MarkEmailReadIcon,
    breadcrumbs: false
  },
  // {
  //   id: 'emailPrice',
  //   title: 'Sent Prices Requests',
  //   type: 'item',
  //   url: addEmailPricesRequestsPageUrl,
  //   icon: ForwardToInboxIcon,
  //   breadcrumbs: false
  // },
  masterData
];

export const supervisorSideBar = [
  productManager,
  {
    id: 'Setting',
    title: 'Settings',
    type: 'item',
    url: settingPageUrl,
    icon: SettingsIcon,
    breadcrumbs: false
  }
];

const menuItemsList = {
  id: 'group-dashboard',
  title: '',
  type: 'group',
  children: sideBarItems
};

export default menuItemsList;
