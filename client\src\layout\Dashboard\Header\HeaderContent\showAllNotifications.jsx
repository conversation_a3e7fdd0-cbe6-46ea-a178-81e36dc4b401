import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getNotifications } from 'redux/reducers/notificationReducer';
import { getUserDetail } from 'utils/auth';
import { Card, CardContent, Typography, List, ListItem, ListItemText, Divider, Badge, Grid, Button } from '@mui/material';
import { getDateWithTime } from 'utils/helper';
import { get } from 'lodash';
import Pagination from 'pages/component/pagination';
import { ReservedSingleRfqPageUrl } from 'utils/constant';
import { Link, useNavigate } from 'react-router-dom';

export const LIMIT = 10;
const ShowAllNotification = () => {
  const dispatch = useDispatch();
  const allNotifications = useSelector((state) => state.notification);
  const [notifications, setNotifications] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(10);
  const navigate = useNavigate(); 

  useEffect(() =>  {  
    const updatedNotification = allNotifications?.data?.messages?.map((notification) => ({ ...notification, RFQID:'9440' }) ) || [];
    const totalCount = get(allNotifications, 'data.count', 0);
    const calculatedTotalPages = Math.ceil(totalCount / LIMIT);

    setTotalPages(calculatedTotalPages > 0 ? calculatedTotalPages : 1);
    setNotifications(updatedNotification || []);
  }, [allNotifications?.data]);

  useEffect(() => {
    const user = getUserDetail();
    dispatch(getNotifications({ UserID: user?.userId?.toString(), limit: LIMIT, page: currentPage }));
  }, [currentPage]);

  const handlePageChange = (pageNumber) =>{
    setCurrentPage(pageNumber);
  };

  const handleOpenRFQ = (RFQID) => {
    navigate(`${ReservedSingleRfqPageUrl}/${RFQID}`);
  };

  return (
    <Card variant="outlined" style={styles.card}>
      <CardContent>
        <div style={styles.header}>
          <Typography variant="h5" component="div" gutterBottom color="secondary" fontWeight={600}>
            Notifications
          </Typography>
        </div>
        <List>
          {notifications?.length === 0 ? (
            <Typography variant="body2" color="textSecondary" style={styles.noNotifications}>
              No notifications available.
            </Typography>
          ) : (
            notifications?.map((notification) => (
              <React.Fragment key={notification.id}>
                <ListItem style={styles.notificationDetail}>
                  <ListItemText
                    primary={
                      <Typography variant="body1" style={{ fontWeight: 'bold' }} color="secondary">
                        {notification?.sender}
                      </Typography>
                    }
                    secondary={
                      <>
                        <Typography variant="body2" color="textSecondary" sx={{ width: '80%' }}>
                          {notification?.message}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {getDateWithTime(notification?.date)}
                        </Typography>
                      </>
                    }
                  />
                  {!notification?.read && <Badge variant="dot" color="primary" />}
                  {notification?.RFQID && (
                    <Link component="button" variant="body2" onClick={()=>handleOpenRFQ(notification?.RFQID)} style={{ minWidth: '100px' }}>
                      <Button>Open RFQ</Button>
                    </Link>
                  )}
                </ListItem>
                <Divider />
              </React.Fragment>
            ))
          )}
        </List>

        {totalPages > 0 && (
          <Grid item xs={12}>
            <Grid container justifyContent="center">
              <Pagination currentPage={currentPage} totalPages={totalPages} handlePageChange={handlePageChange} />
            </Grid>
          </Grid>
        )}
      </CardContent>
    </Card>
  );
};

export default ShowAllNotification;

const styles = {
  card: {
    width: '100%',
    maxWidth: 600,
    margin: 'auto',
    borderRadius: '8px'
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  icon: {
    fontSize: '1.15rem'
  },
  noNotifications: {
    textAlign: 'center'
  },
  notificationDetail: {
    backgroundColor: 'transparent',
    borderLeft: 'none',
    borderRadius: 4,
    marginBottom: 8,
    transition: 'background-color 0.3s ease'
  }
};
