import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { getRequest, post } from 'utils/axios';
import {
  addSupplierApiUrl,
  deleteSupplierApiUrl,
  fetchShippingApiUrl,
  materialUpdateApiUrl,
  singleSupplierApiUrl,
  supplierUrl,
  updateSupplierApiUrl,
  updateSupplierApprovalApiUrl,
} from 'utils/constant';
import { notification, showAlert } from 'utils/helper';
import { getSupplierList } from './offerReducer';


export const getSingleSupplier = createAsyncThunk('supplierMaintenanceSlice/fetch', async (supplierId, { rejectWithValue, dispatch }) => {
  try {
    dispatch(supplierMaintenanceSlice.actions.setLoading(true));
    const response = await post(singleSupplierApiUrl(supplierId), {}, true, dispatch);
    const { error, success, message, data } = get(response, 'data', {});
    if (response) {
      dispatch(supplierMaintenanceSlice.actions.setLoading(false));
      if (success) {
        dispatch(supplierMaintenanceSlice.actions.setSingleSupplierData(data || []));
        return data || [];
      } else {
        dispatch(notification(false, message, true));
        return rejectWithValue(message || 'Failed to fetch RFQ data');
      }
    } else {
      dispatch(supplierMaintenanceSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(supplierMaintenanceSlice.actions.setLoading(false));
    return rejectWithValue('An error occurred while fetching RFQ data');
  }
});
export const getShipping = createAsyncThunk('supplierMaintenanceSlice/fetch', async (supplierId, { rejectWithValue, dispatch }) => {
  try {
    const response = await getRequest(fetchShippingApiUrl, {}, true, dispatch);
    const { error, success, message, data } = get(response, 'data', {});

    if (success) {
      dispatch(supplierMaintenanceSlice.actions.setShippingData(data || []));
      return data || [];
    } else {
      dispatch(notification(false, message, true));
      return rejectWithValue(message || 'Failed to fetch RFQ data');
    }
  } catch (error) {
    return rejectWithValue('An error occurred while fetching RFQ data');
  }
});

export const updateSupplierMaintenanace = createAsyncThunk(updateSupplierApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  const { navigate, ...rest } = data || {}
  const payload = { ...rest }
  const { setLoading, setSingleSupplierData } = supplierMaintenanceSlice.actions || {}
  try {
    dispatch(setLoading(true));
    const response = await post(updateSupplierApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        dispatch(setSingleSupplierData({}))
        navigate(supplierUrl)
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});
export const addSupplierMaintenanace = createAsyncThunk(addSupplierApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  const { navigate, ...rest } = data || {}
  const payload = { ...rest }
  const { setLoading, setSingleSupplierData } = supplierMaintenanceSlice.actions || {}
  try {
    dispatch(setLoading(true));
    const response = await post(addSupplierApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        navigate(supplierUrl)
        dispatch(setSingleSupplierData({}))
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});
export const deleteSupplierAction = createAsyncThunk('deleteSupplierAction/fetch', async (supplierId, { rejectWithValue, dispatch }) => {
  const { setLoading } = supplierMaintenanceSlice.actions || {}
  try {
    dispatch(setLoading(true));

    const response = await post(deleteSupplierApiUrl(supplierId), supplierId, true, dispatch);

    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        dispatch(notification(true, message, false));
        dispatch(getSupplierList())
        return { success: true, data };
      }

      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});
export const updateSupplierApprovalAction = createAsyncThunk(updateSupplierApprovalApiUrl, async (payload, { rejectWithValue, dispatch }) => {
  const { setLoading } = supplierMaintenanceSlice.actions || {}
  try {
    dispatch(setLoading(true));

    const response = await post(updateSupplierApprovalApiUrl, payload, true, dispatch);

    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        dispatch(getSingleSupplier(payload?.ID));
        dispatch(notification(true, message, false));
        return { success: true, data };
      }

      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

const supplierMaintenanceSlice = createSlice({
  name: 'supplierMaintenace',
  initialState: {
    data: [],
    allSupplierQuotes: [],
    supplierData: [],
    singleSupplier: {},
    status: 'idle',
    error: null,
    loading: false,
    allShipping: []
  },
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setSingleSupplierData: (state, action) => {
      state.singleSupplier = action.payload;
    },
    setShippingData: (state, action) => {
      state.allShipping = action.payload;
    },

  }
  ,

});
export const { setLoading, setSingleSupplierData } = supplierMaintenanceSlice.actions;
export default supplierMaintenanceSlice.reducer;
