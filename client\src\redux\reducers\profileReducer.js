import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { getUserDetail } from 'utils/auth';

// Asynchronous thunk action to fetch profile data
export const fetchProfileData = createAsyncThunk('profile/fetchProfileData', async () => {
  //   const response = await fetch('/path/to/profile-data.json');
  const response =getUserDetail()
  return response;
});

const profileSlice = createSlice({
  name: 'profile',
  initialState: {
    data: { name: '', role: '', avatar: '' },
    status: 'idle',
    error: null
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchProfileData.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchProfileData.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload;
      })
      .addCase(fetchProfileData.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      });
  }
});

export default profileSlice.reducer;
