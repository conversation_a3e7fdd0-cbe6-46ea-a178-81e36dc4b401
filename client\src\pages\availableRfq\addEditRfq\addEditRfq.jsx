import React, { useEffect, useState } from 'react';
import { Button, Grid, Typography, Box, IconButton, Container } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';
import ErrorMessage from 'pages/component/errorMessage';
import MainCard from 'components/MainCard';
import DatePickerComponent from 'pages/component/dateField';
import InputField from 'pages/component/inputField'; // Assuming this is your custom input field component
import * as Yup from 'yup';
import { get } from 'lodash';
import { validateForm } from './validations';
import { useDispatch, useSelector } from 'react-redux';
import { addRfqAction, getAllPortalAction } from 'redux/reducers/RfqReducer';
import { convertDateToStringFormat, getApprovedClients, showAlert } from 'utils/helper';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { rfqListPageUrl } from 'utils/constant';
import SelectWithAddNew from './selectWithAddOpton';
import Loader from 'components/Loader';
import { getClientList } from 'redux/reducers/offerReducer';
import { getSingleRfq, searchMaterialAction, updateRfqAction } from 'redux/reducers/singleRfq';
import { validate, validateNested } from 'pages/component/validation';

const AddEditRfq = ({ redirectTo }) => {
  const [materials, setMaterials] = useState([{ id: 1, partNumber: '', brand: '', quantity: '', description: '' }]);
  const [portalNameList, setPortalNameList] = useState([]);
  const [clientList, setClientList] = useState([]);
  const [rfqDetails, setRfqDetails] = useState({
    title: '',
    client: '',
    deadLine: '',
    deliveryDate: '',
    clientRfqNumber: '',
    materials: materials
  });
  const [errors, setErrors] = useState({
    title: '',
    client: '',
    deadLine: '',
    clientRfqNumber: '',
    url: '',
    materials: materials?.map(() => ({
      partNumber: '',
      brand: '',
      quantity: '',
      description: ''
    }))
  });
  const rfqData = useSelector((state) => state.rfq);
  const singleRfq = useSelector((state) => state.singleRfq);
  const offerDetail = useSelector((state) => state.offer);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const rfqId = useParams()?.id;

  useEffect(() => {
    dispatch(getAllPortalAction());
    dispatch(getClientList());
  }, []);

  useEffect(() => {
    if (rfqId) {
      dispatch(getSingleRfq(rfqId));
    }
  }, [rfqId]);

  useEffect(() => {
    let updatedPortalList = get(rfqData, 'portalList', [])?.map((portal) => ({ label: portal, value: portal }));
    setPortalNameList(updatedPortalList || []);
  }, [rfqData?.portalList]);

  useEffect(() => {
    let updatedClient = get(offerDetail, 'clientList', [])?.map((client) => ({ label: client?.Name, value: client?.ClientID, Status: client?.Status }));
    setClientList(updatedClient || []);
  }, [offerDetail?.clientList]);

  useEffect(() => {
    const singleRfqData = get(singleRfq, 'data', {});
    if (rfqId && singleRfqData) {
      const {
        Company_Name,
        CurrentStatus,
        Deadline,
        Delivery_Date,
        RFQ_Name,
        RFQ_Number,
        ClientID,
        status,
        URL,
        Portal,
        Materials,
        Priority,
        Address_Information,
        Additional_Notes
      } = singleRfqData || {};
      let singleClient = clientList?.filter((client) => client?.ClientID === ClientID);
      let clientName = singleClient && singleClient?.length > 0 ? singleClient[0]?.Name : '';

      let materialsData = Materials?.map((singleMaterial, index) => {
        const { Part_Number, Position, Quantity_Required, Brand, Material_Description, Material_ID, Notes } = singleMaterial || {};
        return {
          id: index + 1,
          partNumber: Part_Number,
          brand: Brand,
          quantity: Quantity_Required,
          description: Material_Description,
          materialId: Material_ID,
          isPartNumberFound: true,
          // position: Position,
          notes: Notes
        };
      });

      let rfq = {
        client: { label: clientName, value: ClientID },
        deadLine: Deadline?.value,
        deliveryDate: Delivery_Date?.value,
        clientRfqNumber: RFQ_Number,
        title: RFQ_Name,
        priority: Priority,
        additionalNote: Additional_Notes,
        additionalInfo: Address_Information,
        url: URL,
        portal: { label: Portal, value: Portal },
        materials: materialsData
      };
      setMaterials(materialsData);
      setRfqDetails({ ...rfqDetails, ...rfq });
    }
  }, [singleRfq?.data]);

  const addMaterial = () => {
    const newMaterials = [...materials, { id: materials.length + 1, partNumber: '', brand: '', quantity: '', description: '' }];
    setMaterials(newMaterials);
    setErrors({
      ...errors,
      materials: [...errors.materials, { partNumber: '', brand: '', quantity: '', description: '' }]
    });
  };

  const removeMaterial = (index) => {
    const updatedMaterials = materials.filter((_, i) => i !== index);
    setMaterials(updatedMaterials);
    const updatedErrors = [...errors.materials];
    updatedErrors.splice(index, 1);
    setErrors({
      ...errors,
      materials: updatedErrors
    });
  };

  const handleMaterialChange = (index, event) => {
    const { name, value } = event.target;
    const newMaterials = [...materials];
    if (name === 'partNumber') {
      newMaterials[index] = { ...newMaterials[index], brand: '', description: '', [name]: value };
    }
    newMaterials[index] = { ...newMaterials[index], [name]: value };
    setMaterials(newMaterials);
    const newErrors = [...errors.materials];
    newErrors[index][name] = '';
    setErrors({
      ...errors,
      materials: newErrors
    });
  };

  const handleRfqChange = (e, type = '') => {
    const { name, value } = get(e, 'target', {});
    if (type === 'file') {
      const file = e.target.files[0];
      if (file) {
        setRfqDetails({ ...rfqDetails, [name]: file, fileName: file?.name });
      }
    } else {
      setRfqDetails({ ...rfqDetails, [name]: value });
      setErrors({ ...errors, [name]: '' });
    }
  };

  const handleRfqDateChange = (name, value) => {
    setRfqDetails({ ...rfqDetails, [name]: value });
    setErrors({ ...errors, [name]: '' });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const rfqFieldRules = {
      title: { required: true, label: 'Title' },
      client: { required: true, label: 'Client' },
      deadLine: { required: true, label: 'DeadLine' },
    };
    const materialFieldRules = {
      partNumber: { required: true, label: 'Part Number' },
      brand: { required: true, label: 'Brand' },
      quantity: { required: true, label: 'Quantity' ,number:true},
      description: { required: true, label: 'Description' },
    };
    const validation = validate({ ...rfqDetails, client: rfqDetails?.client?.value }, rfqFieldRules);
    const materialValidation = validateNested(materials, materialFieldRules,true);

    const isValid = ((validation && Object.keys(validation)?.length === 0) && (materialValidation && Object.keys(materialValidation)?.length === 0))

    if (isValid) {
      setErrors({
        title: '',
        client: '',
        deadLine: '',
        deliveryDate: '',
        clientRfqNumber: '',
        materials: materials?.map(() => ({
          partNumber: '',
          brand: '',
          quantity: '',
          description: ''
        }))
      });
      const { title, client, deadLine, portal, priority, deliveryDate, clientRfqNumber, url, additionalInfo, additionalNote, notes } =
        rfqDetails || {};
      let updatedMaterial = materials?.map((material) => ({
        // Position: parseInt(material?.position || 0),
        Part_Number: material?.partNumber?.trim() || '',
        brand: material?.brand?.trim()?.toUpperCase(),
        Quantity_Required: parseInt(material?.quantity || 1),
        Notes: material?.notes,
        Material_Description: material?.description,
        Material_ID: material?.materialId?.toString()
      }));
      let newMaterials = updatedMaterial?.filter((newMaterial) => !newMaterial?.Material_ID);
      let oldMaterials = updatedMaterial?.filter((oldMaterial) => oldMaterial?.Material_ID);
      const payload = {
        redirectTo: redirectTo,
        navigate: navigate,
        rfqId: rfqId,
        RFQ_Name: title,
        Company_Name: client?.value,
        Client_Id: client?.value,
        Deadline: deadLine ? convertDateToStringFormat(new Date(deadLine), true) : '',
        Delivery_Date: deliveryDate ? convertDateToStringFormat(new Date(deliveryDate), true) : '',
        RFQ_Number: clientRfqNumber,
        URL: url,
        Portal: portal?.value,
        Priority: priority,
        Address_Information: additionalInfo,
        Additional_Notes: additionalNote
      };
      if (rfqId) {
        dispatch(
          updateRfqAction({
            ...payload,
            newMaterials: newMaterials,
            existingMaterials: oldMaterials
          })
        );
      } else {
        dispatch(addRfqAction({ ...payload, materials: updatedMaterial }));
      }
    } else {

      setErrors({ ...validation, materials: materialValidation });
    }
  };

  const onSelectChange = (value, name) => {
    setRfqDetails({ ...rfqDetails, [name]: value });
    setErrors({ ...errors, [name]: '' });
  };
  const isLoading = () => {
    const { status } = rfqData || {};
    return (
      status === 'loading' ||
      get(rfqData, 'loading', false) ||
      get(singleRfq, 'status', '') === 'loading' ||
      get(singleRfq, 'loading', false)
    );
  };

  const renderField = (field, rfqDetails, handleRfqChange, onSelectChange, handleRfqDateChange, errors) => {
    switch (field.type) {
      case 'text':
        return (
          <InputField
            type="text"
            name={field.name}
            value={rfqDetails[field.name]}
            onChange={handleRfqChange}
            errors={errors}
            fullWidth
          />
        );
      case 'select':
        return (
          <>
            <SelectWithAddNew
              selectedOption={rfqDetails?.[field.name] || ''}
              portalNameList={field.options || []}
              onSelectChange={(value) => onSelectChange(value, field.name)}
              error={errors?.[field.name]}
              label={field.label}
              name={field.name}
            />
            {errors?.[field.name] && <ErrorMessage message={errors?.[field.name]} />}
          </>
        );
      case 'date':
        return (
          <>
            <DatePickerComponent
              startDateHandleChange={(date) => handleRfqDateChange(field.name, date)}
              startDate={rfqDetails[field.name]}
              className={field.className}
              fullWidth
              error={errors?.[field.name]}
            />
            {errors?.[field.name] && <ErrorMessage message={errors?.[field.name]} />}
          </>
        );
      case 'file':
        return (
          <div style={{ display: 'inline-grid' }}>
            <Button
              variant="info"
              component="label"
              sx={{ backgroundColor: 'rgb(178, 178, 178)', color: 'black', fontWeight: '500', width: '100%' }}
            >
              Browse
              <input type="file" hidden onChange={(e) => handleRfqChange(e, 'file')} />
            </Button>
            <Typography variant="subtitle1" fontWeight={600} ml={1}>
              {get(rfqDetails, 'fileName', '') || ''}
            </Typography>
          </div>
        );
      default:
        return null;
    }
  };

  const handleSearchPartNumber = async (index, material) => {
    const { partNumber, } = material || {}
    if (partNumber && partNumber?.length >= 3) {

      const result = await dispatch(
        searchMaterialAction({ partNumber: partNumber?.trim() || '', isSimilar: false })
      );

      const { success, data } = get(result, 'payload');
      if (success) {
        const { manufacturer, description } = data || {}
        const newMaterials = [...materials];
        newMaterials[index] = { ...newMaterials[index], brand: manufacturer, description: description, isPartNumberFound: true };
        setMaterials(newMaterials);
      }

    } else {
      let message = !partNumber
        ? 'Part Number is required'
        : partNumber?.length < 3
          ? 'Part Number should contain at least 3 characters'
          : '';
      showAlert(dispatch, false, message, true);
    }

  }

  const removePartNumber = (index) => {
    const newMaterials = [...materials];
    newMaterials[index] = { ...newMaterials[index], brand: '', description: '', isPartNumberFound: false, partNumber: '' };
    setMaterials(newMaterials);
  }

  const fields = [
    { type: 'text', name: 'title', label: 'Title', sm: 6 },
    { type: 'select', name: 'client', label: 'Client', sm: 6, options: getApprovedClients(clientList) },
    { type: 'date', name: 'deadLine', label: 'Deadline', sm: 3, className: 'add-rfq-deadLine-date' },
    { type: 'date', name: 'deliveryDate', label: 'Delivery Date', sm: 3, className: 'add-rfq-delivery-date' },
    { type: 'text', name: 'clientRfqNumber', label: 'Client RFQ Number (optional)', sm: 3 },
    { type: 'text', name: 'priority', label: 'Priority', sm: 3 },
    { type: 'text', name: 'additionalNote', label: 'Add Notes', sm: 3 },
    { type: 'file', name: 'attachment', label: 'Attachment (optional)', sm: 3 },
  ];

  const materialFields = [
    { name: 'partNumber', label: 'Part Number', type: 'partNumber', sm: 3, isDisabled: 'isPartNumberFound' },
    { name: 'brand', label: 'Brand', type: 'text', sm: 2 },
    { name: 'description', label: 'Description', type: 'text', sm: 2 },
    { name: 'quantity', label: 'Quantity', type: 'integerNumber', sm: 2 },
    { name: 'notes', label: 'Notes', type: 'text', sm: 2 }
  ];

  const renderMaterialField = (field, material, index, handleMaterialChange, errors) => {
    const { isDisabled, type, name } = field || {}
    switch (type) {
      case 'text':
      case 'number':
        return (
          <InputField
            type={type}
            name={name}
            value={material[name]}
            onChange={(e) => handleMaterialChange(index, e)}
            errorMessage={errors?.materials?.[index]?.[name]}
            fullWidth
          />
        );
      case 'integerNumber':
        return (
          <InputField
            type={type}
            name={name}
            value={material[name]}
            onChange={(e) => {
              handleMaterialChange(index, {
                target: {
                  name: e.target.name,
                  value: parseInt(e.target.value) || 0 // Parse as integer, fallback to an empty string
                }
              });
            }}
            errorMessage={errors?.materials?.[index]?.[name]}
            fullWidth
          />
        );
      case 'partNumber':
        const error = errors?.materials?.[index]?.[name]
        const isPartNumberFound = material[isDisabled]
        return (
          <Box display="flex" alignItems={!error && "center"}>
            <div>
              <InputField
                type="text"
                name={name}
                disabled={isPartNumberFound}
                value={material[name]}
                onChange={(e) => handleMaterialChange(index, e)}
                errorMessage={error}
                fullWidth
              />
            </div>
            <Button
              variant="contained"
              color={isPartNumberFound ? 'error' : "primary"}
              size="small"
              onClick={() => isPartNumberFound ? removePartNumber(index, material) : handleSearchPartNumber(index, material)}
              sx={{ ml: 2 }}
            >
              {isPartNumberFound ? 'Cancel' : 'Search'}
            </Button>
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Grid>
      {isLoading() && <Loader />}
      <Box component="form" onSubmit={handleSubmit}>
        <MainCard className="maincard-boder" boxShadow={true}>
          <Typography variant="h4" component="h1" gutterBottom color="secondary">
            {rfqId ? 'Update RFQ' : 'New RFQ'}
          </Typography>

          <Grid container spacing={3} mt={1}>
            {fields.map((field) => (
              <Grid item xs={12} sm={field.sm} key={field.name}>
                <Typography variant="subtitle1" gutterBottom>
                  {field.label}
                </Typography>
                {renderField(field, rfqDetails, handleRfqChange, onSelectChange, handleRfqDateChange, errors)}
              </Grid>
            ))}
          </Grid>
        </MainCard>

        <MainCard sx={{ mt: 3 }} className="maincard-boder" boxShadow={true}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="h4" component="h1" gutterBottom color="secondary">
                Material Details
              </Typography>
            </Grid>

            <Grid item xs={12}>
              {materials?.map((material, index) => (
                <Grid container spacing={2} key={index} pt={1}>
                  <Grid item>
                    <Typography variant="subtitle1" mb="20px">
                      ID
                    </Typography>
                    <Typography variant="subtitle1" gutterBottom textAlign="center">
                      {material?.id}
                    </Typography>
                  </Grid>

                  {materialFields.map((field) => (
                    <Grid item xs={12} sm={field.sm} key={field.name}>
                      <Typography variant="subtitle1" gutterBottom>
                        {field.label}
                      </Typography>
                      {renderMaterialField(field, material, index, handleMaterialChange, errors)}
                    </Grid>
                  ))}

                  {index !== 0 && (
                    <Grid item mt={4} >
                      <IconButton onClick={() => removeMaterial(index)} color="secondary">
                        <CloseIcon color="error" />
                      </IconButton>
                    </Grid>
                  )}
                </Grid>
              ))}
            </Grid>


            <Grid item xs={12}>
              <Button variant="contained" onClick={addMaterial} color="primary">
                Add Another Material
              </Button>
            </Grid>
          </Grid>
        </MainCard>
        <MainCard sx={{ mt: 3 }} className="maincard-boder" boxShadow={true}>
          <Grid item spacing={3} display="flex" justifyContent="flex-end">
            <Link to={redirectTo} className="link">
              <Button variant="outlined" color="secondary" >
                Cancel
              </Button>
            </Link>
            <Button variant="contained" color="primary" type="submit" disabled={isLoading()} style={{ marginLeft: '10px' }}>
              Submit
            </Button>

          </Grid>
        </MainCard>
      </Box>
    </Grid>
  );
};

export default AddEditRfq;