.secondary-contatiner {
  display: flex;
}

.material-brand {
  margin-left: 30px;
}

.generateOffer-input {
  margin-right: 10px;
}

.exclude-input {
  display: flex;
  position: absolute;
  right: 10px;
  top: 10px;
}

.card-content {
  width: 100%;
  overflow: hidden;
  position: relative;
}

.add-material-offer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border: none;

  cursor: pointer;
  border-radius: 50%;
  margin-top: 50%;
}

.request-btn {
  margin-right: 10px;
}

.disable-add-material {
  background-color: #1677ff;
}

.add-icon {
  width: 24px;
  /* Adjust icon size as needed */
  height: 24px;
  /* Adjust icon size as needed */
  fill: #fff;
  /* Adjust icon color as needed */
}

.over-view-container {
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
}

.close-btn {
  width: 100%;
}

.material-table-card {
  margin-top: 10px;
}

.request-quote-wrapper {
  display: flex;
  gap: 10px;
}

.material-description-wrapper {
  width: 400px;
}

.table-cardHeader-class {
  display: flex;
  justify-content: space-between;
}

.table-badge {
  margin-left: 50px;
}

.price-status {
  margin-left: 10px;
}

.textarea-field {
  width: 100%;
  margin-left: 5px;
  padding: 4px;
}

.request-price-container {
  min-width: 400px;
  padding: 20px;
}

.requestprice-textarea-field {
  width: 100%;
  padding: 10px;
  border-radius: 8px;
  border: 1px solid #ccc;
  background-color: #fff;
  font-size: 13px;
}

.textarea-field::placeholder {
  color: #aaa;
}

.typography-title {
  text-align: center;
  margin-bottom: 16px;
}

.typography-body {
  color: #6c757d;
  /* secondary text color */
}

.request-quote-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.material-description-wrapper {
  max-width: 200px;
  /* Limit the width */
  flex: 1;
}

.material-description {
  word-wrap: break-word;
  margin-bottom: 0.5rem;
}

@media (max-width: 600px) {
  .request-quote-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }

  .material-description-wrapper {
    max-width: 100%;
  }
}
