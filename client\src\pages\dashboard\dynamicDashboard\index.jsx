import Loader from 'components/Loader';
import React, { useEffect, useState } from 'react';

const DashboardUI = ({ src }) => {
  const [height, setHeight] = useState(window.innerHeight);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const handleResize = () => {
      setHeight(window.innerHeight);
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleLoad = () => {
    setLoading(false);
  };
  return (
    <>
      {loading && <Loader />}
      <iframe
        width="100%"
        height={height}
        src={src}
        frameBorder="0"
        style={{ border: 0 }}
        onLoad={handleLoad}
        allowFullScreen
        sandbox="allow-storage-access-by-user-activation allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
      ></iframe>
    </>
  );
};

export default DashboardUI;
