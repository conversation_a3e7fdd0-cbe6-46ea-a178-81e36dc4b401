const { DATASET_ID_SCRAPE, DATASET_ID_MAIN } = require("../constants");
const {
  bigQueryClient,
  mainDataset,
  scrapedDataset,
  landingZoneDataset,
} = require("../db");
const { apiHandler, apiError, apiResponse } = require("../utils/api.util");
const {
  NOT_FOUND,
  FETCH,
  CONFIRM_SUCCESS,
  ADD_SUCCESS,
  CUSTOM_ERROR,
  UPDATE_SUCCESS,
} = require("../utils/message.util");
const { isEmpty, split, setDate, generateID } = require("../utils/misc.util");

const confirmMaterial = apiHandler(async (req, res) => {
  const { materialId, partNumber, brand, description, rfqId } = req.body;

  const [existingMaterial] = await mainDataset.query({
    query: `
      SELECT manufacturer_catalog_number
      FROM partnumbers
      WHERE manufacturer_catalog_number = @partNumber
    `,
    params: {
      partNumber: partNumber,
    },
  });

  if (isEmpty(existingMaterial)) {
    return apiError(
      CUSTOM_ERROR,
      "Material not found. Please save it as new",
      null,
      res
    );
  }

  const [confirmMaterial] = await mainDataset.query({
    query: `
      SELECT * FROM Predictions_Confirmations
      WHERE MaterialID = @materialId AND RFQID = @rfqId
    `,
    params: {
      partNumber,
      brand,
      materialId: parseInt(materialId),
      rfqId: parseInt(rfqId),
    },
  });

  if (isEmpty(confirmMaterial)) {
    const [createdPrediction] = await mainDataset.query({
      query: `
        INSERT INTO Predictions_Confirmations (MaterialID, RFQID, ExtractedBrand, ConfirmedBrand, 
                    ExtractedPartNumber, ConfirmedPartNumber, PredictionDate, PredictionStatus, 
                    ConfirmationDate, ConfirmationStatus)
        VALUES (@materialId, @rfqId, @brand, @brand, 
                @partNumber, @partNumber, @date, @status, 
                @date, @status)
      `,
      params: {
        rfqId: parseInt(rfqId),
        materialId: parseInt(materialId),
        partNumber,
        brand,
        date: setDate(),
        status: "Done",
      },
    });
  } else {
    const [confirmedMaterial] = await mainDataset.query({
      query: `
        UPDATE Predictions_Confirmations
        SET ConfirmedPartNumber = @partNumber, ConfirmedBrand = @brand,
            ConfirmationStatus = 'Done', ConfirmationDate = '${setDate()}'
        WHERE MaterialID = @materialId
      `,
      params: {
        partNumber,
        brand,
        materialId: parseInt(materialId),
      },
    });
  }

  const [material] = await scrapedDataset.query({
    query: `
      UPDATE Material
      SET Part_Number = @partNumber, Material_Description = @description
      WHERE Material_ID = @materialId AND RFQ_ID = @rfqId
    `,
    params: {
      partNumber,
      description,
      materialId: parseInt(materialId),
      rfqId: parseInt(rfqId),
    },
  });

  return apiResponse(CONFIRM_SUCCESS, "Material", null, res);
});

const searchMaterial = apiHandler(async (req, res) => {
  const { partNumber, isSimilar } = req.body;

  let [[materials]] = await mainDataset.query({
    query: `
      SELECT manufacturer_catalog_number as partNumber, product_name as name, manufacturer_name as manufacturer, description FROM partnumbers
      WHERE TRIM(manufacturer_catalog_number) = @partNumber
    `,
    params: {
      partNumber: partNumber.trim(),
    },
  });

  if (isEmpty(materials)) {
    return apiError(
      CUSTOM_ERROR,
      "Part Number not found. Please search manually",
      null,
      res
    );
  }

  let whereExpression = `WHERE`;
  if (isSimilar) {
    const splittedPartNumber = split(partNumber);
    splittedPartNumber.forEach((part) => {
      whereExpression += ` OR manufacturer_catalog_number LIKE '%${part}%'`;
    });
    whereExpression += ` OR (manufacturer_catalog_number = @partNumber)`;
    whereExpression = whereExpression.replace("WHERE OR", "WHERE");

    let [materials] = await mainDataset.query({
      query: `
        SELECT manufacturer_catalog_number as partNumber, product_name as name, manufacturer_name as manufacturer  FROM partnumbers
        ${whereExpression}
      `,
      params: {
        partNumber,
      },
    });

    if (isEmpty(materials)) {
      return apiError(
        CUSTOM_ERROR,
        "Similar Part Number not found. Please add as new material",
        null,
        res
      );
    }

    return apiResponse(FETCH, "Material", materials, res);
  }

  return apiResponse(FETCH, "Material", materials, res);
});

const addMaterial = apiHandler(async (req, res) => {
  let {
    materialId,
    rfqId,
    partNumber,
    manufacturer,
    description,
    productName,
  } = req.body;

  partNumber = partNumber.trim();
  manufacturer = manufacturer.trim().toUpperCase();

  const [existingMaterial] = await mainDataset.query({
    query: `
      SELECT manufacturer_catalog_number
      FROM partnumbers
      WHERE TRIM(manufacturer_catalog_number) = @partNumber
    `,
    params: {
      partNumber: partNumber,
    },
  });
  if (!isEmpty(existingMaterial)) {
    return apiError(
      CUSTOM_ERROR,
      "Material already exists. Please confirm or edit the material instead",
      null,
      res
    );
  }

  const id = generateID();

  const [createdPartNumber] = await mainDataset.query({
    query: `
        INSERT INTO partnumbers (id, manufacturer_catalog_number, product_name, manufacturer_name, description)
        VALUES (@id, @partNumber, @productName, @brand, @description)
      `,
    params: {
      id: id.toString(),
      partNumber,
      productName: productName || "",
      brand: manufacturer,
      description,
    },
  });

  const [deletePreviousIncorrectEntry] = await mainDataset.query({
    query: `
        DELETE FROM Predictions_Confirmations 
        WHERE MaterialID = @materialId AND RFQID = @rfqId
      `,
    params: {
      materialId: parseInt(materialId),
      rfqId: parseInt(rfqId),
    },
  });

  const [createdPrediction] = await mainDataset.query({
    query: `
      INSERT INTO Predictions_Confirmations (MaterialID, RFQID, ExtractedBrand, ConfirmedBrand, 
                  ExtractedPartNumber, ConfirmedPartNumber, PredictionDate, PredictionStatus, 
                  ConfirmationDate, ConfirmationStatus)
      VALUES (@materialId, @rfqId, @brand, @brand, 
              @partNumber, @partNumber, @date, @status, 
              @date, @status)
    `,
    params: {
      rfqId: parseInt(rfqId),
      materialId: parseInt(materialId),
      partNumber,
      brand: manufacturer,
      date: setDate(),
      status: "Done",
    },
  });

  return apiResponse(ADD_SUCCESS, "Material", null, res);
});

const editMaterial = apiHandler(async (req, res) => {
  const {
    materialId,
    rfqId,
    partNumber,
    manufacturer,
    description,
    productName,
  } = req.body;

  const [existingMaterial] = await mainDataset.query({
    query: `
      SELECT manufacturer_catalog_number
      FROM partnumbers
      WHERE manufacturer_catalog_number = @partNumber
    `,
    params: {
      partNumber: partNumber,
    },
  });

  if (isEmpty(existingMaterial)) {
    return apiError(
      CUSTOM_ERROR,
      "No Material with part number found",
      null,
      res
    );
  }

  const [updatedPartNumber] = await mainDataset.query({
    query: `
        UPDATE partnumbers SET
        product_name = @productName,
        manufacturer_name= @brand, 
        description= @description
        WHERE manufacturer_catalog_number=@partNumber

      `,
    params: {
      partNumber,
      productName: productName || "",
      brand: manufacturer,
      description,
    },
  });

  return apiResponse(UPDATE_SUCCESS, "Material", null, res);
});

const materialHistory = apiHandler(async (req, res) => {
  const { id } = req.params;
  const today = new Date().toISOString().split("T")[0]; // Format: 'YYYY-MM-DD'

  const [materialHistory] = await bigQueryClient.query({
    query: `
      SELECT m.Material_Description, m.Part_Number, m.RFQ_ID, m.Material_ID, 
            pc.ConfirmedPartNumber, s.ContactName, s.ContactLastname, s.Name,
            q.*, o.OfferCurrency, o.Margin, o.Status
      FROM ${DATASET_ID_SCRAPE}.Material as m
      LEFT JOIN ${DATASET_ID_MAIN}.Predictions_Confirmations as pc ON m.Part_Number = pc.ConfirmedPartNumber
      LEFT JOIN ${DATASET_ID_MAIN}.Quotes as q ON pc.MaterialID = q.MaterialID
      LEFT JOIN ${DATASET_ID_MAIN}.Offers as o ON q.QuoteID = o.QuoteID
      LEFT JOIN ${DATASET_ID_MAIN}.Suppliers as s ON q.SupplierID = s.SupplierID
      WHERE m.Material_ID = @materialId AND q.DeliveryDate <= @todayDate
    `,
    params: {
      materialId: parseInt(id),
      todayDate: today,
    },
  });
  if (isEmpty(materialHistory)) {
    return apiError(NOT_FOUND, "Material History", null, res);
  }

  let groupedData = {};
  materialHistory.forEach((material) => {
    const {
      Material_Description,
      ConfirmedPartNumber,
      Part_Number,
      RFQID,
      RFQ_ID,
      MaterialID,
      Material_ID,
      ...history
    } = JSON.parse(JSON.stringify(material));

    if (isEmpty(groupedData)) {
      groupedData = {
        MaterialDescription: Material_Description,
        PartNumber: ConfirmedPartNumber || Part_Number,
        RFQID: RFQID || RFQ_ID,
        MaterialID: MaterialID || Material_ID,
        History: [],
      };
    }
    if (!isEmpty(history.QuoteID)) {
      groupedData.History.push(history);
    }
  });

  let quoteIds = [];
  let quoteData = [];

  for (const data of groupedData.History) {
    if (!quoteIds.includes(data.QuoteID)) {
      quoteIds.push(data.QuoteID);
      quoteData.push(data);
    }
  }

  groupedData.History = quoteData;

  return apiResponse(FETCH, "Material History", groupedData, res);
});

const getSpecsSheet = apiHandler(async (req, res) => {
  const { partNumber } = req.params;

  const [specsSheets] = await landingZoneDataset.query({
    query: `
      SELECT pd.group_items_name ,pd.group_items_display_name 
      FROM products_downloads as pd 
      INNER JOIN products_master as pm 
      ON pd.product_id = pm.product_id
      WHERE pm.manufacturer_catalog_number=@partNumber
    `,
    params: {
      partNumber: partNumber.toString(),
    },
  });

  if (isEmpty(specsSheets)) {
    return apiError(NOT_FOUND, "Specs Sheets", null, res);
  }

  return apiResponse(FETCH, "Specs Sheets", specsSheets, res);
});

module.exports = {
  confirmMaterial,
  searchMaterial,
  addMaterial,
  materialHistory,
  getSpecsSheet,
  editMaterial,
};
