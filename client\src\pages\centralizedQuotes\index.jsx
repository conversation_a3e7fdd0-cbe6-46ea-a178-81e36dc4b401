import { useEffect, useState, useCallback } from "react";
import PriceTable from "./priceTable";
import RequestedPrice from "./requestedPrice";
import SupplierPriceRequest from "./supplierPriceRequest";
import { defaultFilterPayload, FORMREQUEST, PRICETABLE, REQUESTEDPRICE } from "./constant";
import './centralized.css';
import Header from "./header";
import { useDispatch, useSelector } from "react-redux";
import { addPageTitle } from "redux/reducers/pageTitleReducer";
import { getAllCentralizedRequestAction } from "redux/reducers/centralizedReducer";
import { getSupplierList } from "redux/reducers/offerReducer";
import { get } from "lodash";
import Loader from "components/Loader";
import OpenRequest from "./requestedPrice/openRequest";
import { createCentralizedFilterPayload } from "./header/filter";

const CentralizedQuotes = () => {
  const dispatch = useDispatch();
  const requestedPriceData = useSelector((state) => state.requestedPrice || []);
  const requestedQuote = useSelector((state) => state.requestedQuote || []);
  const supplierListData = useSelector((state) => state.offer?.supplierList || []);
  const [supplierList, setSupplierList] = useState([]);
  const [formData, setFormData] = useState({
    changeComponent: REQUESTEDPRICE,
    filters: {
      page: 1,
    },
    requestedPrice: {
    }
  });

  useEffect(() => {
    const { page, limit, filterValues } = formData?.filters || {}

    dispatch(getAllCentralizedRequestAction({ ...defaultFilterPayload, page: page, limit: limit || 10, filterData: createCentralizedFilterPayload(filterValues)?.filterData }));
  }, [formData?.filters]);

  useEffect(() => {
    dispatch(getSupplierList());
  }, []);

  useEffect(() => {
    const { page, limit } = formData?.filters || {}

    const updatedPriceRequest = get(requestedPriceData, 'data.filteredCentralizedRequests', [])?.map((request) => {
      const {
        ID: id,
        Brand: brand,
        Date: date,
        KAM: kam,
        Notes: notes,
        PartNumber: partNumber,
        RFQ_ID: rfqId,
        dueDate = request?.Material_Information?.Deadline?.value || request?.dueDate,
        quantity = request?.Material_Information?.quantity,
        description = request?.Material_Information?.Material_Description,
        date: requestDate = request?.Date?.value || request?.dateValue,
        isSupplierAvailable = request?.filteredExistingRequests?.length > 0,
        select = formData?.requestedPrice?.selectedBrands?.find((selectedBrand) => selectedBrand?.id === request?.ID)?.select || false
      } = request;

      return { ...request, select, id, brand, date: requestDate, description, dueDate, kam, notes, partNumber, quantity, rfqId, isSupplierAvailable };
    });
    const totalCount = get(requestedPriceData, 'data.count', 0);

    setFormData((prev) => ({
      ...prev,
      requestedPrice: { ...prev.requestedPrice, totalCount: totalCount > 0 ? totalCount : 0, tableData: updatedPriceRequest },
    }));

  }, [requestedPriceData?.data]);

  const onPageChange = (page) => {
    setFormData({ ...formData, filters: { ...formData?.filters, page: page + 1 } })
  }
  const onPageLimitChange = (limit) => {
    setFormData({ ...formData, filters: { ...formData?.filters, limit: limit } })
  }

  const renderComponent = useCallback(() => {
    const { changeComponent, requestedPrice } = formData || {}

    switch (changeComponent) {

      case PRICETABLE:
        return <PriceTable
          formData={formData}
          setFormData={setFormData}
          suppliers={supplierList} />;

      case FORMREQUEST:
        return <SupplierPriceRequest
          formData={formData}
          setFormData={setFormData}
        />;

      default:
        return requestedPrice?.open ? (
          <OpenRequest
            formData={formData}
            setFormData={setFormData} />
        ) : (
          <RequestedPrice
            formData={formData}
            onPageChange={onPageChange}
            onPageLimitChange={onPageLimitChange}
            setFormData={setFormData}
          />
        );
    }
  }, [formData]);


  const getHeading = useCallback((component) => {
    switch (component) {
      case PRICETABLE:
        return 'Price List';
      case FORMREQUEST:
        return 'Supplier Price Request';
      case REQUESTEDPRICE:
      default:
        return 'Requested Price';
    }
  }, []);


  useEffect(() => {
    dispatch(addPageTitle(getHeading(formData?.changeComponent)));
  }, [formData?.changeComponent]);


  useEffect(() => {
    const updatedSupplierList = supplierListData?.filter((supplier) => supplier?.brands?.length > 0)?.map((supplier) => ({
      ...supplier,
      label: supplier?.Name,
      id: supplier?.SupplierID,
      Status: supplier?.Status
    }));

    setSupplierList(updatedSupplierList);
  }, [supplierListData]);


  const isLoading = () => {
    return requestedPriceData?.loading || requestedQuote?.loading || requestedPriceData?.status === 'loading'
  }

  const { open } = formData?.requestedPrice || {}
  return (
    <>

      {isLoading() && <Loader />}
      {!open && <Header setFormData={setFormData} formData={formData} supplierList={supplierList} loading={isLoading()} />}
      <div className="component">
        {renderComponent()}
      </div>
    </>
  );
};

export default CentralizedQuotes;
