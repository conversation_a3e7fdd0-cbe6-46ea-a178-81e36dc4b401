const { BASE_URL } = require("../constants");
const { isEmpty } = require("./misc.util");

const newClientTemplate = {
  emailSubject: (data) => {
    const { clientName } = data;
    return `New Client Approval Needed: ${clientName}`;
  },

  htmlTemplate: (data) => {
    const {
      token,
      approverName,
      ClientID,
      clientName,
      Created_At,
      Address,
      Municipality,
      BusinessActivity,
      Created_By,
      Postal_Code,
      State,
      Country,
      Region
    } = data;

    return `<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f0f0f5;
      margin: 0;
      padding: 0;
    }

    .email-container {
      background-color: #ffffff;
      margin: 40px auto;
      padding: 30px;
      max-width: 600px;
      border-radius: 12px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    h2 {
      color: #333333;
      font-size: 24px;
      margin-bottom: 15px;
    }

    p {
      color: #555555;
      line-height: 1.8;
      font-size: 16px;
      margin-bottom: 20px;
    }

    .details-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .details-table th,
    .details-table td {
      text-align: left;
      padding: 10px;
      border: 1px solid #ddd;
      font-size: 15px;
    }

    .details-table th {
      background-color: #f8f8f8;
      font-weight: bold;
    }

    .btn {
      display: inline-block;
      padding: 12px 20px;
      margin: 20px 10px 0 0;
      font-size: 16px;
      color: #ffffff;
      text-decoration: none;
      border-radius: 6px;
      transition: background-color 0.3s ease;
    }

    .btn-approve {
      background-color: #4CAF50;
    }

    .btn-approve:hover {
      background-color: #45a049;
    }

    .btn-reject {
      background-color: #f44336;
    }

    .btn-reject:hover {
      background-color: #d73833;
    }

    .footer {
      margin-top: 30px;
      font-size: 14px;
      color: #888888;
    }

    .footer a {
      color: #4CAF50;
      text-decoration: none;
    }

    .footer a:hover {
      text-decoration: underline;
    }

    .logo {
      text-align: center;
      margin-bottom: 20px;
    }

    .logo img {
      max-width: 150px;
      height: auto;
    }
  </style>
</head>

<body>

  <div class="email-container">
    <div class="logo">
      <img src="https://assets.api.uizard.io/api/cdn/stream/32f175ca-32be-40d0-8832-044fb93d8edf.png"
        alt="Company Logo">
    </div>
    <h2>New Client Approval Request: ${clientName}</h2>

    <p>Hello ${approverName},</p>

    <p>A new client has been added to the system and requires your approval. Please review the details below:</p>

    <!-- Client Details -->
    <table class="details-table">
      <tr>
        <th>Client ID</th>
        <td>${ClientID}</td>
      </tr>
      <tr>
        <th>Name</th>
        <td>${clientName}</td>
      </tr>
      <tr>
        <th>Address</th>
        <td>${Address}</td>
      </tr>
      <tr>
        <th>Municipality</th>
        <td>${Municipality}</td>
      </tr>
      <tr>
        <th>Business Activity</th>
        <td>${BusinessActivity}</td>
      </tr>
       <tr>
        <th>Postal Code</th>
        <td>${Postal_Code}</td>
      </tr>
      <tr>
        <th>State</th>
        <td>${State}</td>
      </tr>
      <tr>
        <th>Country</th>
        <td>${Country}</td>
      </tr>
      <tr>
        <th>Region</th>
        <td>${Region}</td>
      </tr>
      <tr>
        <th>Created At</th>
        <td>${Created_At}</td>
      </tr>
            <tr>
        <th>Created By</th>
        <td>${Created_By}</td>
      </tr>
    </table>

    <p>You can take action by clicking one of the buttons below:</p>

    <a href="${BASE_URL}/client-approve/${ClientID}/${token}" class="btn btn-approve"
      style="display: inline-block; padding: 12px 20px; margin: 20px 10px 0 0; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 6px; background-color: #4CAF50;">
      Approve Client
    </a>

    <a href="${BASE_URL}/client-reject/${ClientID}/${token}" class="btn btn-reject"
      style="display: inline-block; padding: 12px 20px; margin: 20px 10px 0 0; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 6px; background-color: #f44336;">
      Reject Client
    </a>

    <p>If you choose to reject the client, you will be prompted to provide a reason for the rejection.</p>

    <div class="footer">
      <p>Best regards,<br />Remiex</p>
    </div>
  </div>

</body>

</html>
    `;
  },
};

const updateClientTemplate = {
  emailSubject: (data) => {
    const { Name } = data.oldRecord;
    return `Client Updated : ${Name}`;
  },

  htmlTemplate: (data) => {
    const { approverName, oldRecord, newRecord, token, ClientID } = data;

    return `<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f0f0f5;
      margin: 0;
      padding: 0;
    }

    .email-container {
      background-color: #ffffff;
      margin: 40px auto;
      padding: 30px;
      max-width: 600px;
      border-radius: 12px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    h2 {
      color: #333333;
      font-size: 24px;
      margin-bottom: 15px;
    }

    p {
      color: #555555;
      line-height: 1.8;
      font-size: 16px;
      margin-bottom: 20px;
    }

    .details-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .details-table th,
    .details-table td {
      text-align: left;
      padding: 10px;
      border: 1px solid #ddd;
      font-size: 15px;
    }

    .details-table th {
      background-color: #f8f8f8;
      font-weight: bold;
    }

    .details-table .old {
      background-color: #f9f9f9;
      text-decoration: line-through;
    }

    .btn {
      display: inline-block;
      padding: 12px 20px;
      margin: 20px 10px 0 0;
      font-size: 16px;
      color: #ffffff;
      text-decoration: none;
      border-radius: 6px;
      transition: background-color 0.3s ease;
    }

    .btn-approve {
      background-color: #4CAF50;
    }

    .btn-approve:hover {
      background-color: #45a049;
    }

    .btn-reject {
      background-color: #f44336;
    }

    .btn-reject:hover {
      background-color: #d73833;
    }

    .footer {
      margin-top: 30px;
      font-size: 14px;
      color: #888888;
    }

    .footer a {
      color: #4CAF50;
      text-decoration: none;
    }

    .footer a:hover {
      text-decoration: underline;
    }

    .logo {
      text-align: center;
      margin-bottom: 20px;
    }

    .logo img {
      max-width: 150px;
      height: auto;
    }
  </style>
</head>

<body>

  <div class="email-container">
    <div class="logo">
      <img src="https://assets.api.uizard.io/api/cdn/stream/32f175ca-32be-40d0-8832-044fb93d8edf.png"
        alt="Company Logo">
    </div>
    <h2>Client Updated: ${oldRecord.Name}</h2>

    <p>Hello ${approverName}</p>

    <p>We want to inform you that the details of a client have been updated. Please review the updated information below:</p>
    <table class="details-table">
      <tr>
        <th>Field</th>
        <th>Old Value</th>
        <th>New Value</th>
      </tr>
      <tr>
        <td>Client Name</td>
        <td >${oldRecord.Name}</td>
        <td>${newRecord.Name}</td>
      </tr>
      <tr>
        <td>Address</td>
        <td >${oldRecord.Address}</td>
        <td>${newRecord.Address}</td>
      </tr>
      <tr>
        <td>Municipality</td>
        <td >${oldRecord.Municipality}</td>
        <td>${newRecord.Municipality}</td>
      </tr>
      <tr>
        <td>Business Activity</td>
        <td >${oldRecord.BusinessActivity}</td>
        <td>${newRecord.BusinessActivity}</td>
      </tr>
      <tr>
        <td>Post Code</td>
        <td >${oldRecord.Postal_Code}</td>
        <td>${newRecord.Postal_Code}</td>
      </tr>
      <tr>
        <td>State</td>
        <td >${oldRecord.State}</td>
        <td>${newRecord.State}</td>
      </tr>
      <tr>
        <td>Country</td>
        <td >${oldRecord.Country}</td>
        <td>${newRecord.Country}</td>
      </tr>
      <tr>
        <td>Region</td>
        <td >${oldRecord.Region}</td>
        <td>${newRecord.Region}</td>
      </tr>
      <tr>
        <td>Created At</td>
        <td >${isEmpty(oldRecord.Created_At) ? "" : oldRecord.Created_At}</td>
        <td>${isEmpty(oldRecord.Created_At) ? "" : oldRecord.Created_At}</td>
      </tr>
      <tr>
        <td>Updated At</td>
        <td >${isEmpty(oldRecord.Updated_At) ? "" : oldRecord.Updated_At}</td>
        <td>${newRecord.Updated_At}</td>
      </tr>
      <tr>
        <td>Created By</td>
        <td >${isEmpty(oldRecord.Created_By) ? "" : oldRecord.Created_By}</td>
        <td>${isEmpty(oldRecord.Created_By) ? "" : oldRecord.Created_By}</td>
      </tr>
      <tr>
        <td>Updated By</td>
        <td >${isEmpty(oldRecord.Updated_By) ? "" : oldRecord.Updated_By}</td>
        <td>${newRecord.Updated_By}</td>
      </tr>
    </table>

    <p>You can take action by clicking one of the buttons below:</p>

    <a href="${BASE_URL}/client-approve/${ClientID}/${token}" class="btn btn-approve"
      style="display: inline-block; padding: 12px 20px; margin: 20px 10px 0 0; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 6px; background-color: #4CAF50;">
      Approve Client
    </a>

    <a href="${BASE_URL}/client-reject/${ClientID}/${token}" class="btn btn-reject"
      style="display: inline-block; padding: 12px 20px; margin: 20px 10px 0 0; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 6px; background-color: #f44336;">
      Reject Client
    </a>

    <p>If you choose to reject the client, you will be prompted to provide a reason for the rejection.</p>

    <div class="footer">
      <p>Best regards,<br />Remiex</p>
    </div>
  </div>

</body>

</html>
`;
  },
};

const newSupplierTemplate = {
  emailSubject: (data) => {
    const { Name } = data;
    return `New Supplier Approval Needed: ${Name}`;
  },

  htmlTemplate: (data) => {
    const {
      approverName,
      token,
      Date,
      Shipping,
      Country,
      Email,
      ContactLastname,
      ContactName,
      Name,
      SupplierID,
      Web,
      Notes,
      Created_By,
      Created_At,
    } = data;

    return `<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f0f0f5;
      margin: 0;
      padding: 0;
    }

    .email-container {
      background-color: #ffffff;
      margin: 40px auto;
      padding: 30px;
      max-width: 600px;
      border-radius: 12px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    h2 {
      color: #333333;
      font-size: 24px;
      margin-bottom: 15px;
    }

    p {
      color: #555555;
      line-height: 1.8;
      font-size: 16px;
      margin-bottom: 20px;
    }

    .details-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .details-table th,
    .details-table td {
      text-align: left;
      padding: 10px;
      border: 1px solid #ddd;
      font-size: 15px;
    }

    .details-table th {
      background-color: #f8f8f8;
      font-weight: bold;
    }

    .btn {
      display: inline-block;
      padding: 12px 20px;
      margin: 20px 10px 0 0;
      font-size: 16px;
      color: #ffffff;
      text-decoration: none;
      border-radius: 6px;
      transition: background-color 0.3s ease;
    }

    .btn-approve {
      background-color: #4CAF50;
    }

    .btn-approve:hover {
      background-color: #45a049;
    }

    .btn-reject {
      background-color: #f44336;
    }

    .btn-reject:hover {
      background-color: #d73833;
    }

    .footer {
      margin-top: 30px;
      font-size: 14px;
      color: #888888;
    }

    .footer a {
      color: #4CAF50;
      text-decoration: none;
    }

    .footer a:hover {
      text-decoration: underline;
    }

    .logo {
      text-align: center;
      margin-bottom: 20px;
    }

    .logo img {
      max-width: 150px;
      height: auto;
    }
  </style>
</head>

<body>

  <div class="email-container">
    <div class="logo">
      <img src="https://assets.api.uizard.io/api/cdn/stream/32f175ca-32be-40d0-8832-044fb93d8edf.png" alt="Company Logo">
    </div>
    <h2>New Supplier Approval Request: ${Name}</h2>

    <p>Hello ${approverName},</p>

    <p>A new Supplier has been added to the system and requires your approval. Please review the details below:</p>

    <table class="details-table">
      <tr>
        <th>Supplier ID</th>
        <td>${SupplierID}</td>
      </tr>
      <tr>
        <th>Name</th>
        <td>${Name}</td>
      </tr>
      <tr>
        <th>Contact First Name</th>
        <td>${ContactName}</td>
      </tr>
      <tr>
        <th>Contact Last Name</th>
        <td>${ContactLastname}</td>
      </tr>
      <tr>
        <th>Email</th>
        <td>${Email}</td>
      </tr>
      <tr>
        <th>Country</th>
        <td>${Country}</td>
      </tr>
      <tr>
        <th>Shipping</th>
        <td>${Shipping}</td>
      </tr>
      <tr>
        <th>Web</th>
        <td>${Web}</td>
      </tr>
      <tr>
        <th>Notes</th>
        <td>${Notes}</td>
      </tr>
      <tr>
        <th>Created At</th>
        <td>${Created_At}</td>
      </tr>
      <tr>
        <th>Created By</th>
        <td>${Created_By}</td>
      </tr>
    </table>

    <p>You can take action by clicking one of the buttons below:</p>

    <a href="${BASE_URL}/supplier-approve/${SupplierID}/${token}" class="btn btn-approve"
      style="display: inline-block; padding: 12px 20px; margin: 20px 10px 0 0; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 6px; background-color: #4CAF50;">
      Approve Supplier
    </a>

    <a href="${BASE_URL}/supplier-reject/${SupplierID}/${token}" class="btn btn-reject"
      style="display: inline-block; padding: 12px 20px; margin: 20px 10px 0 0; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 6px; background-color: #f44336;">
      Reject Supplier
    </a>

    <p>If you choose to reject the supplier, you will be prompted to provide a reason for the rejection.</p>

    <div class="footer">
      <p>Best regards,<br />Remiex</p>
    </div>
  </div>

</body>

</html>
`;
  },
};

const updateSupplierTemplate = {
  emailSubject: (data) => {
    const { Name } = data.oldRecord;
    return `Supplier Updated : ${Name}`;
  },

  htmlTemplate: (data) => {
    const { approverName, oldRecord, newRecord, token, SupplierID } = data;

    return `<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f0f0f5;
      margin: 0;
      padding: 0;
    }

    .email-container {
      background-color: #ffffff;
      margin: 40px auto;
      padding: 30px;
      max-width: 600px;
      border-radius: 12px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    h2 {
      color: #333333;
      font-size: 24px;
      margin-bottom: 15px;
    }

    p {
      color: #555555;
      line-height: 1.8;
      font-size: 16px;
      margin-bottom: 20px;
    }

    .details-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .details-table th,
    .details-table td {
      text-align: left;
      padding: 10px;
      border: 1px solid #ddd;
      font-size: 15px;
    }

    .details-table th {
      background-color: #f8f8f8;
      font-weight: bold;
    }

    .details-table .old {
      background-color: #f9f9f9;
      text-decoration: line-through;
    }

    .btn {
      display: inline-block;
      padding: 12px 20px;
      margin: 20px 10px 0 0;
      font-size: 16px;
      color: #ffffff;
      text-decoration: none;
      border-radius: 6px;
      transition: background-color 0.3s ease;
    }

    .btn-approve {
      background-color: #4CAF50;
    }

    .btn-approve:hover {
      background-color: #45a049;
    }

    .btn-reject {
      background-color: #f44336;
    }

    .btn-reject:hover {
      background-color: #d73833;
    }

    .footer {
      margin-top: 30px;
      font-size: 14px;
      color: #888888;
    }

    .footer a {
      color: #4CAF50;
      text-decoration: none;
    }

    .footer a:hover {
      text-decoration: underline;
    }

    .logo {
      text-align: center;
      margin-bottom: 20px;
    }

    .logo img {
      max-width: 150px;
      height: auto;
    }
  </style>
</head>

<body>

  <div class="email-container">
    <div class="logo">
      <img src="https://assets.api.uizard.io/api/cdn/stream/32f175ca-32be-40d0-8832-044fb93d8edf.png"
        alt="Company Logo">
    </div>
    <h2>Supplier Updated: ${oldRecord.Name}</h2>

    <p>Hello ${approverName}</p>

    <p>We want to inform you that the details of a Supplier have been updated. Please review the updated information below:</p>
    <table class="details-table">
      <tr>
        <th>Field</th>
        <th>Old Value</th>
        <th>New Value</th>
      </tr>
      <tr>
        <td>Supplier Name</td>
        <td >${oldRecord.Name}</td>
        <td>${newRecord.Name}</td>
      </tr>
      <tr>
        <td>Contact First Name</td>
        <td >${oldRecord.ContactName}</td>
        <td>${newRecord.ContactName}</td>
      </tr>
      <tr>
        <td>Contact Last Name</td>
        <td >${oldRecord.ContactLastname}</td>
        <td>${newRecord.ContactLastname}</td>
      </tr>
      <tr>
        <td>Email</td>
        <td >${oldRecord.Email}</td>
        <td>${newRecord.Email}</td>
      </tr>
      <tr>
        <td>Country</td>
        <td >${oldRecord.Country}</td>
        <td>${newRecord.Country}</td>
      </tr>
      <tr>
        <td>Shipping</td>
        <td >${oldRecord.Shipping}</td>
        <td>${newRecord.Shipping}</td>
      </tr>
      <tr>
        <td>Web</td>
        <td >${oldRecord.Web}</td>
        <td>${newRecord.Web}</td>
      </tr>
      <tr>
        <td>Notes</td>
        <td >${oldRecord.Notes}</td>
        <td>${newRecord.Notes}</td>
      </tr>
      <tr>
        <td>Created At</td>
        <td >${isEmpty(oldRecord.Created_At) ? "" : oldRecord.Created_At}</td>
        <td>${isEmpty(oldRecord.Created_At) ? "" : oldRecord.Created_At}</td>
      </tr>
      <tr>
        <td>Updated At</td>
        <td >${isEmpty(oldRecord.Updated_At) ? "" : oldRecord.Updated_At}</td>
        <td>${newRecord.Updated_At}</td>
      </tr>
      <tr>
        <td>Created By</td>
        <td >${isEmpty(oldRecord.Created_By) ? "" : oldRecord.Created_By}</td>
        <td>${isEmpty(oldRecord.Created_By) ? "" : oldRecord.Created_By}</td>
      </tr>
      <tr>
        <td>Updated By</td>
        <td >${isEmpty(oldRecord.Updated_By) ? "" : oldRecord.Updated_By}</td>
        <td>${newRecord.Updated_By}</td>
      </tr>
      
    </table>

    <p>You can take action by clicking one of the buttons below:</p>

    <a href="${BASE_URL}/supplier-approve/${SupplierID}/${token}" class="btn btn-approve"
      style="display: inline-block; padding: 12px 20px; margin: 20px 10px 0 0; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 6px; background-color: #4CAF50;">
      Approve Supplier
    </a>

    <a href="${BASE_URL}/supplier-reject/${SupplierID}/${token}" class="btn btn-reject"
      style="display: inline-block; padding: 12px 20px; margin: 20px 10px 0 0; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 6px; background-color: #f44336;">
      Reject Supplier
    </a>

    <p>If you choose to reject the supplier, you will be prompted to provide a reason for the rejection.</p>

    <div class="footer">
      <p>Best regards,<br />Remiex</p>
    </div>
  </div>

</body>

</html>

`;
  },
};

const supplierMaterialRequestTemplate = {
  emailSubject: "Material Requests",

  htmlTemplate: (data) => {
    const { name, link } = data;

    return `
  <!DOCTYPE html>
  <html lang="en">
  
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script defer src="https://use.fontawesome.com/releases/v5.15.4/js/all.js"
      integrity="sha384-rOA1PnstxnOBLzCLMcre8ybwbTmemjzdNlILg8O7z1lUkLXozs4DHonlDtnE7fpc"
      crossorigin="anonymous"></script>
    <style>
      html,
      body {
        margin: 0;
        padding: 0;
        width: 100%;
        background: #f4f4f4;
      }
  
      table {
        border-collapse: collapse;
        width: 100%;
        max-width: 600px;
        margin: 40px auto;
        background-color: #ffffff;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }
  
      .container {
        padding: 0 20px;
      }
  
      .logo {
        padding: 30px 0;
        text-align: center;
      }
  
      .logo img {
        width: 130px;
        border: 0;
      }
  
      .header {
        padding: 20px 0;
        text-align: center;
        background-color: #4b7bec;
        color: #ffffff;
      }
  
      .header p {
        font-family: 'Poppins', sans-serif;
        color: #ffffff;
        font-size: 20px;
        line-height: 28px;
        font-weight: 600;
        margin: 0;
      }
  
      .content {
        padding: 20px 0;
      }
  
      .content p {
        font-family: 'Poppins', sans-serif;
        color: #595959;
        font-size: 16px;
        line-height: 26px;
        font-weight: 400;
        margin: 0 0 10px;
      }
  
      .button-container {
        text-align: center;
        margin: 30px 0;
      }
  
      .button {
        background-color: #4b7bec;
        color: #ffffff;
        font-family: 'Poppins', sans-serif;
        font-size: 16px;
        font-weight: 600;
        padding: 16px 40px;
        border-radius: 4px;
        text-decoration: none;
        display: inline-block;
      }
  
      .footer {
        padding: 20px 0;
        /* text-align: center; */
        /* background-color: #f4f4f4; */
      }
  
      .footer p {
        font-family: 'Poppins', sans-serif;
        color: #595959;
        font-size: 14px;
        line-height: 22px;
        margin: 0;
      }
    </style>
  </head>
  
  <body>
    <table>
      <tr>
        <td>
          <div class="container">
            <div class="logo">
              <img src="https://assets.api.uizard.io/api/cdn/stream/32f175ca-32be-40d0-8832-044fb93d8edf.png"
                alt="Company Logo" />
            </div>
            <div>
              <p>Hello ${name},</p>
            </div>
            <div class="content">
              <p>We hope this email finds you well.</p>
              <p>
                We are reaching out to remind you to update your quotes in our supplier portal. Keeping your
                quotes up-to-date .
              </p>
              <p>
                To update your quotes, please click the button below:
              </p>
            </div>
            
            <div class="button-container">
              <a href="${link}" class="button" style="color:#ffffff;">Update
                Quotes</a>
            </div>
            <div class="content">
              <p>
                If you have any questions or need further assistance, feel free to contact our support team.
              </p>
              <p>
                Thank you for your attention .
              </p>
            </div>
            <div class="footer">
              <p>
                Best regards,<br />
                Remiex<br />
  
              </p>
            </div>
          </div>
        </td>
      </tr>
    </table>
  </body>
  
  </html>  
`;
  },
};

const automatedCRONStartedTemplate = {
  emailSubject: () => {
    return `Automated CRON Job Started`;
  },

  htmlTemplate: () => {
    return `
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Email Template</title>
  <style>
    /* General Reset */
    body {
      margin: 0;
      padding: 0;
      width: 100%;
      background: #f4f4f4;
      font-family: 'Poppins', sans-serif;
    }

    table {
      border-collapse: collapse;
      width: 100%;
      max-width: 600px;
      margin: 40px auto;
      background-color: #ffffff;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .container {
      padding: 20px;
    }

    .logo {
      text-align: center;
      padding: 30px 0;
    }

    .logo img {
      width: 150px;
      border: 0;
    }

    .header {
      text-align: center;
      padding: 20px;
    }

    .header p {
      font-size: 20px;
      font-weight: bold;
      margin: 0;
    }

    .content {
      padding: 20px;
    }

    .content p {
      color: #595959;
      font-size: 16px;
      line-height: 1.5;
      margin: 10px 0;
    }

    .button-container {
      text-align: center;
      margin: 30px 0;
    }

    .button {
      display: inline-block;
      background-color: #4b7bec;
      color: #ffffff;
      text-decoration: none;
      font-size: 16px;
      font-weight: bold;
      padding: 12px 25px;
      border-radius: 4px;
    }

    .footer {
      text-align: center;
      background-color: #f4f4f4;
      padding: 20px;
      font-size: 14px;
      color: #595959;
    }
  </style>
</head>

<body>
  <table>
    <tr>
      <td>
        <div class="container">
          <!-- Logo -->
          <div class="logo">
            <img src="https://assets.api.uizard.io/api/cdn/stream/32f175ca-32be-40d0-8832-044fb93d8edf.png" alt="Company Logo">
          </div>

          <!-- Header -->
          <div class="header">
            <p>Automation Process Started</p>
          </div>

          <!-- Content -->
          <div class="content">
            <p>Hello, </p>
            <p>
              This is to inform you that the automation process has started
              successfully.
            </p>
            <p>
              You will be notified upon completion. If you have any questions or need further assistance, feel free to contact our support team.
            </p>
          </div>

          <!-- Footer -->
          <div class="footer">
            <p>
              Best regards,<br>
              <strong>Remiex </strong><br>
              
            </p>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>
`;
  },
};

const automatedCRONEndedTemplate = {
  emailSubject: () => {
    return `Automated CRON Job Ended`;
  },
  htmlTemplate: () => {
    return `
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Email Template</title>
  <style>
    /* General Reset */
    body {
      margin: 0;
      padding: 0;
      width: 100%;
      background: #f4f4f4;
      font-family: 'Poppins', sans-serif;
    }

    table {
      border-collapse: collapse;
      width: 100%;
      max-width: 600px;
      margin: 40px auto;
      background-color: #ffffff;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .container {
      padding: 20px;
    }

    .logo {
      text-align: center;
      padding: 30px 0;
    }

    .logo img {
      width: 150px;
      border: 0;
    }

    .header {
      text-align: center;
      padding: 20px;
    }

    .header p {
      font-size: 20px;
      font-weight: bold;
      margin: 0;
    }

    .content {
      padding: 20px;
    }

    .content p {
      color: #595959;
      font-size: 16px;
      line-height: 1.5;
      margin: 10px 0;
    }

    .button-container {
      text-align: center;
      margin: 30px 0;
    }

    .button {
      display: inline-block;
      background-color: #4b7bec;
      color: #ffffff;
      text-decoration: none;
      font-size: 16px;
      font-weight: bold;
      padding: 12px 25px;
      border-radius: 4px;
    }

    .footer {
      text-align: center;
      background-color: #f4f4f4;
      padding: 20px;
      font-size: 14px;
      color: #595959;
    }
  </style>
</head>

<body>
  <table>
    <tr>
      <td>
        <div class="container">
          <!-- Logo -->
          <div class="logo">
            <img src="https://assets.api.uizard.io/api/cdn/stream/32f175ca-32be-40d0-8832-044fb93d8edf.png" alt="Company Logo">
          </div>

          <!-- Header -->
          <div class="header">
            <p>Automation Process Ended</p>
          </div>

          <!-- Content -->
          <div class="content">
            <p>Hello, </p>
            <p>
              This is to inform you that the automation process has ended
              successfully.
            </p>
            <p>
              You will be notified upon completion. If you have any questions or need further assistance, feel free to contact our support team.
            </p>
          </div>

          <!-- Footer -->
          <div class="footer">
            <p>
              Best regards,<br>
              <strong>Remiex </strong><br>
              
            </p>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>
`;
  },
};

const mondayRecordUploadSuccessTemplate = {
  emailSubject: () => {
    return `RFQ record uploaded to Monday`;
  },

  htmlTemplate: (rfqId) => {
    return `
<!DOCTYPE html>

<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content=width=device-width, initial-scale=1.0">
  <title>Email Template</title>
  <style>
    /* General Reset */
    body {
      margin: 0;
      padding: 0;
      width: 100%;
      background: #f4f4f4;
      font-family: 'Poppins', sans-serif;
    }

    table {
      border-collapse: collapse;
      width: 100%;
      max-width: 600px;
      margin: 40px auto;
      background-color: #ffffff;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .container {
      padding: 20px;
    }

    .logo {
      text-align: center;
      padding: 30px 0;
    }

    .logo img {
      width: 150px;
      border: 0;
    }

    .header {
      text-align: center;
      padding: 20px;
    }

    .header p {
      font-size: 20px;
      font-weight: bold;
      margin: 0;
    }

    .content {
      padding: 20px;
    }

    .content p {
      color: #595959;
      font-size: 16px;
      line-height: 1.5;
      margin: 10px 0;
    }

    .button-container {
      text-align: center;
      margin: 30px 0;
    }

    .button {
      display: inline-block;
      background-color: #4b7bec;
      color: #ffffff;
      text-decoration: none;
      font-size: 16px;
      font-weight: bold;
      padding: 12px 25px;
      border-radius: 4px;
    }

    .footer {
      text-align: center;
      background-color: #f4f4f4;
      padding: 20px;
      font-size: 14px;
      color: #595959;
    }
  </style>
</head>

<body>
  <table>
    <tr>
      <td>
        <div class="container">
          <!-- Logo -->
          <div class="logo">
            <img src="https://assets.api.uizard.io/api/cdn/stream/32f175ca-32be-40d0-8832-044fb93d8edf.png" alt="Company Logo">
          </div>

          <!-- Header -->
          <div class="header">
            <p>RFQ record uploaded to Monday</p>
          </div>

          <!-- Content -->
          <div class="content">
            <p>Hello, </p>
            <p>
              This is to inform you that the upload process for RFQID - ${rfqId} to monday.com has completed successfully.
            </p>
          </div>

          <!-- Footer -->
          <div class="footer">
            <p>
              Best regards,<br>
              <strong>Remiex </strong><br>
              
            </p>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>
`;
  },
};

const mondayRecordUploadFailureTemplate = {
  emailSubject: () => {
    return `RFQ record upload to Monday failed`;
  },

  htmlTemplate: (rfqId) => {
    return `
<!DOCTYPE html>

<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content=width=device-width, initial-scale=1.0">
  <title>Email Template</title>
  <style>
    /* General Reset */
    body {
      margin: 0;
      padding: 0;
      width: 100%;
      background: #f4f4f4;
      font-family: 'Poppins', sans-serif;
    }

    table {
      border-collapse: collapse;
      width: 100%;
      max-width: 600px;
      margin: 40px auto;
      background-color: #ffffff;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .container {
      padding: 20px;
    }

    .logo {
      text-align: center;
      padding: 30px 0;
    }

    .logo img {
      width: 150px;
      border: 0;
    }

    .header {
      text-align: center;
      padding: 20px;
    }

    .header p {
      font-size: 20px;
      font-weight: bold;
      margin: 0;
    }

    .content {
      padding: 20px;
    }

    .content p {
      color: #595959;
      font-size: 16px;
      line-height: 1.5;
      margin: 10px 0;
    }

    .button-container {
      text-align: center;
      margin: 30px 0;
    }

    .button {
      display: inline-block;
      background-color: #4b7bec;
      color: #ffffff;
      text-decoration: none;
      font-size: 16px;
      font-weight: bold;
      padding: 12px 25px;
      border-radius: 4px;
    }

    .footer {
      text-align: center;
      background-color: #f4f4f4;
      padding: 20px;
      font-size: 14px;
      color: #595959;
    }
  </style>
</head>

<body>
  <table>
    <tr>
      <td>
        <div class="container">
          <!-- Logo -->
          <div class="logo">
            <img src="https://assets.api.uizard.io/api/cdn/stream/32f175ca-32be-40d0-8832-044fb93d8edf.png" alt="Company Logo">
          </div>

          <!-- Header -->
          <div class="header">
            <p>RFQ record failed to be uploaded to Monday</p>
          </div>

          <!-- Content -->
          <div class="content">
            <p>Hello, </p>
            <p>
              This is to inform you that the upload process for RFQID - ${rfqId} to monday.com has been unsuccessful.
            </p>
          </div>

          <!-- Footer -->
          <div class="footer">
            <p>
              Best regards,<br>
              <strong>Remiex </strong><br>
              
            </p>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>
`;
  },
};

const emailSupplierRequestTemplate = {
  emailSubject: (data) => {
    return `RFQID - ${data.rfqID} - Price Request`;
  },

  htmlTemplate: (data) => {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Price Request</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f4f6f9;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 30px auto;
            background: #ffffff;
            padding: 25px;
            border-radius: 10px;
        }
        .header {
            text-align: center;
            padding-bottom: 15px;
        }
        .header h3 {
            color: #007bff;
            margin: 0;
            margin-top: 15px;
        }
        .content {
            padding: 20px 0;
            /* font-size: 16px; */
            line-height: 1.6;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 14px;
        }
        .table th, .table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .table th {
            background: #007bff;
            color: #ffffff;
            font-weight: 500;
        }
        .table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        .cta-button {
            display: block;
            width: 100%;
            text-align: center;
            background: #007bff;
            color: #ffffff;
            text-decoration: none;
            padding: 14px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 6px;
            margin-top: 20px;
            transition: background 0.3s;
        }
        .cta-button:hover {
            background: #0056b3;
        }
        .footer {
            text-align: center;
            padding-top: 15px;
            font-size: 12px;
            color: #555;
            border-top: 1px solid #ddd;
            margin-top: 20px;
        }
        @media (max-width: 600px) {
            .table, .table thead, .table tbody, .table th, .table td, .table tr {
                display: block;
                width: 100%;
            }
            .table thead {
                display: none;
            }
            .table tr {
                margin-bottom: 15px;
                border: 1px solid #ddd;
                padding: 10px;
                background: #ffffff;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }
            .table td {
                display: flex;
                justify-content: space-between;
                padding: 8px;
                border: none;
                font-size: 14px;
            }
            .table td::before {
                content: attr(data-label);
                font-weight: bold;
                color: #333;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://platform.remiex.com/assets/logo-CTqoACry.png" alt="Company Logo" width="150">
            <h3>Price Request Notification</h3>
        </div>
        
        <div class="content">
            <p>Dear Supplier,</p>
            <p>We kindly request a quotation for the following materials. Please indicate the price, delivery time, and weight.</p>
            <p><strong>RFQ ID:</strong> ${data.rfqID}</p>
            <table class="table">
                <thead>
                    <tr>
                        <th>Material ID</th>
                        <th>Name</th>
                        <th>Part No.</th>
                        <th>Brand</th>
                        <th>Qty</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.materials
                      .map(
                        (material) => `
                    <tr>
                        <td data-label="Material ID">${material.materialID}</td>
                        <td data-label="Description">${material.Material_Description}</td>
                        <td data-label="Part No.">${material.partNumber}</td>
                        <td data-label="Brand">${material.brand}</td>
                        <td data-label="Qty">${material.quantity}</td>
                    </tr>
                    `
                      )
                      .join("")}
                </tbody>
            </table>
            <p>Please send us the details of your quotation as soon as possible.</p>
            <!-- <a href="#" class="cta-button">Submit Quote</a> -->
            <p>Sincerely,</p>
            <p><strong>Remiex</strong></p>
        </div>
        <div class="footer">
            <p>&copy; 2024 Remiex. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
`;
  },
};

const emailSpanishSupplierRequestTemplate = {
  emailSubject: (data) => {
    return `RFQID - ${data.rfqID} - Solicitud de Precio`;
  },

  htmlTemplate: (data) => {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitud de Cotización</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f4f6f9;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 30px auto;
            background: #ffffff;
            padding: 25px;
            border-radius: 10px;
        }
        .header {
            text-align: center;
            padding-bottom: 15px;
        }
        .header h3 {
            color: #007bff;
            margin: 0;
            margin-top: 15px;
        }
        .content {
            padding: 20px 0;
            line-height: 1.6;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 14px;
        }
        .table th, .table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .table th {
            background: #007bff;
            color: #ffffff;
            font-weight: 500;
        }
        .table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        .cta-button {
            display: block;
            width: 100%;
            text-align: center;
            background: #007bff;
            color: #ffffff;
            text-decoration: none;
            padding: 14px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 6px;
            margin-top: 20px;
            transition: background 0.3s;
        }
        .cta-button:hover {
            background: #0056b3;
        }
        .footer {
            text-align: center;
            padding-top: 15px;
            font-size: 12px;
            color: #555;
            border-top: 1px solid #ddd;
            margin-top: 20px;
        }
        @media (max-width: 600px) {
            .table, .table thead, .table tbody, .table th, .table td, .table tr {
                display: block;
                width: 100%;
            }
            .table thead {
                display: none;
            }
            .table tr {
                margin-bottom: 15px;
                border: 1px solid #ddd;
                padding: 10px;
                background: #ffffff;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }
            .table td {
                display: flex;
                justify-content: space-between;
                padding: 8px;
                border: none;
                font-size: 14px;
            }
            .table td::before {
                content: attr(data-label);
                font-weight: bold;
                color: #333;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://platform.remiex.com/assets/logo-CTqoACry.png" alt="Logo de la Empresa" width="150">
            <h3>Notificación de Solicitud de Cotización</h3>
        </div>
        
        <div class="content">
            <p>Estimado Proveedor,</p>
            <p>Solicitamos amablemente una cotización para los siguientes materiales, por favor indicar precio, tiempo de entrega y peso.</p>
            <p><strong>ID de RFQ:</strong> ${data.rfqID}</p>
            <table class="table">
                <thead>
                    <tr>
                        <th>Material ID</th>
                        <th>Descripción</th>
                        <th>Número de Parte</th>
                        <th>Marca</th>
                        <th>Cantidad</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.materials
                      .map(
                        (material) => `
                    <tr>
                        <td data-label="Material ID">${material.materialID}</td>
                        <td data-label="Descripción">${material.Material_Description}</td>
                        <td data-label="Número de Parte">${material.partNumber}</td>
                        <td data-label="Marca">${material.brand}</td>
                        <td data-label="Cantidad">${material.quantity}</td>
                    </tr>
                    `
                      )
                      .join("")}
                </tbody>
            </table>
            <p>Por favor, envíenos los detalles de su cotización a la mayor brevedad posible.</p>
            <p>Atentamente,</p>
            <p><strong>Remiex</strong></p>
        </div>
        <div class="footer">
            <p>&copy; 2024 Remiex. Todos los derechos reservados.</p>
        </div>
    </div>
</body>
</html>
`;
  },
};

module.exports = {
  newClientTemplate,
  updateClientTemplate,
  newSupplierTemplate,
  updateSupplierTemplate,
  supplierMaterialRequestTemplate,
  automatedCRONStartedTemplate,
  automatedCRONEndedTemplate,
  mondayRecordUploadSuccessTemplate,
  mondayRecordUploadFailureTemplate,
  emailSupplierRequestTemplate,
  emailSpanishSupplierRequestTemplate,
};
