body {
  background-color: rgb(244, 244, 244);
}
.supplier-portal-header {
  background-color: white !important;
}
.header-wrapper {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.supplier-portal-form-wrapper {
  margin: 20px;
}

.submit-btn {
  margin: 10px;
  display: flex;
  justify-content: flex-end;
}

.no-data {
  text-align: center;
}
.add-notes {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.cross-icon {
  font-size: 16px;
  cursor: pointer;
}
.scrollable-container {
  overflow-y: auto;
  min-width: 400px;
  max-height: 600px;
}

.fixed-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: white;
  min-width: 100px;
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
}

.supplier-portal-content-container {
  min-width: 100px;
  width: 100%;
  flex-wrap: nowrap;
  margin-top: 3px;
}

.no-data {
  color: #888;
  font-size: 14px;
  text-align: center;
}
