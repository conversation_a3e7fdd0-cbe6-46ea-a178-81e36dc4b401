.card-icon-container {
  border-radius: 50%;
  height: 25px;
  width: 25px;
  color: white;
  text-align: center;
}

.offer-card-content {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 30px;
}

.custom-switch {
  display: flex;
  align-items: center;
  width: 300px;
  height: 50px;
  border-radius: 30px;
  background-color: #f0f0f0;
  cursor: pointer;
  position: relative;
  padding: 0 20px;
  border: none;
  transition: background-color 0.3s ease;
}

.switch-text {
  color: #333;
  font-size: 14px;
  font-weight: 500;
  transition: opacity 0.3s ease;
}

.switch-slider {
  width: 50%;
  height: calc(100% - 10px);
  /* Subtract 20px for top and bottom padding */
  border-radius: 30px;
  background-color: white;
  position: absolute;
  left: 0;
  top: 5px;
  /* Add space from the top */
  bottom: 5px;
  /* Add space from the bottom */
  transition: transform 0.3s ease;
  z-index: 1;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
}

.checked {
  background-color: #f0f0f0;
  padding: 10px;
}

.checked .switch-slider {
  transform: translateX(100%);
}

.checked .switch-text {
  opacity: 0.5;
}

.checked .switch-text.active {
  opacity: 1;
}

.switch-text.left-align {
  position: absolute;
  right: 10px;
}

.switch-text.right-align {
  text-align: right;
}

.switch-text.center-align {
  text-align: center;
  position: absolute;
  top: 10px;
  left: 30%;
}

.custom-switch {
  display: flex;
  align-items: center;
  width: 300px;
  height: 50px;
  border-radius: 30px;
  background-color: #f0f0f0;
  cursor: pointer;
  position: relative;
  padding: 0 20px;
  border: none;
  transition: background-color 0.3s ease;
}

.switch-text {
  color: #333;
  font-size: 14px;
  font-weight: 500;
  transition: opacity 0.3s ease;
}

.switch-slider {
  width: 50%;
  height: calc(100% - 10px); /* Subtract 20px for top and bottom padding */
  border-radius: 30px;
  background-color: white;
  position: absolute;
  left: 0;
  top: 5px; /* Add space from the top */
  bottom: 5px; /* Add space from the bottom */
  transition: transform 0.3s ease;
  z-index: 1;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
}

.checked {
  background-color: #f0f0f0;
  padding: 10px;
}

.checked .switch-slider {
  transform: translateX(100%);
}

.checked .switch-text {
  opacity: 0.5;
}

.checked .switch-text.active {
  opacity: 1;
}

.switch-text.left-align {
  position: absolute;
  right: 30px;
}

.switch-text.right-align {
  text-align: center;
}
.switch-text.center-align {
  text-align: center;
  position: absolute;
  top: 10px;
  left: 30%;
}

.myoffer-badge-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pdf-content {
  width: 100%;
  padding: 30px 0;
  font-family: sans-serif;
  position: absolute;
  left: -9999px;
  visibility: hidden;
}

.order-purchase-inner-wrapper {
  margin-bottom: 12px;
  margin-left: 5px;
}

.myoffer-card-content {
  width: 100%;
  overflow: hidden;
  position: relative;
}