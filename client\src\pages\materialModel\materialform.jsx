import { Box, Button, Grid, TextField, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import ErrorMessage from 'pages/component/errorMessage';
import InputField from 'pages/component/inputField';
import TextAreaComponent from 'pages/component/textArea';
import * as Yup from 'yup';

const validationSchema = Yup.object().shape({
  partNumber: Yup.string().required('Part Number is required'),
  name: Yup.string().required('Name is required'),
  manufacturer: Yup.string().required('Manufacturer is required')
});

const MaterialForm = ({ material, isEdit, confirmButton, searchMaterial, singlePartNumber, addMaterial, loading }) => {
  const handleSubmit = async (values, actionType, validateForm) => {
    const errors = await validateForm;
    if (Object.keys(errors).length === 0) {
      if (actionType === 'add') {
        await addMaterial({ ...values, materialId: singlePartNumber?.materialId });
      } else {
        confirmButton({ ...values, materialId: singlePartNumber?.materialId });
      }
    }
  };
  return (
    <Formik
      initialValues={{
        partNumber: singlePartNumber?.partNumber || material?.Part_Number || '',
        name: singlePartNumber?.name || '',
        manufacturer: singlePartNumber?.manufacturer || '',
        description: singlePartNumber?.description || material?.Material_Description || ''
      }}
      validateOnBlur={false}
      validateOnChange={false}
      enableReinitialize
      validationSchema={validationSchema}
      onSubmit={(values, { setSubmitting, setErrors }) => {
        setSubmitting(false); // To prevent default submission as we'll handle it manually
      }}
    >
      {({ isSubmitting, errors, touched, values, setFieldValue, handleChange, validateForm, setSubmitting }) => (
        <Form>
          <Grid container spacing={2}>
            <Grid item xs={12} width="100%" justifyContent="space-between">
              <Typography variant="body1">Part Number:</Typography>
              <Grid display="flex">
                <InputField
                  type="text"
                  name="partNumber"
                  value={values?.partNumber}
                  onChange={(e) => {
                    setFieldValue(e.target.name, e.target.value);
                    handleChange(e);
                  }}
                  placeholder="Enter Part Number"
                />
                <Button
                  variant="contained"
                  color="success"
                  sx={{ ml: 2, mr: 2 }}
                  onClick={() => searchMaterial(values?.partNumber)}
                  disabled={loading}
                >
                  Search
                </Button>
              </Grid>
              <ErrorMessage message={errors?.partNumber || ''} />
            </Grid>
            <Grid item xs={12} width="100%" pr={2}>
              <Typography variant="body1">Name:</Typography>
              <InputField
                type="text"
                name="name"
                value={values?.name}
                onChange={(e) => {
                  setFieldValue(e.target.name, e.target.value);
                  handleChange(e);
                }}
                errors={errors}
                placeholder="Enter Name"
              />
            </Grid>
            <Grid item xs={12} width="100%" pr={2}>
              <Typography variant="body1">Manufacturer:</Typography>
              <InputField
                type="text"
                name="manufacturer"
                value={values?.manufacturer}
                onChange={(e) => {
                  setFieldValue(e.target.name, e.target.value);
                  handleChange(e);
                }}
                errors={errors}
                placeholder="Enter Brand Name"
              />
            </Grid>
            <Grid item xs={12} width="100%" pr={2}>
              <Typography variant="body1">Description:</Typography>
              <TextAreaComponent
                minRows={5}
                name="description"
                className="name-text-field"
                onChange={(name, e) => {
                  setFieldValue(name, e.target.value);
                  handleChange(e);
                }}
                placeholder="Enter Description"
                value={values?.description}
              />
            </Grid>
            <Grid item xs={12} pr={2} className="confirm-btn-container">
              <Button
                type="submit"
                variant="contained"
                className="confirm-btn"
                disabled={loading}
                onClick={async () => {
                  handleSubmit(values, 'confirm', validateForm());
                }}
              >
                Confirm
              </Button>
              <Button
                type="button"
                disabled={loading}
                variant="contained"
                onClick={async () => {
                  handleSubmit(values, 'add', validateForm());
                }}
              >
                {isEdit ? 'Update' : 'Save New'}
                {/* Save New */}
              </Button>
            </Grid>
          </Grid>
        </Form>
      )}
    </Formik>
  );
};

export default MaterialForm;
