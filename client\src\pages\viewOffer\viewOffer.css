.offer-detail-select {
  width: 200px;
  margin-bottom: 10px;
  height: 30px
}

.select-input-label {
  margin-top: -7px;
  font-size: 12px
}

.generate-offer-input-field {
  margin-bottom: 10px;
  width: 200px;
  height: 30px
}

.offer-detail-label {
  padding-left: 20px;
}

.margin-field {
  width: 50px
}

.card-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.tooltip-wrapper {
  width: 200px;
  display: flex;
  justify-content: space-between
}

.input-field-wrapper {
  display: flex;
  gap: 10px;
  align-items: center
}

.info-icon {
  cursor: pointer;
}

.quotes-inner-container {
  width: 100px;
  box-sizing: border-box;
}

.material-requested {
  display: flex;
  justify-content: space-between;
}

.material-requested-wrapper {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: auto;
}

.quote-label {
  margin-top: 15px;
}

.quote-column {
  background-color: #f0f0f0;
}
.request-quote-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}
.material-description-wrapper {
  width: 400px;
}
.material-description-wrapper {
  max-width: 200px;
  /* Limit the width */
  flex: 1;
}
@media (max-width: 600px) {
  .request-quote-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }

  .material-description-wrapper {
    max-width: 100%;
  }
}
