const { DATASET_ID_SCRAPE, DATASET_ID_MAIN } = require("../constants");
const {
  bigQueryClient,
  mainDataset,
  scrapedDataset,
  landingZoneDataset,
} = require("../db");
const { apiHandler, apiError, apiResponse } = require("../utils/api.util");
const {
  NOT_FOUND,
  FETCH,
  CONFIRM_SUCCESS,
  ADD_SUCCESS,
  CUSTOM_ERROR,
  UPDATE_SUCCESS,
  SERVER_ERROR,
  FORBIDDEN,
  DELETE_SUCCESS,
} = require("../utils/message.util");
const {
  isEmpty,
  split,
  setDate,
  generateID,
  getCurrentDateTime,
} = require("../utils/misc.util");
const crypto = require("crypto");

const getAllConfigurations = apiHandler(async (req, res) => {
  let [existingConfigurations] = await mainDataset.query({
    query: `
      SELECT *
      FROM Configurations
      ORDER BY ID ASC
    `,
  });

  let [existingApprover] = await mainDataset.query({
    query: `
      SELECT Email
      FROM Users
      WHERE Approver is TRUE
    `,
  });
  let [existingSystemUser] = await mainDataset.query({
    query: `
      SELECT UserID
      FROM Users
      WHERE isSystemReserved is TRUE
    `,
  });

  let [existingDashboards] = await mainDataset.query({
    query: `
      SELECT *
      FROM dashboards
      ORDER BY PARSE_TIMESTAMP('%Y-%m-%dT%H:%M:%E*SZ', Date) DESC, ID ASC
    `,
  });

  let data = {
    configurations: existingConfigurations,
    dashboards: existingDashboards,
    systemUser: existingSystemUser[0]?.UserID,
    approver: existingApprover[0],
  };

  return apiResponse(FETCH, "Configurations", data, res);
});

const updateAllConfigurations = apiHandler(async (req, res) => {
  if (req.user.role.trim().toUpperCase() !== "SUPERVISOR") {
    return apiError(FORBIDDEN, "User", null, res);
  }

  const { data } = req.body;

  let configurationQuery = `
      INSERT INTO Configurations (ID, Day, FromTime, ToTime)
      VALUES `;

  let configurationParams = {};
  data.forEach((day, index) => {
    let suffix = index === data.length - 1 ? ";" : ",";

    configurationQuery += `(@ID${index}, @Day${index}, @FromTime${index}, @ToTime${index})${suffix}`;

    configurationParams[`ID${index}`] = day.ID;
    configurationParams[`Day${index}`] = day.Day;
    configurationParams[`FromTime${index}`] = day.FromTime;
    configurationParams[`ToTime${index}`] = day.ToTime;
  });

  let [deleteConfigurations] = await mainDataset.query({
    query: `
      DELETE FROM Configurations WHERE 1=1
    `,
  });

  await mainDataset.query({
    query: configurationQuery,
    params: configurationParams,
  });

  return apiResponse(UPDATE_SUCCESS, "Configurations", null, res);
});

const updateApprover = apiHandler(async (req, res) => {
  if (req.user.role.trim().toUpperCase() !== "SUPERVISOR") {
    return apiError(FORBIDDEN, "User", null, res);
  }

  const { email } = req.params;

  let [existingUser] = await mainDataset.query({
    query: `
    SELECT UserID FROM Users WHERE Email = @email
    `,
    params: {
      email,
    },
  });

  if (isEmpty(existingUser[0])) {
    return apiError(
      CUSTOM_ERROR,
      "User with this email does not exist, Please add new user with this email First",
      null,
      res
    );
  }

  let [updateApprover] = await mainDataset.query({
    query: `
          BEGIN
          
            UPDATE Users
            SET Approver = NULL
            WHERE Approver = TRUE;

            UPDATE Users
            SET Approver = TRUE
            WHERE Email = @email;

          END
    `,
    params: {
      email,
    },
  });

  return apiResponse(UPDATE_SUCCESS, "Approver", null, res);
});

const addTimeSlot = apiHandler(async (req, res) => {
  const { UserID, timeSlots } = req.body;

  // Helper function to convert UNIX timestamp to readable format (HH:MM AM/PM)
  const convertTimestampToReadable = (timestamp) => {
    const date = new Date(parseInt(timestamp) * 1000); // Convert to milliseconds
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }); // Returns readable time in HH:MM format
  };

  const uniqueDays = [...new Set(timeSlots.map((slot) => slot.Day))];

  for (const day of uniqueDays) {
    const dayTimeSlots = timeSlots.filter((timeSlot) => timeSlot.Day === day);

    // Sort day time slots by FromTime
    dayTimeSlots.sort((a, b) => parseInt(a.FromTime) - parseInt(b.FromTime));

    // Check for overlaps
    for (let i = 0; i < dayTimeSlots.length - 1; i++) {
      const currentSlot = dayTimeSlots[i];
      const nextSlot = dayTimeSlots[i + 1];

      // If next slot's FromTime is less than current slot's ToTime, they overlap
      if (parseInt(nextSlot.FromTime) < parseInt(currentSlot.ToTime)) {
        // Convert timestamps to readable times for the error message (without seconds)
        const currentSlotFromTimeReadable = convertTimestampToReadable(
          currentSlot.FromTime
        );
        const currentSlotToTimeReadable = convertTimestampToReadable(
          currentSlot.ToTime
        );
        const nextSlotFromTimeReadable = convertTimestampToReadable(
          nextSlot.FromTime
        );
        const nextSlotToTimeReadable = convertTimestampToReadable(
          nextSlot.ToTime
        );

        return apiError(
          CUSTOM_ERROR,
          `Overlapping time slots on ${day}: ${currentSlotFromTimeReadable} - ${currentSlotToTimeReadable},  ${nextSlotFromTimeReadable} - ${nextSlotToTimeReadable}`,
          null,
          res
        );
      }
    }
  }

  let timeSlotInsertValues = timeSlots.map((slot) => {
    return {
      ID: generateID().toString(), // Generate a new ID for each time slot
      UserID,
      Day: slot.Day,
      FromTime: slot.FromTime,
      ToTime: slot.ToTime,
    };
  });

  let timeSlotQuery = `
      INSERT INTO Time_Slots (ID, UserID, Day, FromTime, ToTime)
      VALUES 
  `;

  let timeSlotParams = {};
  timeSlotInsertValues.forEach((slot, index) => {
    let suffix = index === timeSlotInsertValues.length - 1 ? ";" : ",";
    timeSlotQuery += `(@ID${index}, @UserID${index}, @Day${index}, @FromTime${index}, @ToTime${index})${suffix}`;

    // Add parameters for the query
    timeSlotParams[`ID${index}`] = slot.ID;
    timeSlotParams[`UserID${index}`] = slot.UserID;
    timeSlotParams[`Day${index}`] = slot.Day;
    timeSlotParams[`FromTime${index}`] = slot.FromTime;
    timeSlotParams[`ToTime${index}`] = slot.ToTime;
  });

  await mainDataset.query({
    query: `DELETE FROM Time_Slots WHERE UserID = @UserID`,
    params: { UserID },
  });

  // Execute the bulk insert query
  await mainDataset.query({
    query: timeSlotQuery,
    params: timeSlotParams,
  });

  return apiResponse(ADD_SUCCESS, "Time Slot", null, res);
});

const getTimeSlots = apiHandler(async (req, res) => {
  const { UserID } = req.params;

  const [timeSlots] = await mainDataset.query({
    query: `
    SELECT * FROM Time_Slots
    WHERE UserID = @UserID`,
    params: {
      UserID,
    },
  });

  if (isEmpty(timeSlots)) {
    return apiError(
      CUSTOM_ERROR,
      "User Time Slots not found, Please contact Supervisor to add Time Slots",
      null,
      res
    );
  }

  return apiResponse(FETCH, "User Time Slots", timeSlots, res);
});

const addDashboard = apiHandler(async (req, res) => {
  const {
    Name,
    Link,
    visible_to_country_manager,
    visible_to_kam,
    visible_to_supervisor,
  } = req.body;

  await mainDataset.query({
    query: `
    INSERT INTO dashboards (ID, Name, Link, Date, visible_to_country_manager, visible_to_kam, visible_to_supervisor)
    VALUES 
    (@ID, @Name, @Link, @Date, @visible_to_country_manager, @visible_to_kam, @visible_to_supervisor)
    `,
    params: {
      ID: generateID(),
      Name,
      Link,
      Date: getCurrentDateTime(),
      visible_to_country_manager,
      visible_to_kam,
      visible_to_supervisor,
    },
  });

  return apiResponse(ADD_SUCCESS, "Dashboard", null, res);
});

const editDashboard = apiHandler(async (req, res) => {
  const {
    ID,
    Name,
    Link,
    visible_to_country_manager,
    visible_to_kam,
    visible_to_supervisor,
  } = req.body;

  await mainDataset.query({
    query: `
    UPDATE dashboards SET
    Name = @Name, Link = @Link, Date = @Date, visible_to_country_manager = @visible_to_country_manager, visible_to_kam = @visible_to_kam, visible_to_supervisor = @visible_to_supervisor
    WHERE ID = @ID
    `,
    params: {
      ID,
      Name,
      Link,
      Date: getCurrentDateTime(),
      visible_to_country_manager,
      visible_to_kam,
      visible_to_supervisor,
    },
  });

  return apiResponse(UPDATE_SUCCESS, "Dashboard", null, res);
});

const deleteDashboard = apiHandler(async (req, res) => {
  const { ID } = req.params;

  await mainDataset.query({
    query: `
    DELETE FROM dashboards 
    WHERE ID = @ID
    `,
    params: {
      ID: parseInt(ID),
    },
  });

  return apiResponse(DELETE_SUCCESS, "Dashboard", null, res);
});

const createSettingTable = apiHandler(async (req, res) => {
  const query = `
    CREATE TABLE IF NOT EXISTS settings (
      SettingID STRING NOT NULL,
      Language STRING NOT NULL,
      Type STRING NOT NULL,
      Content STRING,
    );
  `;

  await mainDataset.query(query);

  return apiResponse(ADD_SUCCESS, "Setting Table", null, res);
});

const getContentOfLegalTerms = apiHandler(async (req, res) => {
  const { language } = req.params;

  const [data] = await mainDataset.query({
    query: `
    SELECT Content
    FROM settings
    WHERE Language = @language AND Type = 'LegalTerms'
    `,
    params: {
      language,
    },
  });
  if (isEmpty(data)) {
    return apiError(NOT_FOUND, "Content", null, res);
  }

  return apiResponse(FETCH, "Content", data[0].Content, res);
});

const addOrUpdateContentOfLegalTerms = apiHandler(async (req, res) => {
  const { language, content } = req.body;

  const SettingID = crypto.randomBytes(16).toString("hex");

  const [existingContent] = await mainDataset.query({
    query: `
    SELECT SettingID
    FROM settings
    WHERE Language = @Language AND Type = 'LegalTerms'
    `,
    params: {
      language,
    },
  });

  if (existingContent.length > 0) {
    await mainDataset.query({
      query: `
      UPDATE settings
      SET Content = @content
      WHERE SettingID = @SettingID
      `,
      params: {
        SettingID: existingContent[0].SettingID,
        content,
      },
    });
  } else {
    await mainDataset.query({
      query: `
      INSERT INTO settings (SettingID, Language, Type, Content)
      VALUES 
      (@SettingID, @language, @Type, @content)
      `,
      params: {
        SettingID,
        language,
        Type: "LegalTerms",
        content,
      },
    });
  }

  return apiResponse(
    ADD_SUCCESS,
    "Content of Legal Terms",
    null,
    res
  );
});

module.exports = {
  getAllConfigurations,
  updateAllConfigurations,
  updateApprover,
  addTimeSlot,
  getTimeSlots,
  addDashboard,
  editDashboard,
  deleteDashboard,
  createSettingTable,
  getContentOfLegalTerms,
  addOrUpdateContentOfLegalTerms,
};
