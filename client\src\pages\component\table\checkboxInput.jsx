import { Checkbox, FormControl, FormControlLabel, FormGroup, FormLabel } from '@mui/material';

const label = { inputProps: { 'aria-label': 'Checkbox demo' } };

const CheckBoxInput = ({ onChange, checked, value, disabled, formLabel }) => {
  return (
    <FormControl component="fieldset">
      {/* {formLabel && <FormLabel component="legend">{formLabel}</FormLabel>} */}
      <FormGroup >
        <FormControlLabel
          color="warning"
          control={<Checkbox {...label} onChange={onChange} checked={checked} value={value} disabled={disabled} />}
          label={formLabel} // Add the form label here for each checkbox
        />
      </FormGroup>
    </FormControl>
  );
};

export default CheckBoxInput;
