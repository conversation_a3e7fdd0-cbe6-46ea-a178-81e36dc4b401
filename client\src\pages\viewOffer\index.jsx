import Grid from '@mui/material/Grid';
// import OrdersTable from './OrdersTable';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { get } from 'lodash';
import './viewOffer.css';
import OfferDetails from './offerDetails';
import MaterialTable from './viewOfferMaterial';
import OfferDetailTotal from './total';
import { calculateOfferApiUrl, myOfferPageUrl, offerPageUrl } from 'utils/constant';
import { useParams } from 'react-router';
import { generateOfferAction, getClientList, getSupplierList } from 'redux/reducers/offerReducer';
import { notification, showAlert } from 'utils/helper';
import Loader from 'components/Loader';
import { post } from 'utils/axios';
import Overview from '../singlrRfq/overView';
import { marginValidationMessage, quantityValidationMessage, unitpriceValidationMessage } from 'utils/validationMessage';
import { getMySingleOfferDetail } from 'redux/reducers/myOffersReducer';

// ==============================|| DASHBOARD - DEFAULT ||============================== //

export default function ViewOffer() {
  const singleOffer = useSelector((state) => state.myOffer);
  const [offer, setOffer] = useState({});
  const [clientList, setClientList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showGenerateOfferBtn, setShowGenerateOfferBtn] = useState(false);
  const offerDetail = useSelector((state) => state.offer);
  const rfqId = useParams()?.id;
  const offerId = useParams()?.offerId;
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getSupplierList());
    dispatch(getClientList());
  }, []);

  useEffect(() => {
    if (offerId) {
      dispatch(getMySingleOfferDetail(offerId));
    }
  }, [offerId]);

  useEffect(() => {
    if (!singleOffer?.singleOfferDetail || !offerId) {
      setOffer({});
      return;
    }

    const selectedOfferDetail = { ...get(singleOffer, 'singleOfferDetail', {}) };
    const {
      ClientID: clientId,
      Notes: notes,
      OfferCurrency: offerCurrency,
      ValidFor: validFor,
      FirstName,
      LastName,
      SubTotal: subTotal,
      GrandTotal: grandTotal,
      Tax: taxCost,
      PaymentTerms: paymentTerm,
      materials = []
    } = selectedOfferDetail;

    const updatedMaterials = materials.map(material => {
      const { Material_ID, RFQ_ID, Part_Number, Quantity_Required = '', Material_Description, brand, suppliers = [] } = material;

      // Map through the suppliers and transform the data
      const transformedSuppliers = suppliers.map(supplier => {
        let offerCost = supplier?.exchangedOfferCostWithHandlingCost || supplier?.totalCostAfterMargin;
        let unitOfferPriceWithHandlingCost = supplier?.unitOfferPriceWithHandlingCost || supplier?.tempUnitOfferPrice;
        const {
          quoteId: id,
          rfqId: RFQ_ID,
          materialId: Material_ID,
          partNumber: Part_Number,
          kam,
          supplierId,
          unitOfferPrice,
          unitPrice,
          shippingCost: sh,
          taxCost: tax,
          quantity,
          exchangedOfferCost,
          exchangeOfferHandlingCost,
          totalCost,
          unitOfferPriceHandlingCost,
          unitCurrency: currency,
          margin,
          totalCostAfterMargin,
          offerCurrency,
          weight,
          leadTime,
          isTax = false,
          date,
          Tax,
          ...rest
        } = supplier || {};

        // Determine if the offer is made
        const isOffered = checkIsOfferd(selectedOfferDetail, supplier);

        return {
          unitOfferPrice: unitOfferPriceWithHandlingCost,
          unitOfferPriceCost: unitOfferPrice,
          unitOfferPriceHandlingCost,
          weight,
          leadTime,
          isTax,
          date,
          id,
          kam,
          supplier: supplierId,
          supplierId,
          unitPrice,
          sh,
          tax: Tax || tax || 0,
          quantity,
          offer: offerCost,
          exchangedOfferCost,
          exchangeOfferHandlingCost,
          totalCost,
          currency,
          margin,
          totalCostAfterMargin,
          offerCurrency,
          isOffered, // Marking if the offer is made
          ...rest
        };
      });

      // Sort suppliers to ensure that the one with isOffered === true is at index 0
      transformedSuppliers.sort((a, b) => b.isOffered - a.isOffered);

      return {
        RFQ_ID,
        Material_ID,
        Part_Number,
        Quantity_Required,
        Material_Description,
        brand,
        showAddBtn: getShowButton(Material_ID),
        suppliers: transformedSuppliers, // Assign the sorted suppliers
      };
    });

    const totalDetail = {
      subTotal,
      taxCost,
      grandTotal,
    };

    const updatedOffer = {
      ...selectedOfferDetail,
      materials: updatedMaterials,
      paymentTerm,
      kam: `${FirstName || ''} ${LastName || ''}`.trim(),
      totalDetail,
      clientId: clientList?.find((client) => client?.value === clientId)?.label,
      offerCurrency,
      validFor,
      notes,
    };

    setShowGenerateOfferBtn(true);
    setOffer(updatedOffer);

  }, [offerDetail?.selectedOffer, singleOffer?.singleOfferDetail, offerId]);


  const checkIsOfferd = (selectedOfferDetail, supplier) => {

    let quotes = selectedOfferDetail?.Quotes;

    if (typeof quotes === 'string') {
      try {
        quotes = JSON.parse(quotes);
      } catch (error) {
        return false;
      }
    }
    return quotes?.some((quote) => quote?.quoteId === supplier?.quoteId);
  };
  const getShowButton = (Material_ID) => {
    return offer?.materials?.find((material) => material?.Material_ID === Material_ID)?.showAddBtn;
  };

  useEffect(() => {
    const formattedClientList = get(offerDetail, 'clientList', [])?.map((client) => ({
      label: client.Name,
      value: client.ClientID
    }));
    if (formattedClientList && formattedClientList?.length > 0) {
      setClientList(formattedClientList);
    }
  }, [offerDetail?.clientList]);

  const buttonsConfig = [
    {
      type: 'close',
      label: 'Close',
      link: offerId ? myOfferPageUrl : `${offerPageUrl}/${rfqId}`
    }
  ];

  const isLoading = () => {
    return singleOffer?.loading || get(offerDetail, 'loading', false) || loading;
  };

  return (
    <Grid container spacing={2}>
      {isLoading() && <Loader />}
      <Grid item xs={12}>
        <Overview rfqDetails={offer} buttonsConfig={buttonsConfig} isLoading={isLoading()} />
      </Grid>

      <Grid item xs={12}>
        <OfferDetails
          offer={offer}
          viewOnly={true}
          clientList={clientList}
          loading={get(offerDetail, 'loading', false) || loading}
        />
      </Grid>

      <Grid item xs={12}>
        <MaterialTable
          rfqId={rfqId}
          supplierList={get(offerDetail, 'supplierList', [])?.map((supplier) => ({ label: supplier?.Name, value: supplier?.SupplierID, Status: supplier?.Status }))}
          viewOnly={true}
          offerId={offerId}
          setOffer={setOffer}
          offer={offer}
          loading={isLoading()}
          materials={get(offer, 'materials', [])}
        />
      </Grid>

      <Grid item xs={12} id="offerDetailTotal">
        <OfferDetailTotal
          viewOnly={true}
          totalDetail={get(offer, 'totalDetail', {})}
          loading={get(offerDetail, 'loading', false) || loading}
        />
      </Grid>
    </Grid>
  );
}
