import React, { useEffect, useState } from 'react';
import { Text<PERSON><PERSON>, Button, Grid, Container, Typography, Box } from '@mui/material';
import InputField from 'pages/component/inputField';
import { validate } from 'pages/component/validation';
import { showAlert } from 'utils/helper';
import { useDispatch, useSelector } from 'react-redux';
import { getUserDetail } from 'utils/auth';
import { updateUserMaintenanace, changePasswordAction, forgotPasswordAction } from 'redux/reducers/userReducer'; // Make sure to import changePasswordAction
import Loader from 'components/Loader';

const ForgotPassword = () => {
  const [userDetail, setUserDetail] = useState({
    email: '',
  });
  const [errors, setErrors] = useState({});
  const [isMailSent, setIsMailSent] = useState(false);
  const userData = useSelector((state) => state.users)
  const dispatch = useDispatch();

  const fields = [
    { name: 'email', label: 'Email', placeholder: 'Enter email', type: 'text', },
  ];

  const handleChange = (name, value) => {
    setUserDetail((prevUserDetail) => ({
      ...prevUserDetail,
      [name]: value
    }));
  };

  const renderField = (field) => {
    const { type, name, placeholder, autoComplete, label } = field || {};
    return (<>
      <Typography variant="subtitle1" gutterBottom>{label}</Typography>
      <InputField
        type={type}
        name={name}
        placeholder={placeholder}
        value={userDetail[name] || ''}
        onChange={(e) => handleChange(name, e?.target?.value)}
        errors={errors}
        fullWidth
        autoComplete={autoComplete}

      />
    </>
    );
  };

  const sendForgotPasswordMail = async () => {
    const rules = {
      email: { required: true, label: 'Email' },
    };
    const validation = validate(userDetail, rules);
    setErrors(validation);

    if (Object.keys(validation).length === 0) {
      const { email } = userDetail || {};

      const payload = {
        Email: email
      };
      const response = await dispatch(forgotPasswordAction(payload));
      const success = response?.payload || {}

      if (success) {
        setIsMailSent(success)
      }
    }
  };

  const cancel = () => {

  }

  const isLoading = () => {
    return userData?.loading
  }

  const buttons = [
    {
      label: 'Cancel',
      onClick: cancel,
      variant: 'outlined',
      color: 'error',
      disabled: isLoading(),
    },
    {
      label: 'Send Mail',
      onClick: sendForgotPasswordMail,
      variant: 'contained',
      color: 'primary',
      disabled: isLoading(),
    }
  ];

  const ThankYouMessage = () => {
    return (

      <Box textAlign="center" my={4}>
        <Typography variant="h4" component="h1" gutterBottom color="primary">
          Password Reset Request Sent
        </Typography>
        <Typography variant="body1" color="textSecondary" paragraph>
          Please check your email for further instructions to reset your password.
          If you don't see it in your inbox, make sure to check your spam folder.
        </Typography>
        <Button variant="contained" color="primary" onClick={() => window.location.href = '/login'}>
          Back to Login
        </Button>
      </Box>
    );
  };


  return (
    <div style={styles?.container}>
      {isLoading() && <Loader />}
      {isMailSent ?
        <ThankYouMessage /> :
        <Box my={4}>
          <Typography variant="h4" component="h1" gutterBottom color="secondary" mb={4}>
            Forgot Password
          </Typography>
          <Grid container spacing={4}>
            {fields
              ?.map((field) => (
                <Grid item xs={12} sm={field.sm} key={field.name}>
                  {renderField(field)}
                </Grid>
              ))}

            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', gap: '10px' }}>
              {buttons
                ?.filter(button => button.show !== false)
                .map((button, index) => (
                  <Button
                    key={index}
                    onClick={button.onClick}
                    variant={button.variant}
                    color={button.color}
                    disabled={button?.disabled}
                  >
                    {button.label}
                  </Button>
                ))}
            </Grid>
          </Grid>
        </Box>}
    </div>
  );
};

export default ForgotPassword;

const styles = {
  container: {
    maxWidth: '300px',
    margin: '0 auto',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center'
  }
}