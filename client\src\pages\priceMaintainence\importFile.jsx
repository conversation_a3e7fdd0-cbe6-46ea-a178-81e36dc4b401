import React, { useEffect, useState } from 'react';
import { Button, Typography, Box } from "@mui/material";
import PageTitle from 'pages/pageTitle';

const ImportFile = ({ setSelectedValue, selectedValue }) => {
  const handleFileImport = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile && selectedFile.type === "text/csv") {
      readFile(selectedFile)
    }
  };

  const readFile = (file) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      const text = event.target.result;
      parseCSV(text, file);
    };
    reader.readAsText(file);
  };

  const cleanData = (data) => {
    return data.map(item => {
      const { "\r": removed, ...rest } = item;
      return rest;
    });
  };

  const parseCSV = (text, file) => {
    const lines = text.split("\n");
    const result = [];
    const headers = lines[0]?.split(",");

    for (let i = 1; i < lines?.length; i++) {
      const obj = {};
      const currentline = lines[i]?.split(",");

      for (let j = 0; j < headers?.length; j++) {
        obj[headers[j]] = currentline[j];
      }
      result.push(obj);
    }
    setSelectedValue({ ...selectedValue, fileData: cleanData(result), file: file });
  };

  const { file } = selectedValue || {};

  return (<>
    <Box className="main-container">
      <PageTitle title='Price Maintenance' />
      <Box className="inner-container">
        <Button
          variant="contained"
          component="label"
          className="import-button"
        >
          Import CSV File
          <input
            type="file"
            hidden
            accept=".csv"
            onChange={handleFileImport}
          />
        </Button>
        {file && (
          <Typography variant="body1" color="textPrimary" className="file-info" mt={1}>
            Selected File: {file.name}
          </Typography>
        )}
      </Box>

    </Box>
  </>
  );
};

export default ImportFile;
