import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { getRequest, post } from 'utils/axios';
import {
  addUserApiUrl,
  deleteUserApiUrl,
  singleUserApiUrl,
  updateUserApiUrl,
  getAllUserApiUrl,
  userPageUrl,
  changeUserPasswordApiUrl,
  forgotPasswordApiUrl,
  resetPasswordApiUrl
} from 'utils/constant';
import { logoutUser, notification, showAlert } from 'utils/helper';
import { getAllKamAction } from './RfqReducer';
import { getUserDetail } from 'utils/auth';

export const getAllUserAction = createAsyncThunk(getAllUserApiUrl, async (data, { dispatch }) => {
  try {
    const response = await getRequest(getAllUserApiUrl, data, true, dispatch);
    if (response) {
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {

        return data;
      }
      if (!success) {
        return [];
      }
    }
  } catch (error) {
    return [];
  }
});

export const getSingleUser = createAsyncThunk('userMaintenanceSlice/fetch', async (userId, { rejectWithValue, dispatch }) => {
  try {
    dispatch(userMaintenanceSlice.actions.setLoading(true));
    const response = await post(singleUserApiUrl(userId), {}, true, dispatch);
    const { error, success, message, data } = get(response, 'data', {});

    if (response) {
      dispatch(userMaintenanceSlice.actions.setLoading(false));

      if (success) {
        dispatch(userMaintenanceSlice.actions.setSingleUserData(data || {}));
        return data || [];
      } else {
        dispatch(notification(false, message, true));
        return rejectWithValue(message || 'Failed to fetch RFQ data');
      }

    } else {
      dispatch(userMaintenanceSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(userMaintenanceSlice.actions.setLoading(false));
    return rejectWithValue('An error occurred while fetching RFQ data');
  }
});


export const updateUserMaintenanace = createAsyncThunk(updateUserApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  const { navigate, ...rest } = data || {}
  const payload = { ...rest }
  const user = getUserDetail()
  const isCurrentUser = user?.userId == data?.UserID

  const { setLoading, setSingleUserData } = userMaintenanceSlice.actions || {}
  try {
    dispatch(setLoading(true));
    const response = await post(updateUserApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
     
        dispatch(setSingleUserData({}))

        if (isCurrentUser) {
          dispatch(notification(success, "Your profile was updated. Please log in again to continue.", false));
          logoutUser(dispatch, navigate)
        } else {
          dispatch(notification(success, message, false));
          navigate(userPageUrl)
        }

        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

export const changePasswordAction = createAsyncThunk(changeUserPasswordApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  const { navigate, ...rest } = data || {}
  const payload = { ...rest }
  const { setLoading, setSingleUserData } = userMaintenanceSlice.actions || {}
  try {
    dispatch(setLoading(true));
    const response = await post(changeUserPasswordApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

export const forgotPasswordAction = createAsyncThunk(forgotPasswordApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  const { navigate, ...rest } = data || {}
  const payload = { ...rest }
  const { setLoading, setSingleUserData } = userMaintenanceSlice.actions || {}
  try {
    dispatch(setLoading(true));
    const response = await post(forgotPasswordApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});
export const resetPasswordAction = createAsyncThunk(resetPasswordApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  const { navigate, ...rest } = data || {}
  const payload = { ...rest }
  const { setLoading, setSingleUserData } = userMaintenanceSlice.actions || {}
  try {
    dispatch(setLoading(true));
    const response = await post(resetPasswordApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: true, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

export const addUserMaintenanace = createAsyncThunk(addUserApiUrl, async (data, thunkAPI) => {
  const { dispatch, rejectWithValue } = thunkAPI;
  const { navigate, ...rest } = data || {}
  const payload = { ...rest }
  const { setLoading, setSingleUserData } = userMaintenanceSlice.actions || {}
  try {
    dispatch(setLoading(true));
    const response = await post(addUserApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        navigate(userPageUrl)
        dispatch(setSingleUserData({}))
        return { success: true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});
export const deleteUserAction = createAsyncThunk('deleteUserAction/fetch', async (userId, { rejectWithValue, dispatch }) => {
  const { setLoading } = userMaintenanceSlice.actions || {}
  try {
    dispatch(setLoading(true));
    const response = await post(deleteUserApiUrl(userId), userId, true, dispatch);

    if (response) {
      dispatch(setLoading(false));

      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        dispatch(notification(true, message, false));
        dispatch(getAllUserAction())
        return { success: true, data };
      }

      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return rejectWithValue({ success: false, data: [] });
  }
});

const userMaintenanceSlice = createSlice({
  name: 'userMaintenace',
  initialState: {
    data: [],
    singleUser: {},
    status: 'idle',
    error: null,
    loading: false,
  },
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setSingleUserData: (state, action) => {
      state.singleUser = action.payload;
    },
    setShippingData: (state, action) => {
      state.allShipping = action.payload;
    },

  }
  , extraReducers: (builder) => {
    builder
      .addCase(getAllUserAction.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getAllUserAction.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload;
      })
      .addCase(getAllUserAction.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      });
  }

});
export const { setLoading, setSingleUserData } = userMaintenanceSlice.actions;
export default userMaintenanceSlice.reducer;
