import { Button } from '@mui/material';
import ActionButton from 'pages/component/actionButton';
import AlertDialog from 'pages/component/dialogbox';
import TableComponent from 'pages/component/table/table';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  getOfferDetail,
  getRequestedQuoteList,
  sendQuoteRequest,
  setRequestedQuoteList,
  setUseQuoteList,
  useQuoteAction
} from 'redux/reducers/offerReducer';
import { NOPRICEAVAILABLEQUOTE, } from './constant';
import { get } from 'lodash';
import { compareTwoArrayOfObjects, showAlert } from 'utils/helper';
import { REQUESTEDQUOTE } from 'pages/monitorRfq/constant';
import { post } from 'utils/axios';
import { getRequesteQuoteList } from 'utils/constant';
import { inviteExpiredSupplier } from 'redux/reducers/supplierPortalReducer';

const RequestQuote = ({ material, rfqId, loading }) => {
  const [loader, setLoader] = useState(false)
  const offer = useSelector((state) => state.offer);
  const [open, setOpen] = useState(false);
  const [previousMaterialId, setPreviousMaterialId] = useState('');
  const [requestedQuote, setRequestedQuote] = useState([]);
  const [oldRequestedQuote, setOldRequestedQuote] = useState([]);
  const dispatch = useDispatch();
  const headers = [
    {
      name: 'name',
      type: 'text',
      title: 'Supplier',
      sortingactive: true,
      minWidth: '170px'
    },
    {
      name: 'price',
      type: 'text',
      title: 'Price',
      defaultValue: '0',
      sortingactive: true
    },
    {
      name: 'date',
      type: 'date',
      title: 'Date',
      sortingactive: true
    },
    {
      name: 'status',
      type: 'badge',
      onlyTextColour: true,
      minWidth: '100px',
      title: 'Status',
      sortingactive: true,
    },
    {
      name: 'actions',
      btnType: 'checkbox',
      type: 'actions',
      checkboxName: 'use',
      btnName: 'use',
      title: 'Use',
      sortingactive: false,
      NoDisable: true,
      isDisable: 'isPriceAvailable',
      component: ActionButton,
      buttonOnClick: (type, supplierId, materialId, index, data) => {
        quoteAction(data, 'use');
      }
    },
    {
      name: 'actions',
      btnType: 'multipleButton',
      type: 'actions',
      title: 'Send Mail',
      sortingactive: false,
      component: ActionButton,
      minWidth: '200px',

      multipleButtons: [
        {
          type: 'button',
          buttonTitle: 'Request',
          disabledKey: 'isDisabled',
          disabledBtn: loader,
          showButton: 'existsInSupplierBrand',
          buttonOnClick: (type, data) => {
            mailQuoteAction(data, 'select');
          },
          tooltip: 'Send Mail Later',
        },
      ],
    },
  ];

  useEffect(() => { setLoader(loading) }, [loading])

  const quoteAction = (quote, actionName) => {
    const updatedRequestedQuote = requestedQuote?.map((requested) =>
      requested.id === quote.id ? { ...requested, [actionName]: !requested[actionName] } : requested
    );
    setRequestedQuote(updatedRequestedQuote);
  };

  const mailQuoteAction = (quote, actionName) => {
    const updatedRequestedQuote = requestedQuote?.map((requested) =>
      requested.id === quote.id ? { ...requested, [actionName]: !requested[actionName] } : requested
    );

    requestQuoteFunction(updatedRequestedQuote, actionName);

  };

  const sendMail = async (supplierId) => {
    let sendMailPayload = { supplierId: supplierId, isRequestPage: true }
    const response = await dispatch(inviteExpiredSupplier(sendMailPayload))
    const { success } = response?.payload || {}
    return success
  }

  const createRequestPayload = (selectedQuoteToSendRequest) => {

    const { brand, Part_Number, Material_ID, Quantity_Required } = material || {};
    const { RFQ_ID, RFQ_Number, RFQ_Name, Portal, RFQ_Date, Delivery_Date, Deadline, FirstName, LastName, URL } = offer?.data || {};

    const payload = {
      partNumber: Part_Number,
      brand: brand,
      materialID: Material_ID?.toString(),
      quantity: Quantity_Required,
      rfqID: rfqId,
      suppliers: selectedQuoteToSendRequest,
      RFQ_ID: RFQ_ID,
      RFQ_Number: RFQ_Number,
      RFQ_Name: RFQ_Name,
      RFQ_Date: RFQ_Date?.value,
      Delivery_Date: Delivery_Date?.value,
      Deadline: Deadline?.value,
      Portal: Portal,
      URL: URL,
      FirstName: FirstName,
      LastName: LastName
    };
    return payload;
  }


  const requestQuoteFunction = async (updatedRequestedQuote, actionName) => {
    const selectedQuoteToUse = updatedRequestedQuote?.filter((requested) => requested?.use === true);

    const selectedQuoteToSendRequest = updatedRequestedQuote
      ?.filter((requested) => requested[actionName] === true)
      ?.map((supplier) => supplier?.supplierId);

    const payload = createRequestPayload(selectedQuoteToSendRequest);

    const response = await dispatch(sendQuoteRequest(payload));
    const success = get(response, 'payload.success', false);

    if (success) {
      let mailSentSuccess = true
      if (actionName === 'sendNow') {
        mailSentSuccess = await sendMail(selectedQuoteToSendRequest[0])
      }
      getRequestedQuotes();
      if (selectedQuoteToUse && selectedQuoteToUse?.length === 0) {
        // handleClose();
      } else {

        // if (mailSentSuccess) {
        // getRequestedQuotes();
        // }
      }
    }
  };

  const getRequestedQuotes = async () => {
    const { brand, Part_Number, Material_ID } = material || {};

    let requestPayload = {
      partNumber: Part_Number,
      brand: brand,
      Material_ID: Material_ID?.toString(),
      RFQ_ID: rfqId,
    };

    try {
      setLoader(true)
      const response = await post(getRequesteQuoteList, requestPayload, true, dispatch);
      const { data: quoteData, success } = response?.data || {};

      if (success) {
        setLoader(false)
        let updatedRequestedQuotes = requestedQuote?.map((request) => {
          let matchedQuote = quoteData?.find((d) => d.supplierID === request.supplierId);
          return {
            ...request,
            use: request?.use || matchedQuote?.use || false,
            status: matchedQuote?.status || request.status,
            isPriceAvailable: matchedQuote?.status === NOPRICEAVAILABLEQUOTE,
            isDisabled: matchedQuote?.status === REQUESTEDQUOTE

          };
        }) || [];

        setRequestedQuote(updatedRequestedQuotes);
      } else {
        setLoader(false)
      }
    } catch (error) {
      setLoader(false)

      console.error('Error fetching quote list:', error)
    }
  };

  const useQuoteFunction = async () => {
    const selectedQuoteToSendRequest = requestedQuote
      ?.filter((requested) => requested?.use === true && requested?.status !== NOPRICEAVAILABLEQUOTE)
      ?.map((supplier) => Number(supplier?.supplierId));

    const selectedQuoteToUse = requestedQuote?.filter((requested) => requested?.select === true);

    const { Material_ID, } = material || {};
    const payload = {
      RFQ_ID: Number(rfqId),
      Material_ID: Number(Material_ID),
      showSuppliers: selectedQuoteToSendRequest
    };

    const response = await dispatch(useQuoteAction(payload));
    const success = get(response, 'payload.success', false);
    if (success) {
      setOldRequestedQuote(requestedQuote || [])
      handleClose();
      // if (selectedQuoteToUse && selectedQuoteToUse?.length === 0) {
      //   handleClose();
      // }
    }

  };

  const checkUseDisable = () => {
    return compareTwoArrayOfObjects(requestedQuote, oldRequestedQuote, 'use')
  };
  const checkRequestDisable = () => {
    return !requestedQuote?.some((requested) => requested?.select === true && requested?.status !== REQUESTEDQUOTE);
  };

  const actionBtns = [
    { title: 'Close', onClick: () => handleClose(), className: 'request-btn', color: 'error', disabled: loader },
    { title: 'Use', onClick: () => useQuoteFunction(), className: 'request-btn', disabled: checkUseDisable() || loader },
  ];

  useEffect(() => {
    if (previousMaterialId !== material?.Material_ID) {
      setRequestedQuote(offer?.requestedQuote || []);
      setOldRequestedQuote(offer?.requestedQuote || []);
    }
    else if (requestedQuote && requestedQuote?.length === 0) {
      setRequestedQuote(offer?.requestedQuote || []);
      setOldRequestedQuote(offer?.requestedQuote || []);
    }
  }, [offer?.requestedQuote]);

  const handleOpen = async () => {
    const { brand, Part_Number, Material_ID } = material || {};

    let payload = {
      partNumber: Part_Number,
      brand: brand,
      Material_ID: Material_ID?.toString(),
      RFQ_ID: rfqId,
    };

    const response = await dispatch(getRequestedQuoteList(payload));
    const { success } = get(response, 'payload', {});
    if (success) {
      setPreviousMaterialId(Material_ID)
      setOpen(true);
    }
  };

  const handleClose = async () => {
    setOpen(false);
    setPreviousMaterialId('');
    setRequestedQuote([]);
    setOldRequestedQuote([]);
    dispatch(setRequestedQuoteList([]));
    dispatch(getOfferDetail(rfqId));
  };

  return (
    <>
      <AlertDialog
        title="Supplier List"
        Component={
          <div style={{ minWidth: '600px' }}>
            <TableComponent
              columns={headers}
              maxHeight="100%"
              rows={requestedQuote || []}
              tableHeading="Supplier List"
              enablePagination={true}
              actionBtns={actionBtns}
              placeActionButtonsIn='header'
            />
          </div>
        }
        open={open}
        borderRadius="20px"
      />
      <Button variant="contained" onClick={() => handleOpen()} disabled={loader}>
        Quote
      </Button>
    </>
  );
};
export default RequestQuote;
