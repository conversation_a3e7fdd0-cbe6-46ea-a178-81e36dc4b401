const router = require("express").Router();
const {
  loginUser,
  getKamUsers,
  fetchAllNotifications,
  readNotifications,
  getAllUsers,
  addUser,
  getSingleUser,
  editUser,
  updatePassword,
  forgotPassword,
  sendPasswordLink,
  recoverPassword,
  deleteUser,
} = require("../user/user.controller");
const { validate } = require("../middlewares/validation.middleware");
const {
  loginSchema,
  fetchAllNotificationsSchema,
  readNotificationsSchema,
  addUserSchema,
  editUserSchema,
  updatePasswordSchema,
  forgotPasswordSchema,
  sendPasswordLinkSchema,
  recoverPasswordSchema,
  getSingleUserSchema,
  deleteUserSchema,
} = require("../user/user.validation");

router.get("/get", getKamUsers);

router.post("/login", validate(loginSchema, "body"), loginUser);

router.post(
  "/notifications",
  validate(fetchAllNotificationsSchema, "body"),
  fetchAllNotifications
);

router.post(
  "/read-notifications",
  validate(readNotificationsSchema, "body"),
  readNotifications
);

router.get("/get-all-users", getAllUsers);

router.post("/add", validate(addUserSchema, "body"), addUser);

router.post(
  "/get-single-user/:ID",
  validate(getSingleUserSchema, "params"),
  getSingleUser
);

router.post("/edit", validate(editUserSchema, "body"), editUser);

router.post(
  "/update-password",
  validate(updatePasswordSchema, "body"),
  updatePassword
);

router.post(
  "/send-password-link",
  validate(sendPasswordLinkSchema, "body"),
  sendPasswordLink
);

router.post(
  "/recover-password",
  validate(recoverPasswordSchema, "body"),
  recoverPassword
);

router.post("/delete/:ID", validate(deleteUserSchema, "params"), deleteUser);

module.exports = router;
