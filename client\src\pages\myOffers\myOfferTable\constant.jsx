export const english = {
  heading: {
    company: 'REMIEX Australia',
    address1: 'Remiex Pty Ltd',
    ABN: 'ABN  58 ***********',
    address2: '9 Richardson Street 4068 Chelmer',
    phone: 'Phone',
    email: 'Email',
    phoneValue: '+**************',
    emailValue: '<EMAIL>'
  },
  headerRight: {
    subHeading: 'Quote'
  },
  name: 'To',
  item: 'Item',
  partNumber: 'Part Number ',
  description: 'Product Description',
  qty: 'Qty',
  unitPrice: 'Unit Price',
  total: 'Total',
  // city: 'Remove',
  subTotal: 'Taxable Amount',
  tax: 'GST (10%)',
  grandTotal: 'Total',
  // buisness: 'Remove',
  // discount: 'Surcharge/Discount',
  // handling: 'Handling',
  id: 'ABN/Client ID',
  address: 'Delivery Address',
  // country: 'Remove',
  date: 'Issue Date',
  validUntil: 'Quote Validity',
  currency: 'Currency',
  exchangeRate: 'Exchange Rate',
  comments: 'Comments',
  brand: 'Brand',
  leadTime: 'Delivery Time in days',
  paymentTerm: 'Payment Terms',
  representative: 'Representative'
};

export const spanish = {
  heading: {
    company: 'REMIEX SPA',
    address1: 'Venta al por menor ',
    address2: 'PRINCIPE DE GALES 5921 OF 1206, LA REINA',
    phone: 'Fono',
    email: 'Email',
    phoneValue: '***********',
    emailValue: '<EMAIL>'
  },
  headerRight: {
    subHeading: 'Cotizacion',
    heading: 'R.U.T: 77044785-2'
  },
  representative: 'Vendedor',
  paymentTerm: 'Condiciones de Pago',
  name: 'Señor (es)',
  item: 'Item',
  partNumber: 'Código ',
  description: 'Detalle',
  qty: 'Cantidad',
  unitPrice: 'Precio Unitario',
  total: 'Total',
  city: 'Ciudad',
  discount: 'Recargo/Dscto',
  // handling: 'Manejo',
  subTotal: 'Afecto',
  tax: '19% IVA',
  grandTotal: 'Total',
  buisness: 'Giro',
  id: 'RUT',
  address: 'Dirección',
  country: 'Comuna',
  date: 'Fecha Documento',
  validUntil: 'Fecha Vencimiento',
  currency: 'Moneda',
  comments: 'Comentarios',
  brand: 'Marca',
  leadTime: 'Plazo de Entrega'
};

export const defaultOfferFilters = [
  {
    type: 'text',
    filterName: 'search',
    heading: 'Search',
    field: '',
    data: ''
  },
  {
    type: 'select',
    filterName: 'status',
    heading: 'All', // "All"
    field: 'o.Status',
    data: ''
  },
  {
    type: 'sort',
    filterName: 'offerDateSort',
    heading: 'Sort By',
    field: 'o.OfferDate',
    data: 'last-7-days'
  },
  {
    type: 'sort',
    filterName: 'freeSort',
    heading: 'Sort By',
    field: 'r.RFQ_ID',
    data: 'ASC'
  }
];

export const sortingTypes = [
  {
    label: 'Last 7 days',
    value: 'last-7-days'
  },
  {
    label: 'Last Month',
    value: 'last-month'
  },
  {
    label: 'Last 3 months',
    value: 'last-3-months'
  },
  {
    label: 'Last 6 months',
    value: 'last-6-months'
  },
  {
    label: 'All',
    value: 'all'
  }
];
