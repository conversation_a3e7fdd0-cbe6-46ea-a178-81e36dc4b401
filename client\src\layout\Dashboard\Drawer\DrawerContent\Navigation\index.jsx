import { useEffect, useState } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';

// project imports
import NavGroup from './NavGroup';
import menuItem from 'menu-items';
import { getUserDetail } from 'utils/auth';
import { COUNTRY_MANAGER, dashboardPageUrl, KAM, SUPERVISER } from 'utils/constant';
import menuItemsList, { supervisorSideBar } from 'menu-items/menuItem';
import { DashboardOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { getSetting } from 'redux/reducers/settingReducer';
import { get, isEmpty } from 'lodash';

// Dashboard group structure
const dashboardTemplate = {
  id: 'group-dashboard',
  title: 'Dashboard',
  type: 'collapse',
  icon: DashboardOutlined,
  children: []
};

// ==============================|| DRAWER CONTENT - NAVIGATION ||============================== //

export default function Navigation() {
  const [open, setOpen] = useState('');
  const [dashboardGroup, setDashboardGroup] = useState([menuItem]);
  const dispatch = useDispatch();
  const setting = useSelector((state) => state.setting);
  useEffect(() => {
    dispatch(getSetting());
  }, []);

  useEffect(() => {
    const { role } = getUserDetail() || {};
    let dashboards = get(setting, 'data.dashboards', []) || [];
    let dashboardItems = dashboards?.map((item) => ({
      ...item,
      id: `dashboard${item.ID}`,
      title: item.Name,
      type: 'item',
      url: `${dashboardPageUrl}/${item?.ID}`,
      breadcrumbs: false
    }));

    dashboardItems = dashboardItems?.filter(
      (item) =>
        (item?.visible_to_supervisor && role === SUPERVISER) ||
        (item?.visible_to_country_manager && role === COUNTRY_MANAGER) ||
        (item?.visible_to_kam && role === KAM)
    );
    let dashboardMenuItem = {
      ...dashboardTemplate,
      children: [...dashboardTemplate.children, ...dashboardItems]
    };
    if (dashboards?.length > 0) {
      setDashboardGroup([dashboardMenuItem, menuItemsList]);
    } else {
      setDashboardGroup([menuItemsList]);
    }
  }, [setting?.data]);

  const getRoleWiseSideBar = (item) => {
    const { role } = getUserDetail() || {};
    if (role === SUPERVISER) {
      return { ...item, children: [...item?.children, ...supervisorSideBar] };
    } else {
      return {
        ...item,
        children: item?.children
          ?.map((childItem) => {
            if (childItem?.type === 'collapse') {
              const accessibleChildren = childItem?.children?.filter(
                (nestedChild) => !nestedChild.accessRole || nestedChild?.accessRole?.includes(role)
              );
              if (!isEmpty(accessibleChildren)) {
                return { ...childItem, children: accessibleChildren };
              }
            } else {
              if (!isEmpty(childItem?.accessRole)) {
                if (childItem?.accessRole?.includes(role)) {
                  return childItem;
                }
              } else {
                return childItem;
              }
            }
          })
          ?.filter(Boolean)
      };
    }
  };

  const handleClick = (i) => {
    if (i?.title === open) {
      setOpen('');
    } else {
      setOpen(i?.title);
    }
  };

  const navGroups = dashboardGroup?.map((item) => {
    switch (item.type) {
      case 'group':
        return <NavGroup key={item.id} item={getRoleWiseSideBar(item)} type={item?.type} />;
      case 'collapse':
        return <NavGroup key={item.id} item={item} type={item?.type} handleClick={(i) => handleClick(i)} open={open} />;
      default:
        return (
          <Typography key={item.id} variant="h6" color="error" align="center">
            Fix - Navigation Group
          </Typography>
        );
    }
  });

  return <Box sx={{ pt: 2 }}>{navGroups}</Box>;
}
