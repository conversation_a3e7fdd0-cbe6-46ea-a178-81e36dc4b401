import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import { centralizedPageUrl, monitorRfqPageUrl, priceMaitenancePageUrl } from 'utils/constant';

// ==============================|| MENU ITEMS - DASHBOARD ||============================== //

const productManager = {
  id: 'group-dashboard',
  title: 'Product Management',
  type: 'collapse',
  icon: ManageAccountsIcon,
  children: [
    {
      id: 'monitor',
      title: 'Requested Quotes',
      type: 'item',
      url: monitorRfqPageUrl,
      breadcrumbs: false
    },

    {
      id: 'pricepriceMaitenance',
      title: 'Price Maintenance',
      type: 'item',
      url: priceMaitenancePageUrl,
      breadcrumbs: false
    },
    {
      id: 'requestedPrice',
      title: 'Centralized Quoting',
      type: 'item',
      url: centralizedPageUrl,
      breadcrumbs: false
    }
  ]
};

export default productManager;
