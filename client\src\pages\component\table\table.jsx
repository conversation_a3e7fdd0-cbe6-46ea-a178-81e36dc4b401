/* eslint-disable jsx-a11y/img-redundant-alt */
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableContainer,
  TablePagination,
  Tooltip,
  TableSortLabel,
  Typography,
  Button,
  Checkbox
} from '@mui/material';
import { getComparator, stableSort } from '../sorting';
import { get } from 'lodash';
import { convertDateToStringFormat, formattedPrice, getDateWithTime } from 'utils/helper';
import './table.css';
import NodataImage from '../../../assets/images/users/no-data-image.png';
import { Link } from 'react-router-dom';
import TableSearch from './tableSearch';
import BadgeInputComponent from './badgeInput';
import ActionCell from './actionCell';
import renderArrayCell from './renderArrayCell';

const TableComponent = ({
  rows,
  columns,
  showDeleteButton,
  customFilters,
  enableSearch,
  allRows,
  handleSearch,
  enablePagination,
  generatePdf,
  loading,
  defaultPage,
  onChangeRowValues,
  headerValues,
  onChangeHeaderValues,
  tableHeading,
  actionBtns,
  tableCardHeaderClass,
  maxHeight,
  isBadgeFilter,
  badgeFilterData,
  placeActionButtonsIn,
  showDeleteIcon,
  columnValues
}) => {
  const [order, setOrder] = useState('desc');
  const [orderBy, setOrderBy] = useState('date');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(defaultPage || 10);
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  const displayedRows = enablePagination
    ? stableSort(rows, getComparator(order, orderBy))?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    : stableSort(rows, getComparator(order, orderBy));

  useEffect(() => {
    if (displayedRows?.length === 0) {
      setPage(0);
    }
  }, [displayedRows]);

  const highlightRow = (row, name) => {
    return row[name] && 'black';
  };

  const ActionButon = () => {
    return (
      <div className="action-buttons">
        {actionBtns?.map((actionBtn) => (
          <Button
            variant={actionBtn?.variant || 'contained'}
            color={actionBtn?.color || 'primary'}
            onClick={actionBtn?.onClick}
            className={actionBtn?.className}
            disabled={actionBtn?.disabled}
          >
            {actionBtn?.title}
          </Button>
        ))}
      </div>
    );
  };

  return (
    <>
      <div className={tableCardHeaderClass || 'table-cardHeader-class'}>
        {tableHeading && (
          <Typography variant="h5" color="secondary">
            {tableHeading}
          </Typography>
        )}
        {placeActionButtonsIn === 'header' && <ActionButon />}
      </div>

      {enableSearch && (
        <div className={tableCardHeaderClass || 'table-cardHeader-class'}>
          <TableSearch
            customFilters={customFilters}
            allRows={allRows}
            handleSearch={(data, searchValue) => handleSearch(data, searchValue)}
            isBadgeFilter={isBadgeFilter}
            badgeFilterData={badgeFilterData}
          />

          {placeActionButtonsIn === 'search' && <ActionButon />}
        </div>
      )}
      <TableContainer sx={{ maxHeight: maxHeight || 440 }}>
        <Table stickyHeader aria-label="all-products" size="small">
          <TableHead sx={{ position: 'sticky', top: 0, zIndex: 1 }}>
            <TableRow>
              {columns?.map((head) => {
                switch (head.type) {
                  case 'checkbox':
                    return (
                      <TableCell key={head.name} sx={{ minWidth: head?.minWidth, width: head?.width, color: 'black', fontWeight: 500 }}>
                        <Checkbox checked={headerValues[head?.name]} onChange={() => onChangeHeaderValues('all')} />
                      </TableCell>
                    );

                  default:
                    return head.sortingactive ? (
                      <TableCell
                        key={head.name}
                        sortDirection="desc"
                        variant="contained"
                        color="primary"
                        sx={{ minWidth: head?.minWidth, maxWidth: head?.maxWidth, color: 'black', fontWeight: 500 }}
                      >
                        <Tooltip enterDelay={300} title="Sort">
                          <TableSortLabel
                            direction={order}
                            onClick={() => {
                              setOrder(order === 'asc' ? 'desc' : 'asc');
                              setOrderBy(head.name);
                            }}
                          >
                            {head.title}
                          </TableSortLabel>
                        </Tooltip>
                      </TableCell>
                    ) : (
                      <TableCell
                        key={head.name}
                        variant="contained"
                        color="primary"
                        sx={{ color: 'black', fontWeight: 500, width: head?.width }}
                      >
                        <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                          <div>{head.title}</div>
                          {head?.columnAction && (
                            <ActionCell isColumnAction={true} columnValues={columnValues} column={head?.columnAction} loading={loading} />
                          )}
                        </div>
                      </TableCell>
                    );
                }
              })}
            </TableRow>
          </TableHead>

          {rows?.length ? (
            <TableBody>
              {displayedRows?.map((data, index) => (
                <TableRow key={data.id} hover>
                  {columns?.map((column, colIndex) => {
                    const {
                      name,
                      type,
                      keyName,
                      dateformat,
                      includeTime,
                      showToday,
                      className,
                      onlyTextColour,
                      badgeClassName,
                      showButton,
                      defaultValue,
                      highlight
                    } = column || {};
                    const cellData = data?.[name] === 0 ? '0' : get(data, name, '-');

                    switch (type) {
                      case 'date':
                        return (
                          <TableCell sx={{ color: highlightRow(data, highlight) }} key={colIndex}>
                            {get(data, name, '')
                              ? includeTime
                                ? getDateWithTime(cellData)
                                : convertDateToStringFormat(cellData, dateformat, showToday)
                              : '-'}
                          </TableCell>
                        );
                      case 'price':
                        return (
                          <TableCell sx={{ color: highlightRow(data, highlight) }} key={colIndex}>
                            {get(data, name, '') ? formattedPrice(cellData) : '-'}
                          </TableCell>
                        );

                      case 'email':
                        return (
                          <TableCell key={colIndex} style={{ textTransform: 'lowercase', color: highlightRow(data, highlight) }}>
                            {cellData}
                          </TableCell>
                        );
                      case 'link':
                        return (
                          <TableCell key={colIndex}>
                            <Link to={cellData || '#'} className="link" target="_blank" rel="noopener noreferrer">
                              <TableCell className="link">Link</TableCell>
                            </Link>
                          </TableCell>
                        );
                      case 'badge':
                        return (
                          <TableCell key={colIndex} className={className}>
                            <BadgeInputComponent
                              onlyTextColour={onlyTextColour}
                              badgeContent={cellData}
                              color={cellData}
                              onClick={() => {}}
                              className={badgeClassName}
                            />
                          </TableCell>
                        );
                      case 'array':
                        return renderArrayCell(
                          data[keyName],
                          name,
                          column?.arrayType || 'text',
                          dateformat,
                          includeTime,
                          showToday,
                          column?.actionBtns,
                          column,
                          data
                        );

                      case 'checkbox':
                        return (
                          <TableCell key={colIndex}>
                            <Checkbox checked={cellData} onChange={() => onChangeRowValues(data)} />
                          </TableCell>
                        );

                      case 'actions':
                        return showButton ? (
                          data[showButton] ? (
                            <ActionCell
                              column={column}
                              data={data}
                              index={index}
                              loading={loading}
                              showDeleteButton={showDeleteButton}
                              generatePdf={generatePdf}
                              showDeleteIcon={showDeleteIcon}
                            />
                          ) : (
                            <TableCell sx={{ minWidth: column?.minWidth }} key={index}></TableCell>
                          )
                        ) : (
                          <ActionCell
                            column={column}
                            data={data}
                            index={index}
                            loading={loading}
                            showDeleteIcon={showDeleteIcon}
                            showDeleteButton={showDeleteButton}
                            generatePdf={generatePdf}
                          />
                        );
                      default:
                        return (
                          <TableCell sx={{ color: highlightRow(data, highlight), whiteSpace: 'pre-line' }} key={colIndex}>
                            {cellData || '-'}
                          </TableCell>
                        );
                    }
                  })}
                </TableRow>
              ))}
            </TableBody>
          ) : (
            <TableBody>
              <TableRow>
                <TableCell align="center" colSpan={columns?.length}>
                  <Typography variant="h6" m={2}>
                    There are no records to display
                  </Typography>
                </TableCell>
              </TableRow>
            </TableBody>
          )}
        </Table>
      </TableContainer>

      {enablePagination && (
        <TablePagination
          rowsPerPageOptions={[5, 10, 20]}
          component="div"
          count={rows?.length || 0}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      )}
    </>
  );
};

TableComponent.propTypes = {
  rows: PropTypes.arrayOf(PropTypes.object).isRequired,
  columns: PropTypes.arrayOf(PropTypes.object).isRequired,
  showDeleteButton: PropTypes.bool,
  enableSearch: PropTypes.bool,
  allRows: PropTypes.array,
  handleSearch: PropTypes.func.isRequired,
  enablePagination: PropTypes.bool,
  generatePdf: PropTypes.func,
  loading: PropTypes.bool,
  tableHeading: PropTypes.string,
  actionBtns: PropTypes.array,
  tableCardHeaderClass: PropTypes.string,
  minHeight: PropTypes.number,
  isBadgeFilter: PropTypes.bool,
  badgeFilterData: PropTypes.array
};

TableComponent.defaultProps = {
  showDeleteButton: false,
  enableSearch: false,
  allRows: [],
  enablePagination: false,
  generatePdf: () => {},
  loading: false,
  tableHeading: '',
  actionBtns: [],
  tableCardHeaderClass: '',
  minHeight: 440,
  isBadgeFilter: false,
  badgeFilterData: []
};

export default TableComponent;
