const { JWT_SECRET } = require("../constants");
const { mainDataset } = require("../db");
const { apiHandler, apiError, apiResponse } = require("../utils/api.util");
const {
  NOT_FOUND,
  FETCH,
  ADD_SUCCESS,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  EXISTS,
  STATUS_SUCCESS,
  SENT_ERROR,
  CUSTOM_ERROR,
} = require("../utils/message.util");
const {
  isEmpty,
  getCurrentDateTime,
  generateID,
} = require("../utils/misc.util");
const { sendNotification } = require("../utils/notifications.util");
const jwt = require("jsonwebtoken");
const {
  newClientTemplate,
  updateClientTemplate,
} = require("../utils/templates");
const { sendMail } = require("../utils/email.util");
const countries = require("../utils/countries");
const states = require("../utils/states");

const getClients = apiHandler(async (req, res) => {
  let { page, limit, search, sort } = req.body;
  const { userId, role } = req.user;

  limit = limit || Number.MAX_SAFE_INTEGER;
  let offset = ((page || 1) - 1) * (limit || Number.MAX_SAFE_INTEGER);

  let whereExpression = ``;
  let orderByExpression = ``;

  let paramObj = {};

  // whereExpression += `WHERE Status = 'APPROVED' OR Status IS NULL`;
  whereExpression += !isEmpty(search)
    ? ` WHERE Name LIKE '%${search}%' OR Address LIKE '%${search}%' OR Municipality LIKE '%${search}%' OR BusinessActivity LIKE '%${search}%' `
    : ``;

  if (role === "COUNTRY_MANAGER") {
    // Fetch country of logged in user
    const [existingUser] = await mainDataset.query({
      query: `
      SELECT Country FROM Users
      WHERE UserID = @UserID
    `,
      params: {
        UserID: userId,
      },
    });
    const userCountryId = existingUser?.[0]?.Country;

    if (userCountryId) {
      paramObj.countryId = userCountryId;
    }

    // Add country filter for COUNTRY_MANAGER role
    if (whereExpression) {
      whereExpression += ` AND Country = @countryId`;
    } else {
      whereExpression += ` WHERE Country = @countryId`;
    }
  }

  orderByExpression += !isEmpty(sort)
    ? ` ORDER BY ${sort.field} ${sort.order} `
    : ` ORDER BY Name ASC `;

  let [existingClients] = await mainDataset.query({
    query: `
      SELECT *
      FROM Clients
      ${whereExpression}
      ${orderByExpression}
    `,
    params: paramObj,
  });

  if (isEmpty(existingClients)) {
    return apiError(NOT_FOUND, "Clients", null, res);
  }

  const count = existingClients.length;
  existingClients = existingClients.splice(offset, limit);

  existingClients = existingClients.sort(
    (a, b) => new Date(b.Updated_At) - new Date(a.Updated_At)
  );

  const data = {
    count: count,
    clients: existingClients,
  };

  return apiResponse(FETCH, "Clients", data, res);
});

const addClient = apiHandler(async (req, res) => {
  const {
    ClientID,
    Name,
    Address,
    Municipality,
    BusinessActivity,
    Postal_Code,
    Country,
    State,
    Region,
  } = req.body;

  let [country] = await mainDataset.query({
    query: `
      SELECT *
      FROM Countries 
      WHERE CountryId = @Country`,
    params: {
      Country,
    },
  });

  let [state] = await mainDataset.query({
    query: `
      SELECT *
      FROM States 
      WHERE StateId = @State AND  CountryId = @Country`,
    params: {
      State,
      Country,
    },
  });

  const date = getCurrentDateTime();

  // Check if the client already exists
  const [existingClient] = await mainDataset.query({
    query: `
      SELECT ClientID FROM Clients
      WHERE ClientID = @ClientID
    `,
    params: {
      ClientID,
    },
  });

  // Early exit if client already exists
  if (!isEmpty(existingClient[0])) {
    return apiError(EXISTS, "Client ID", null, res);
  }

  // Get approver information
  const [approverInformation] = await mainDataset.query({
    query: `
      SELECT Email, FirstName FROM Users
      WHERE Approver = true
    `,
  });

  // Early exit if no approver is found
  if (isEmpty(approverInformation)) {
    return apiError(
      CUSTOM_ERROR,
      "Approver not Available, Please Contact Supervisor",
      null,
      res
    );
  }

  // Generate token data and sign the token
  const tokenData = {
    ClientID,
    Name,
    Address,
    Municipality,
    BusinessActivity,
    date,
    Created_By: req.user.userId.toString(),
    Postal_Code,
    State: state[0].State,
    Country: country[0].Country,
    Region,
  };

  const token = jwt.sign(tokenData, JWT_SECRET);

  // Insert the new client into the database
  await mainDataset.query({
    query: `
      INSERT INTO Clients
      (ClientID, Name, Address, Municipality, BusinessActivity, Status, Created_By, Updated_At, token, Created_At, Postal_Code, State, Country, Region)
      VALUES
      (@ClientID, @Name, @Address, @Municipality, @BusinessActivity, @Status, @Created_By, @Updated_At, @token, @Created_At, @Postal_Code, @State, @Country, @Region)
    `,
    params: {
      ClientID,
      Name,
      Address: Address || "",
      Municipality: Municipality || "",
      BusinessActivity: BusinessActivity || "",
      Created_At: date,
      Updated_At: date,
      Created_By: req.user.userId.toString(),
      Status: "PENDING",
      token,
      Postal_Code,
      State,
      Country,
      Region,
    },
  });

  // Get the approver details and prepare email data
  const { Email: approverEmail, FirstName: approverName } =
    approverInformation[0];

  const data = {
    ClientID,
    clientName: Name,
    token,
    approverName,
    Created_At: new Date(date).toLocaleDateString(),
    Address,
    Municipality,
    BusinessActivity,
    Created_By: `${req.user.firstName} ${req.user.lastName}`,
    Postal_Code,
    State: state[0].State,
    Country: country[0].Country,
    Region,
  };

  // Prepare and send email notification to approver
  const option = {
    to: approverEmail,
    subject: newClientTemplate.emailSubject(data),
    html: newClientTemplate.htmlTemplate(data),
  };

  await sendMail(option);

  // Return success response
  return apiResponse(ADD_SUCCESS, "Client", null, res);
});

const getSingleClient = apiHandler(async (req, res) => {
  const { ClientID } = req.params;

  const [existingClient] = await mainDataset.query({
    query: `
      SELECT CONCAT(u2.FirstName, ' ', u2.LastName) AS Updated_By_Name, CONCAT(u.FirstName, ' ', u.LastName) AS Created_By, 
      cn.Country, s.State,
      c.* FROM Clients as c
      LEFT JOIN Users as u ON CAST(u.UserID as STRING) = CAST(c.Created_By as string)
      LEFT JOIN Users as u2 ON CAST(u2.UserID as STRING) = CAST(c.Updated_By as string)
      LEFT JOIN Countries as cn ON cn.CountryId = c.Country  
      LEFT JOIN States as s ON s.StateId = c.State AND s.CountryId = cn.CountryId
      WHERE ClientID = @ClientID

    `,
    params: {
      ClientID: ClientID,
    },
  });

  if (isEmpty(existingClient[0])) {
    return apiError(NOT_FOUND, "Client", null, res);
  }

  const [existingApprover] = await mainDataset.query({
    query: `
      SELECT CONCAT(u.FirstName, ' ', u.LastName) AS approverName, Email 
      FROM Users as u
      WHERE approver is true
    `,
  });

  existingClient[0].approverName = existingApprover[0].approverName;
  existingClient[0].approverEmail = existingApprover[0].Email;

  return apiResponse(FETCH, "Client", existingClient[0], res);
});

const editClient = apiHandler(async (req, res) => {
  const {
    ClientID,
    Name,
    Address,
    Municipality,
    BusinessActivity,
    Postal_Code,
    State,
    Country,
    Region,
  } = req.body;

  let [country] = await mainDataset.query({
    query: `
      SELECT *
      FROM Countries 
      WHERE CountryId = @Country`,
    params: {
      Country,
    },
  });

  let [state] = await mainDataset.query({
    query: `
      SELECT *
      FROM States 
      WHERE StateId = @State AND  CountryId = @Country`,
    params: {
      State,
      Country,
    },
  });

  const date = getCurrentDateTime();

  // Fetch old client information
  const [oldClientInformation] = await mainDataset.query({
    query: `SELECT ClientID, Name, Address, Municipality, BusinessActivity, c.Region, c.Postal_Code, s.State, cn.Country, c.Created_At, c.Updated_At, CONCAT(u.FirstName, ' ', u.LastName) AS Created_By
            FROM Clients as c
            LEFT JOIN Users as u ON CAST(u.UserID as STRING) = CAST(c.Created_By as string)
            LEFT JOIN Countries as cn ON cn.CountryId = c.Country  
            LEFT JOIN States as s ON s.StateId = c.State AND s.CountryId = cn.CountryId
            WHERE ClientID = @ClientID`,
    params: { ClientID },
  });

  // Early exit if client not found
  if (isEmpty(oldClientInformation[0])) {
    return apiError(NOT_FOUND, "Client", null, res);
  }

  const newRecord = {
    ClientID,
    Name,
    Address,
    Municipality,
    BusinessActivity,
    Created_At: new Date(
      oldClientInformation[0].Created_At
    ).toLocaleDateString(),
    Updated_At: new Date(date).toLocaleDateString(),
    Updated_By: `${req.user.firstName} ${req.user.lastName}`,
    Postal_Code,
    State: state[0].State,
    Country: country[0].Country,
    Region,
  };

  // Fetch approver information
  const [approverInformation] = await mainDataset.query({
    query: `SELECT Email, FirstName FROM Users
            WHERE Approver = true`,
  });

  // Early exit if no approver found
  if (isEmpty(approverInformation)) {
    return apiError(
      CUSTOM_ERROR,
      "Approver not Available, Please Contact Supervisor",
      null,
      res
    );
  }

  // Prepare the old record for logging
  const oldRecord = {
    ...oldClientInformation[0],
    Created_At: !isEmpty(oldClientInformation[0].Created_At)
      ? new Date(oldClientInformation[0].Created_At).toLocaleDateString()
      : null,
    Updated_At: !isEmpty(oldClientInformation[0].Updated_At)
      ? new Date(oldClientInformation[0].Updated_At).toLocaleDateString()
      : null,
  };

  const token = jwt.sign(oldRecord, JWT_SECRET);

  const logID = generateID().toString();

  // Update the client information
  await mainDataset.query({
    query: `UPDATE Clients SET
            Name = @Name,
            Address = @Address,
            Municipality = @Municipality,
            BusinessActivity = @BusinessActivity,
            Updated_By = @Updated_By,
            status = @status,
            token = @token,
            Updated_At = @Updated_At,
            logID = @logID,
            Postal_Code = @Postal_Code,
            State = @State,
            Country = @Country,
            Region = @Region
            WHERE ClientID = @ClientID`,
    params: {
      ClientID,
      Name,
      Address: Address || "",
      Municipality: Municipality || "",
      BusinessActivity: BusinessActivity || "",
      Updated_At: date,
      Updated_By: req.user.userId.toString(),
      status: "PENDING",
      token,
      logID,
      Postal_Code,
      State,
      Country,
      Region,
    },
  });

  // Log the client update operation
  await mainDataset.query({
    query: `INSERT INTO logs 
              (type, typeID, operation, userID, typeData, date, logID)
              VALUES
              (@type, @typeID, @operation, @userID, @typeData, @date, @ID)`,
    params: {
      type: "CLIENT",
      typeID: ClientID,
      operation: "UPDATE",
      userID: req.user.userId.toString(),
      typeData: JSON.stringify(oldClientInformation[0]),
      date: date,
      ID: logID,
    },
  });

  // Prepare email data for approver notification
  const { Email: approverEmail, FirstName: approverName } =
    approverInformation[0];

  const data = {
    ClientID,
    approverName,
    oldRecord,
    newRecord,
    token,
  };

  const option = {
    to: approverEmail,
    subject: updateClientTemplate.emailSubject(data),
    html: updateClientTemplate.htmlTemplate(data),
  };

  await sendMail(option);

  return apiResponse(UPDATE_SUCCESS, "Client", null, res);
});

const deleteClient = apiHandler(async (req, res) => {
  const { ClientID } = req.params;

  const [deleteClient] = await mainDataset.query({
    query: `
      DELETE FROM Clients
      WHERE ClientID = @ClientID
    `,
    params: {
      ClientID,
    },
  });

  return apiResponse(DELETE_SUCCESS, "Client", null, res);
});

const updateClientStatus = apiHandler(async (req, res) => {
  const { ID: ClientID, status, reason, token } = req.body;

  const [ClientInformation] = await mainDataset.query({
    query: `SELECT Name, Created_By, status, logID FROM Clients WHERE ClientID = @ClientID AND token = @token`,
    params: {
      ClientID,
      token,
    },
  });

  if (isEmpty(ClientInformation[0])) {
    return apiError(NOT_FOUND, "Client", null, res);
  }

  if (ClientInformation[0].status !== "PENDING") {
    return apiError(
      CUSTOM_ERROR,
      `Can not be ${status}, Client has already been ${ClientInformation[0].status}`,
      null,
      res
    );
  }

  let name = ClientInformation[0].Name;
  let userId = ClientInformation[0].Created_By;
  let logID = ClientInformation[0].logID;

  if (status === "APPROVED") {
    const [approveClient] = await mainDataset.query({
      query: `UPDATE Clients SET 
              lastUpdatedStatus = @status,
              Status = @status
              WHERE ClientID = @ClientID`,
      params: {
        ClientID,
        status,
      },
    });

    let message = `The Client - ${name} has been approved`;

    sendNotification({
      userID: userId,
      message: message,
    });

    return apiResponse(STATUS_SUCCESS, "Client", null, res);
  }

  if (!isEmpty(logID)) {
    const [oldClientInformation] = await mainDataset.query({
      query: `SELECT typeData FROM logs
              WHERE typeID = @typeID AND type = @type AND logID = @logID
              `,
      params: {
        typeID: ClientID.toString(),
        type: "CLIENT",
        logID,
      },
    });

    let oldData = JSON.parse(oldClientInformation[0].typeData);

    const [rejectClient] = await mainDataset.query({
      query: `UPDATE Clients SET
              Status = "APPROVED",
              lastUpdatedStatus = @status,
              Name = @Name,
              Address = @Address,
              Municipality = @Municipality,
              BusinessActivity = @BusinessActivity,
              Reject_Reason = @reason
              WHERE ClientID = @ClientID
              `,
      params: {
        ClientID,
        status,
        reason,
        Name: oldData.Name,
        Address: oldData.Address,
        Municipality: oldData.Municipality,
        BusinessActivity: oldData.BusinessActivity,
      },
    });
  } else {
    const [rejectClient] = await mainDataset.query({
      query: `UPDATE Clients SET
              Status = @status,
              lastUpdatedStatus = @status,
              Reject_Reason = @reason
              WHERE ClientID = @ClientID
              `,
      params: {
        ClientID,
        status,
        reason,
      },
    });
  }

  let message = `The Client - ${name} has been rejected for the following reason: ${reason}`;

  sendNotification({
    userID: userId,
    message: message,
  });

  return apiResponse(STATUS_SUCCESS, "Client", null, res);
});

const getCountries = apiHandler(async (req, res) => {
  const { role, userId } = req.user;
  let CountryId;
  let query = `
    SELECT *
    FROM Countries
    ORDER BY Country ASC
  `;
  let params = {};

  if (role === "COUNTRY_MANAGER") {
    const [existingUser] = await mainDataset.query({
      query: `
        SELECT Country FROM Users
        WHERE UserID = @UserID
      `,
      params: {
        UserID: userId,
      },
    });

    CountryId = existingUser?.[0]?.Country;

    if (CountryId) {
      query = `
        SELECT *
        FROM Countries 
        WHERE CountryId = @CountryId
      `;
      params.CountryId = CountryId;
    }
  }

  const [countries] = await mainDataset.query({
    query,
    params,
  });

  if (isEmpty(countries)) {
    return apiError(NOT_FOUND, "Countries", null, res);
  }

  const data = {
    count: countries.length,
    countries: countries,
  };

  return apiResponse(FETCH, "Countries", data, res);
});


const getStateList = apiHandler(async (req, res) => {
  const { CountryId } = req.params;

  let [countries] = await mainDataset.query({
    query: `
      SELECT *
      FROM Countries 
      WHERE CountryId = @CountryId`,
    params: {
      CountryId: parseInt(CountryId),
    },
  });

  if (isEmpty(countries)) {
    return apiError(NOT_FOUND, "Country", null, res);
  }

  let [states] = await mainDataset.query({
    query: `
      SELECT *
      FROM States 
      WHERE CountryId = @CountryId`,
    params: {
      CountryId: parseInt(CountryId),
    },
  });

  if (isEmpty(states)) {
    return apiError(NOT_FOUND, "State", null, res);
  }

  const data = {
    count: states.length,
    states: states,
  };

  return apiResponse(FETCH, "States", data, res);
});

const importCountry = apiHandler(async (req, res) => {
  let allCountries = countries;

  const BATCH_SIZE = 100; // adjust based on testing
  for (let i = 0; i < allCountries.length; i += BATCH_SIZE) {
    const batch = allCountries.slice(i, i + BATCH_SIZE);
    const rows = batch
      .map(
        ({ id, name }) => `(${parseInt(id)}, "${name.replace(/"/g, '\\"')}")`
      )
      .join(", ");

    const query = `INSERT INTO Countries (CountryId, Country) VALUES ${rows}`;

    await mainDataset.query({ query });
  }

  return apiResponse(FETCH, "Import", null, res);
});

const importState = apiHandler(async (req, res) => {
  let allStates = states;

  const BATCH_SIZE = 100; // adjust based on testing
  for (let i = 0; i < allStates.length; i += BATCH_SIZE) {
    const batch = allStates.slice(i, i + BATCH_SIZE);

    const rows = batch
      .map(
        ({ id, name, countryId }) =>
          `(${parseInt(id)}, "${name.replace(/"/g, '\\"')}", ${parseInt(
            countryId
          )})`
      )
      .join(", ");

    const query = `INSERT INTO States (StateId, State, CountryId) VALUES ${rows}`;

    await mainDataset.query({ query });
  }

  return apiResponse(FETCH, "Import", null, res);
});

const getRegion = apiHandler(async (req, res) => {
  let [regions] = await mainDataset.query({
    query: `
      SELECT DISTINCT(Destination)
      FROM Shipping_Rates ORDER BY Destination ASC`,
  });

  if (isEmpty(regions)) {
    return apiError(NOT_FOUND, "Regions", null, res);
  }

  // Transform to an array of strings
  const regionNames = regions.map((item) => item.Destination);

  const data = {
    count: regionNames.length,
    regions: regionNames,
  };

  return apiResponse(FETCH, "Regions", data, res);
});

module.exports = {
  getClients,
  addClient,
  getSingleClient,
  editClient,
  deleteClient,
  updateClientStatus,
  getCountries,
  getStateList,
  importCountry,
  importState,
  getRegion,
};
