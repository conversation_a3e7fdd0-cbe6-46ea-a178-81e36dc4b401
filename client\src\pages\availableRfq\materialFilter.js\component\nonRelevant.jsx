/* eslint-disable no-unused-vars */
import { Button } from '@mui/material';
import { useDispatch } from 'react-redux';
import { assignRfqAction, discardRfqAction } from 'redux/reducers/RfqReducer';
import { getUserDetail } from 'utils/auth';
import { IRRELEVANT, KAM, RESERVED } from 'utils/constant';

const NonRelevant = ({ currentPage, filters, order, variant, loading }) => {
  const dispatch = useDispatch();
  const assignRfq = () => {
    const { RFQ_ID } = order;

    dispatch(discardRfqAction({ currentPage, rfqId: RFQ_ID, filters }));
  };
  return (
    <>
      {' '}
      <Button variant={variant} size="small" className="material-request-btn small-btn" onClick={() => assignRfq()} disabled={loading}>
        Discard
      </Button>
    </>
  );
};
export default NonRelevant;
