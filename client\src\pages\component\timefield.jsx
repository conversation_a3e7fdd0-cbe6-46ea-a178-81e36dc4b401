import React from 'react';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import dayjs from 'dayjs';

const GlobalTimePicker = ({ label, value, className, onChange, ...props }) => {

  const convertUnixToDayjs = (unixTime) => {
    return dayjs.unix(unixTime);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <TimePicker
        label={label}
        className={className}
        value={value ? convertUnixToDayjs(value) : null}
        onChange={(newValue) => {
          if (newValue) {
            onChange(newValue.unix());
          } else {
            onChange(null);
          }
        }}
        {...props}
      />
    </LocalizationProvider>
  );
};

export default GlobalTimePicker;
