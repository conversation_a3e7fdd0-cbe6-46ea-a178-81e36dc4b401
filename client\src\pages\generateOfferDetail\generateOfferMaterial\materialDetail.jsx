/* eslint-disable no-unused-vars */
import React, { useState } from 'react';
import { Grid, Typography, Tooltip, Box, Button } from '@mui/material';
import InputField from 'pages/component/inputField';
import { getSupplierName, isCountryManager } from 'utils/helper';
import InfoIcon from '@mui/icons-material/Info';
import CalculateMargin from 'pages/offer/offerMaterial/calculateMargin/calculateMargin';

const initialData = [
  { name: 'supplierName', label: 'Supplier', hide: isCountryManager() },
  { name: 'unitPrice', label: 'Unit price', defaultValue: '0' },
  { name: 'weight', label: 'Weight', defaultValue: '0' },
  { name: 'sh', label: 'Shipping', defaultValue: '0' },
  { name: 'tax', label: 'Tax', defaultValue: '0' },
  { name: 'quantity', label: 'Qty', defaultValue: '0', type: 'input', inputType: 'number' },
  { name: 'totalCost', label: 'Total Cost', defaultValue: '0' },
  { name: 'currency', label: 'Currency' },
  { name: 'margin', label: 'Margin', type: 'input', inputType: 'number' },
  {
    name: 'offer',
    label: 'Offer',
    defaultValue: '0',
    icon: true,
    iconContent: [
      { label: 'Offer Cost', value: 'exchangedOfferCost' },
      { label: 'Offer Handling Cost', value: 'exchangeOfferHandlingCost' }
    ]
  },
  {
    name: 'unitOfferPrice',
    label: 'Offer Unit Price',
    defaultValue: '0',
    icon: true,
    iconContent: [
      { label: 'Unit Price', value: 'unitOfferPriceCost' },
      { label: 'Unit Handling Price', value: 'unitOfferPriceHandlingCost' }
    ]
  },
  { name: 'offerCurrency', label: 'Offer Currency', width: '50px' },
  {
    name: 'actions',
    btnType: 'update',
    type: 'actions',
    minWidth: '250px',
    buttonTitle: 'Get Margin',
    label: 'Action',
    sortingactive: false,
    hideDeleteBtn: true,
    component: CalculateMargin,
    buttonOnClick: (type, id, materialId, index, data) => {
      // setMarginDetail({ ...data });
    }
  }
];

const OfferMaterialDetail = ({ suppliers, offer, onChange, material, calculateOffer, updateMaterial, supplierList, loading }) => {
  // Function to get value by name
  const getValueByName = (supplier, name, item) => {
    if (name === 'supplierName') {
      return getSupplierName(supplier, supplierList);
    } else {
      return supplier[name] || item?.defaultValue || '-';
    }
  };
  const RenderInfo = ({ name, iconContent, supplier, item }) => {
    return (
      <div style={{ padding: '10px' }}>
        {iconContent?.map((icon) => (
          <div style={{ width: '200px', display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body1">{icon?.label}</Typography>
            <Typography variant="caption" sx={{ textAlign: 'center' }}>
              {getValueByName(supplier, icon?.value)}
            </Typography>
          </div>
        ))}
      </div>
    );
  };

  const renderField = (type, value, onChange, name, inputType, item, supplier) => {
    const { icon, iconContent } = item || {};
    return (
      <div className="field-wrapper">
        {type === 'input' ? (
          <InputField type={inputType} style={{ width: '70px' }} onChange={onChange} className="margin-field" value={value} name={name} />
        ) : type === 'actions' ? (
          <>
            <item.component rowData={supplier} offer={offer} />
          </>
        ) : (
          <>
            <Typography
              variant="body1"
              sx={{
                overflowWrap: 'break-word',
                wordWrap: 'break-word'
              }}
            >
              {value}
            </Typography>
            {icon && (
              <Tooltip title={<RenderInfo name={name} iconContent={iconContent} supplier={supplier} item={item} />} placement="top">
                <div>
                  <InfoIcon fontSize="16px" />
                </div>
              </Tooltip>
            )}
          </>
        )}
      </div>
    );
  };

  return (
    <>
      {suppliers?.map((supplier, supplierIndex) => (
        <Box key={supplierIndex} className="material-requested" mr={1}>
          {initialData.map(
            (item, index) =>
              !item?.hide && (
                <Box key={index} className="quotes-inner-container" mr={1}>
                  <div className="quote-label">
                    <Typography variant="body1" className="over-view-label" color="secondary">
                      {item.label}
                    </Typography>
                  </div>
                  {renderField(
                    item.type,
                    getValueByName(supplier, item.name, item),
                    (e) => onChange(e, material, supplier), // Handle input change here
                    item?.name,
                    item?.inputType,
                    item,
                    supplier,
                    calculateOffer
                  )}
                </Box>
              )
          )}
          {supplier?.isMaterialUpdated && (
            <Box className="update-btn-wrapper" mt={3}>
              <Button variant="contained" onClick={() => updateMaterial(supplier)} disabled={loading}>
                Update
              </Button>
            </Box>
          )}
        </Box>
      ))}
    </>
  );
};

export default OfferMaterialDetail;
