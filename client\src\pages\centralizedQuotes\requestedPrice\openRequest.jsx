import { Button } from "@mui/material"
import ActionButton from "pages/component/actionButton";
import TableComponent from "pages/component/table/table";
import { REQUESTEDQUOTE, RESPONDEDDQUOTE } from "pages/monitorRfq/constant";
import { useEffect, useState } from "react";
const statusTabsData = [
  {
    label: 'ALL',
    value: 'ALL'
  },
  {
    label: REQUESTEDQUOTE,
    value: REQUESTEDQUOTE
  },
  {
    label: RESPONDEDDQUOTE,
    value: RESPONDEDDQUOTE
  },
]

const OpenRequest = ({ setFormData, formData }) => {
  const [rows, setRows] = useState([]);

  useEffect(() => {
    const { openRequestData } = formData?.requestedPrice || {}
    setRows(openRequestData || [])
  }, [])

  const headers = [
    {
      name: 'requestDate',
      type: 'date',
      title: 'Requested Date',
      sortingactive: true
    },
    {
      name: 'respondDate',
      type: 'date',
      title: 'Received Date',
      sortingactive: true
    },
    {
      name: 'supplierName',
      type: 'text',
      title: 'Supplier',
      sortingactive: true
    },
    {
      name: 'unitPrice',
      type: 'text',
      title: 'Unit Cost',
      sortingactive: true
    },
    {
      name: 'currency',
      type: 'text',
      title: 'Currency',
      sortingactive: true,
      defaultValue: 0
    },
    {
      name: 'actions',
      btnType: 'infoIcon',
      type: 'actions',
      toolTipName: 'notes',
      title: 'Notes',
      sortingactive: false,
      component: ActionButton,
      buttonOnClick: (type, id, materialId, index, data) => {
      }
    },
    {
      name: 'status',
      type: 'badge',
      title: 'Status',
      sortingactive: true,
      className: 'status-badge'
    },

  ];
  const cancel = () => {
    setFormData({ ...formData, requestedPrice: { ...formData?.requestedPrice, open: '' } })
  }

  const handleSearch = (filteredData) => {
    setRows(filteredData || [])
  }

  const { openRequestData: allRows } = formData?.requestedPrice || {}
  return (
    <>
      <TableComponent
        columns={headers}
        rows={rows || []}
        title="Vovier"
        titleLink="dashboard"
        enableSearch={true}
        placeActionButtonsIn='search'
        handleSearch={handleSearch}
        allRows={allRows}
        isBadgeFilter={true}
        badgeFilterData={statusTabsData}
        actionBtns={[{ onClick: cancel, title: 'Cancel' }]}
        enablePagination={true} />
    </>
  )
}
export default OpenRequest