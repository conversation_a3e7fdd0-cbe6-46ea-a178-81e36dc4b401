import React from 'react';
import { Grid } from '@mui/material';
import MyOfferCard from './card';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import CustomSwitch from './myOfferSwitch';
import { FaArrowRightArrowLeft } from "react-icons/fa6";
import { formattedPrice } from 'utils/helper';
const MyOfferSummary = ({ onSwitch, cardDetails }) => {
  const { monthRFQWon, wtdOffersWon, wtdOffers } = cardDetails || {}
  const cardData = [
    {
      icon: ArrowUpwardIcon,
      title: `$ ${monthRFQWon ? formattedPrice(monthRFQWon) : '0.00'}`,
      content: 'Total RFQ won this month',
      iconBg: 'red'
    },
    {
      icon: FaArrowRightArrowLeft,
      title: `$ ${wtdOffersWon ? formattedPrice(wtdOffersWon) : '0.00'}`,
      content: 'WTD Offers Won',
      iconBg: 'rgb(76, 217, 100)',
      padding: '5px'
    },
    {
      icon: ArrowUpwardIcon,
      title: wtdOffers || '0',
      content: 'WTD Offers',
      iconBg: 'rgb(76, 217, 100)'
    }

  ];

  return (
    <Grid display="flex">
      <CustomSwitch onSwitch={onSwitch} />
      <Grid container spacing={2} display="flex" justifyContent="flex-end">
        {cardData.map((card, index) => (
          <Grid item key={index}>
            <MyOfferCard title={card.title} content={card.content} Icon={card.icon} iconBg={card.iconBg} padding={card.padding} />
          </Grid>
        ))}
      </Grid>
    </Grid>
  );
};
export default MyOfferSummary;
