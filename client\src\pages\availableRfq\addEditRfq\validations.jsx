export const validateForm = (rfqDetails, materials) => {
  let formIsValid = true;
  const newErrors = {
    title: '',
    client: '',
    deadLine: '',
    deliveryDate: '',
    clientRfqNumber: '',
    materials: materials?.map(() => ({
      partNumber: '',
      brand: '',
      quantity: '',
      description: ''
    }))
  };

  // Validate RFQ details
  if (!rfqDetails?.title) {
    newErrors.title = 'Title is required';
    formIsValid = false;
  }
  if (!rfqDetails?.client) {
    newErrors.client = 'Client is required';
    formIsValid = false;
  }
  if (!rfqDetails?.deadLine) {
    newErrors.deadLine = 'Deadline is required';
    formIsValid = false;
  }
  if (!rfqDetails?.deliveryDate) {
    newErrors.deliveryDate = 'Delivery Date is required';
    formIsValid = false;
  }

  // Validate materials
  materials?.forEach((material, index) => {
    if (index >= 0) {
      if (!material?.partNumber) {
        newErrors.materials[index].partNumber = 'Part Number is required';
        formIsValid = false;
      }
      if (!material?.brand) {
        newErrors.materials[index].brand = 'Brand is required';
        formIsValid = false;
      }
      if (!material?.quantity || material?.quantity < 1) {
        newErrors.materials[index].quantity = 'Quantity must be at least 1';
        formIsValid = false;
      }
      if (!material?.description) {
        newErrors.materials[index].description = 'Description is required';
        formIsValid = false;
      }
      //   if (!material?.notes) {
      //     newErrors.materials[index].notes = 'Notes is required';
      //     formIsValid = false;
      //   }
      // if (!material?.position) {
      //     newErrors.materials[index].position = 'Position is required';
      //     formIsValid = false;
      // }
    }
  });

  return { formIsValid, newErrors };
};
