const Joi = require("joi");
const {
  stringValidation,
  numberValidation,
  statusValidation,
  dateValidation,
  booleanValidation,
} = require("../utils/validator.util");

const getAllRFQSchema = Joi.object({
  limit: numberValidation.optional(),
  page: numberValidation.optional(),
}).unknown();

const getSingleRFQSchema = Joi.object({
  id: stringValidation,
});

const updateStatusSchema = Joi.object({
  rfqId: stringValidation,
  status: statusValidation,
  userId: Joi.when("status", {
    switch: [
      { is: "IRRELEVANT", then: stringValidation },
      { is: "RESERVED", then: stringValidation },
    ],
    otherwise: Joi.optional(),
  }),
});

const generateRFQSchema = Joi.object({
  RFQ_Number: stringValidation.allow("").optional(),
  RFQ_Name: stringValidation,
  Company_Name: stringValidation,
  Portal: stringValidation.allow("").optional(),
  Priority: stringValidation.allow("").optional(),
  Deadline: dateValidation,
  Address_Information: stringValidation.allow("").optional(),
  Additional_Notes: stringValidation.allow("").optional(),
  Delivery_Date: dateValidation.allow("").optional(),
  URL: stringValidation.allow("").optional(),
  Client_Id: stringValidation,
  materials: Joi.array()
    .required()
    .items(
      Joi.object({
        Part_Number: stringValidation,
        Quantity_Required: numberValidation,
        Material_Description: stringValidation,
        Notes: stringValidation.allow("").optional(),
        // Position: numberValidation.allow("").optional(),
        brand: stringValidation,
      })
    ),
});

const updateRFQSchema = Joi.object({
  rfqId: stringValidation,
  RFQ_Number: stringValidation.allow("").optional(),
  RFQ_Name: stringValidation,
  Company_Name: stringValidation.allow("").optional(),
  Portal: stringValidation.allow("").optional(),
  Priority: stringValidation.allow("").optional(),
  Deadline: dateValidation,
  Address_Information: stringValidation.allow("").optional(),
  Additional_Notes: stringValidation.allow("").optional(),
  Delivery_Date: dateValidation.allow("").optional(),
  URL: stringValidation.allow("").optional(),
  Client_Id: stringValidation,
  existingMaterials: Joi.array()
    .required()
    .items(
      Joi.object({
        Material_ID: stringValidation,
        Part_Number: stringValidation,
        Quantity_Required: numberValidation,
        Material_Description: stringValidation,
        Notes: stringValidation.allow("").optional(),
        // Position: numberValidation.allow("").optional(),
        brand: stringValidation,
      })
    ),
  newMaterials: Joi.array()
    .required()
    .items(
      Joi.object({
        Part_Number: stringValidation,
        Quantity_Required: numberValidation,
        Material_Description: stringValidation,
        Notes: stringValidation.allow("").optional(),
        // Position: numberValidation,
        brand: stringValidation,
      })
    ),
});

const discardRFQSchema = Joi.object({
  id: stringValidation,
});

const releaseRFQSchema = Joi.object({
  rfqId: stringValidation,
  changeStatusToNew: booleanValidation,
  reason: stringValidation,
});

module.exports = {
  getAllRFQSchema,
  getSingleRFQSchema,
  updateStatusSchema,
  updateRFQSchema,
  generateRFQSchema,
  discardRFQSchema,
  releaseRFQSchema,
};
