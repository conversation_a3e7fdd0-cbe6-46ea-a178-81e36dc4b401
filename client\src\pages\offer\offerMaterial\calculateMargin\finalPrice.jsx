import { Box, Typography, Grid, Divider } from '@mui/material';
import LocalAtmIcon from '@mui/icons-material/LocalAtm';
import InputField from 'pages/component/inputField';
import { useSelector } from 'react-redux';

const FinalPriceBox = ({ handleInputChange, rowData, quoteInfo }) => {
  const offerData = useSelector((state) => state.offer);
  const breakdownFields = [
    { label: 'Target Price', value: offerData.calculatedMargin?.result?.desiredUnitPrice || '' },
    { label: 'Base Cost', value: rowData?.unitPrice },
    { label: 'Shipping', value: rowData?.sh },
    { label: 'Tax', value: rowData?.tax },
    { label: 'Handling', value: 150 }
  ];

  const inputFields = [
    { type: 'number', name: 'unitOfferPrice', label: 'Enter Desired Unit Offer Price', placeholder: 'Unit Offer Price' }
  ];

  const renderField = ({ type, name, placeholder, errors, value }) => {
    switch (type) {
      case 'number':
        return (
          <InputField
            type="number"
            name={name}
            placeholder={placeholder}
            errors={errors}
            value={quoteInfo?.[name] || ''}
            onChange={(e) => (!e.target.value || e.target.value > 0) && handleInputChange(name, parseInt(e.target.value))}
            fullWidth
          />
        );
    }
  };

  return (
    <Box minWidth="400px">
      <Box display="flex" alignItems="center" mb={2}>
        <LocalAtmIcon color="primary" sx={{ mr: 1 }} />
        <Typography variant="h5" color="primary">
          Set Final Price to Calculate Margin
        </Typography>
      </Box>

      <Divider sx={{ mb: 2 }} />

      <Grid container spacing={2}>
        {inputFields.map((field, index) => (
          <Grid item xs={12} key={index}>
            <Typography variant="subtitle1" color="text.secondary" gutterBottom>
              {field.label}
            </Typography>
            {renderField(field, handleInputChange)}
          </Grid>
        ))}
        {offerData?.calculatedMargin?.result?.calculatedMargin && (
          <>
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="body2" color="secondary">
                Calculation
              </Typography>
              <Box mt={1}>
                {breakdownFields.map(({ label, value }, idx) => (
                  <Box display="flex" key={idx}>
                    <Box minWidth={'200px'}>
                      <Typography key={idx} variant="body2">
                        {label}
                      </Typography>
                    </Box>
                    <Typography key={idx} variant="subtitle2" color={'text.secondary'}>
                      {value !== undefined ? value : '-'}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Box display="flex">
                <Box minWidth={'200px'}>
                  <Typography variant="body2">Calculated Margin:</Typography>
                </Box>
                <Typography variant="body2">
                  <span style={{ color: '#9c27b0' }}>{offerData.calculatedMargin?.result?.calculatedMargin}%</span>
                </Typography>
              </Box>
            </Grid>
          </>
        )}
      </Grid>
    </Box>
  );
};

export default FinalPriceBox;
