/* eslint-disable no-empty */
import moment from 'moment';
import { jwtDecode } from 'jwt-decode';
import { logoutSuccess } from 'redux/reducers/loginReducer';
import { ALERT_SUCCESS } from 'redux/reducers/alertReducer';
import {
  APPROVED,
  centralizedPageUrl,
  clientPageUrl,
  COUNTRY_MANAGER,
  dashboardPageUrl,
  emailPricesRequestsPageUrl,
  IRRELEVANT,
  KAM,
  monitorRfqPageUrl,
  priceTablePageUrl,
  QUOTING,
  RESERVED,
  settingPageUrl,
  supplierUrl
} from './constant';
import { get, isEqual, sortBy } from 'lodash';
import { getUserDetail } from './auth';
import dayjs from 'dayjs';
import { getTimeSlots } from 'redux/reducers/settingReducer';

export const getItemFromLocalStorage = (key, defaultValue = []) => {
  try {
    if (!localStorage.hasOwnProperty(key)) {
      return defaultValue;
    }
    const item = localStorage.getItem(key);
    return JSON.parse(item);
  } catch (error) {
    return [];
  }
};

export const setItemToLocalStorage = (key, value) => {
  try {
    if (value) {
      localStorage.setItem(key, JSON.stringify(value));
    }
  } catch (error) {}
};

export const removeItemFromLocalStorage = (key) => {
  try {
    if (!localStorage.hasOwnProperty(key)) {
      return;
    }
    localStorage.removeItem(key);
  } catch (error) {}
};

export const convertDateToStringFormat = (date, format, showToday) => {
  if (date) {
    const today = moment().startOf('day');
    const inputDate = moment(date).startOf('day');

    if (showToday && inputDate.isSame(today)) {
      return 'Today';
    }

    if (format === 'dd/mm/yyyy') {
      return moment(date).format('DD/MM/YYYY');
    }
    if (format) {
      return moment(date).format('YYYY-MM-DD');
    }
    return moment(date).format('MM/DD/YYYY');
  }
  return '';
};

export const getDateWithTime = (dateString) => {
  const date = new Date(dateString);
  const offset = date.getTimezoneOffset();
  const localDate = moment(dateString).utcOffset(-offset);

  return localDate.format('MMM D, YYYY, h:mm A');
};

export const formatNumber = (value) => (value && !isNaN(value) ? parseFloat(value) : 0);
export const formattedPrice = (price, showDecimals = false) => {
  let value = formatNumber(price);
  if (value) {
    if (showDecimals) {
      return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
    return Math.floor(value)
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  } else {
    return showDecimals ? '0.00' : '0';
  }
};

export const logoutUser = (dispatch, navigate) => {
  removeItemFromLocalStorage('token');
  dispatch(logoutSuccess());
  if (navigate) {
    navigate('/login');
  } else {
    window.location.href = '/login';
  }
};

export const notification = (success, message, error = false) => ({
  type: ALERT_SUCCESS,
  payload: { success, message, error }
});
export const showAlert = (dispatch, success, message, error = false, warning = false) => {
  dispatch({
    type: ALERT_SUCCESS,
    payload: { success, message, error, warning }
  });
};

export const badgeColor = (color) => {
  if (color === 'Done') return 'success';
  else return 'warning';
};

export const getSupplierName = (supplier, supplierList, val) => {
  const selectedSupplier = supplierList?.filter((supp) => supp?.value == supplier?.supplier);

  if (selectedSupplier && selectedSupplier?.length > 0) {
    return selectedSupplier[0]?.label;
  } else {
    return '';
  }
};

export const generateUniqueId = () => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  return `${timestamp}-${random}`;
};

export const checkIsRfqReserved = (status) => {
  return status === RESERVED || status === QUOTING;
};

export const compareTwoArrayOfObjects = (firstArrayOfObjects, secondArrayOfObjects, specificKey = null) => {
  if (specificKey) {
    return isEqual(
      firstArrayOfObjects?.map((item) => item[specificKey]),
      secondArrayOfObjects?.map((item) => item[specificKey])
    );
  } else {
    // Compare based on all keys
    return isEqual(
      sortBy(firstArrayOfObjects, Object.keys(firstArrayOfObjects[0])),
      sortBy(secondArrayOfObjects, Object.keys(secondArrayOfObjects[0]))
    );
  }
};

export const handleReserveClick = async (dispatch, isManuallyAdded) => {
  const user = getUserDetail();
  const response = await dispatch(getTimeSlots(user?.userId));
  const { success, data, message } = response?.payload || {};

  if (user?.role === KAM && !isManuallyAdded) {
    if (success) {
      let today = new Date();
      let dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' }).toUpperCase();
      const currentTime = dayjs().format('HH:mm');

      let daySlots = data?.filter(({ Day }) => Day === dayOfWeek);

      if (daySlots && daySlots.length > 0) {
        let isTimeInSlot = false;
        let availableTimeRange = '';

        daySlots.forEach((slot) => {
          let { FromTime, ToTime } = slot;

          let fromTime = dayjs.unix(Number(FromTime)).format('HH:mm');
          let toTime = dayjs.unix(Number(ToTime)).format('HH:mm');

          if (!fromTime || !toTime) {
            showAlert(dispatch, false, 'Invalid time format.', true);
            return false;
          }

          let formattedFromTime = dayjs.unix(Number(FromTime)).format('hh:mm A'); // Change format to AM/PM
          let formattedToTime = dayjs.unix(Number(ToTime)).format('hh:mm A'); // Change format to AM/PM

          // Check if current time falls within the slot
          if (currentTime >= fromTime && currentTime <= toTime) {
            isTimeInSlot = true;
          }

          // Capture the time range for the alert
          availableTimeRange += `, ${formattedFromTime} to ${formattedToTime}`;
        });

        if (isTimeInSlot) {
          return true;
        } else {
          // Remove the initial comma and space from availableTimeRange
          availableTimeRange = availableTimeRange.slice(2);

          let message = `On ${dayOfWeek}, RFQ reservation is only available from ${availableTimeRange}.`;
          showAlert(dispatch, false, message, true);
          return false;
        }
      } else {
        showAlert(dispatch, false, 'User Time Slots not found.', true);
        return false;
      }
    } else {
      showAlert(dispatch, false, message || '', true);
      return false;
    }
  } else {
    return true;
  }
};

export const getPageTitle = (pageUrl, title) => {
  if (pageUrl.startsWith('/price-table')) {
    return 'Price Table';
  }
  if (pageUrl.startsWith(dashboardPageUrl)) {
    return title;
  }
  switch (pageUrl) {
    case supplierUrl:
      return 'Suppliers';
    case monitorRfqPageUrl:
      return 'Supplier List';
    case clientPageUrl:
      return 'Clients';
    case settingPageUrl:
      return 'Settings';
    case centralizedPageUrl:
      return title;
    case dashboardPageUrl:
      return title;
    case emailPricesRequestsPageUrl:
      return 'Email Price Requests';
    default:
      return '';
  }
};

export const getApprovedSupplier = (suppliers) => {
  return suppliers?.filter((supplier) => supplier?.Status === APPROVED) || [];
};
export const getApprovedClients = (clients) => {
  return clients?.filter((client) => client?.Status === APPROVED) || [];
};

export const formatRelativeTime = (timestamp) => {
  const now = dayjs();
  const createdAt = dayjs(timestamp);
  const diffInMinutes = now.diff(createdAt, 'minute');
  const diffInHours = now.diff(createdAt, 'hour');
  const diffInDays = now.diff(createdAt, 'day');

  if (diffInMinutes === 0) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} min ago`;
  } else if (diffInHours < 12) {
    return `${diffInHours} hr ago`;
  } else if (diffInHours < 24) {
    return '1 day ago';
  } else if (diffInDays <= 1) {
    return `${diffInDays} day ago`;
  } else {
    return createdAt.format('DD MMM');
  }
};

export const isCountryManager = () => {
  const user = getUserDetail();
  return user?.role === COUNTRY_MANAGER;
};
