import React from 'react';
import PropTypes from 'prop-types';
import DynamicAutocomplete from 'pages/component/autoComplete';

const TextAutocomplete = ({ data, handleFilterChange }) => {
  const currentValue = data?.data || '';
  const selectedOption = data?.options?.find((opt) => opt.value === currentValue) || null;

  return (
    <div className="filter-input">
      <DynamicAutocomplete
        options={data.options || []}
        label=""
        placeholder="Enter your Search"
        value={selectedOption}
        getOptionLabel={(option) => option?.label || ''}
        onChange={(_, newValue) =>
          handleFilterChange({
            target: {
              value: newValue ? newValue.value : ''
            }
          })
        }
        isNotRemoveButton={false}
        isLoading={false}
      />
    </div>
  );
};

TextAutocomplete.propTypes = {
  data: PropTypes.object.isRequired,
  handleFilterChange: PropTypes.func.isRequired
};

export default TextAutocomplete;
