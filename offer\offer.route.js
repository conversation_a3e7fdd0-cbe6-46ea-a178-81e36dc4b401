const { validate } = require("../middlewares/validation.middleware");
const {
  getOfferList,
  generateOffer,
  updateOfferStatus,
  calculateOffer,
  getOffer,
  getMultipleSpecsSheet,
  getOfferCalculations,
  winOffer,
  mockMondayFetchData,
  copyRFQ,
} = require("./offer.controller");
const {
  generateOfferSchema,
  updateStatusSchema,
  calculateOfferSchema,
  getOfferSchema,
  specsSheetSchema,
  getAllOfferSchema,
  winOfferSchema,
  copyRFQSchema,
} = require("./offer.validation");
const { upload } = require("../middlewares/multer.middleware.js");

const router = require("express").Router();

router.post("/get", validate(getAllOfferSchema, "body"), getOfferList);

router.get("/get/:id", validate(getOfferSchema), getOffer);

router.post("/add", validate(generateOfferSchema, "body"), generateOffer);

router.post(
  "/calculate",
  validate(calculateOfferSchema, "body"),
  calculateOffer
);

router.patch(
  "/update/status",
  validate(updateStatusSchema, "body"),
  updateOfferStatus
);

router.get(
  "/specs-sheet/:offerId",
  validate(specsSheetSchema, "params"),
  getMultipleSpecsSheet
);

router.get("/getOfferCalculations", getOfferCalculations);

router.patch(
  "/winOffer",
  upload.single("OC_File"),
  validate(winOfferSchema, "body"),
  winOffer
);

router.get("/mockMonday", mockMondayFetchData);

router.patch("/copy/rfq", validate(copyRFQSchema, "body"), copyRFQ);


module.exports = router;
