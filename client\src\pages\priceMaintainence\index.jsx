// PriceMaintenance.js
import React, { useEffect, useState } from 'react';
import DynamicStepper from 'pages/component/stepper';
import ImportFile from './importFile';
import SelectSupplier from './selectSupplier';
import './priceMaintainence.css'
import { getSupplierList } from 'redux/reducers/offerReducer';
import { useDispatch, useSelector } from 'react-redux';
import UpdatePrice from './updatePrice';
import { getApprovedSupplier, showAlert } from 'utils/helper';
import { requiredFields, uniqueKeyFields } from './constant';
import { updatePriceMaitenance } from 'redux/reducers/priceMaitenanceReducer';
import Loader from 'components/Loader';
import { get } from 'lodash';
import { validateUniqueObject } from 'pages/component/validation';
import BatchDetailsCard from './updateResult';

const initialValue = {
  action: 'update'
}
const PriceMaintenance = () => {
  const offerDetail = useSelector((state) => state.offer);
  const priceMaintainence = useSelector((state) => state.priceMaitenance);
  const [activeStep, setActiveStep] = useState(0);
  const [successCount, setSuccess] = useState(0);
  const [error, setError] = useState({});
  const [selectedValue, setSelectedValue] = useState(initialValue);
  const dispatch = useDispatch()
  const steps = [
    {
      label: 'Step 1',
      content: <ImportFile setSelectedValue={setSelectedValue} selectedValue={selectedValue} />
    },
    {
      label: 'Step 2',
      content: <SelectSupplier setSelectedValue={setSelectedValue} selectedValue={selectedValue} supplierList={getApprovedSupplier(offerDetail?.supplierList || [])?.map((supplier) => ({ label: supplier?.Name, id: supplier?.SupplierID })) || []} />
    },
    {
      label: 'Step 3', content: successCount ? <BatchDetailsCard details={error} /> : <UpdatePrice selectedValue={selectedValue} setSelectedValue={setSelectedValue} />
    },
  ];

  useEffect(() => {
    dispatch(getSupplierList());
  }, [])

  const handleNext = async () => {
    if (activeStep + 1 < steps?.length) {
      // const { error, message } = validateUniqueObject(selectedValue?.fileData, requiredFields, uniqueKeyFields);
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    } else {
      if (!successCount) {
        const { fileData, supplier, action } = selectedValue || {}
        let payload = {
          data: fileData?.map((res) => ({ PartNumber: res?.PARTNUMBER, Price: res?.UNITPRICE ? Number(res?.UNITPRICE || 0) : 0, Currency: res?.CURRENCY })),
          SupplierID: supplier?.id?.toString(),
          Action: action
        }
        const response = await dispatch(updatePriceMaitenance(payload))
        const { success, data } = get(response, 'payload', false);
        if (success) {
          setError({ ...data })
          setSuccess(true)
        }
      } else {
        handleReset()
      }
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleReset = () => {
    setActiveStep(0);
    setSelectedValue(initialValue)
    setSuccess(false)
  };


  const checkDisableNextBtn = () => {
    if (activeStep === 0) {
      return !selectedValue?.file
    }
    if (activeStep === 1) {
      return !selectedValue?.supplier
    }

    return priceMaintainence?.loading
  }
  const checkDisableBackBtn = () => {
    if (activeStep === 2) {
      return successCount
    } else return false
  }

  const isLoading = () => {
    return priceMaintainence?.loading
  }

  return (
    <div >
      {isLoading() && <Loader />}
      <DynamicStepper
        steps={steps}
        activeStep={activeStep}
        handleNext={handleNext}
        handleBack={handleBack}
        handleReset={handleReset}
        orientation="vertical"
        SubmitText={successCount && 'Finish'}
        disabled={checkDisableNextBtn()}
        disabledBackBtn={checkDisableBackBtn()}
      />
    </div>
  );
};

export default PriceMaintenance;


