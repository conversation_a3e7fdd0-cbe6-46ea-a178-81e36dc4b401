const Joi = require("joi");
const {
  stringValidation,
  numberValidation,
  currencyValidation,
  conditionValidation,
  dateValidation,
  emailValidation,
  urlValidation,
  booleanValidation,
} = require("../utils/validator.util");

const getCentralizedMaterialListSchema = Joi.object({
  deadlineFrom: stringValidation.optional().allow(""),
  deadlineTo: stringValidation.optional().allow(""),
  brand: stringValidation.optional().allow(""),
  status: stringValidation.optional().allow("").valid("0", "1", "2", "3"),
  page: numberValidation,
  limit: numberValidation,
  searchQuery: stringValidation.optional().allow(""),
});

module.exports = {
  getCentralizedMaterialListSchema,
};
