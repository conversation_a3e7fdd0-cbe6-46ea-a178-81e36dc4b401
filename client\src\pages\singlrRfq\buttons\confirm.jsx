import { Button } from '@mui/material';
import Loader from 'components/Loader';
import { get } from 'lodash';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import {
  addMaterialAction,
  confirmMaterialAction,
  getSingleRfq,
  searchMaterialAction,
  setSimilarPartNumber,
  setSinglePartNumber,
  updateMaterialAction
} from 'redux/reducers/singleRfq';
import MaterialModal from '../../materialModel/materialModal';
import { checkIsRfqReserved, showAlert } from 'utils/helper';
import { rfqNotReservedMessage } from 'utils/validationMessage';

const Confirm = ({ order, variant, rfqStatus, loading }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const singleRfq = useSelector((state) => state.singleRfq);
  const [similarMaterials, setSimilarMaterials] = useState([]);
  const [singlePartNumber, setPartNumber] = useState({});
  const [isEdit, setIsEdit] = useState(false);
  const dispatch = useDispatch();
  const rfqId = useParams()?.id;
  const handleOpen = () => {
    setIsModalOpen(true);
  };

  const handleClose = () => {
    setIsModalOpen(false);
    setSimilarMaterials([]);
    setPartNumber({});
    setIsEdit(false);
  };
  const confirmMaterial = async (material) => {
    const { partNumber, manufacturer, name, description, materialId } = material || {};
    let payload = {
      materialId: order?.Material_ID?.toString(),
      partNumber,
      brand: manufacturer,
      rfqId: rfqId.toString(),
      description
    };
    const result = await dispatch(confirmMaterialAction(payload));
    if (result.payload.success) {
      dispatch(getSingleRfq(rfqId));
      handleClose();
    }
  };
  const addMaterial = async (material) => {
    const { partNumber, manufacturer, name, description, materialId } = material || {};
    const { Quantity_Requested } = order || {};
    let payload = {
      materialId: order?.Material_ID?.toString(),
      rfqId: rfqId,
      partNumber: partNumber,
      manufacturer: manufacturer,
      quanity: Quantity_Requested,
      productName: name,
      description: description
    };
    let result;
    if (isEdit) {
      result = await dispatch(updateMaterialAction(payload));
    } else {
      result = await dispatch(addMaterialAction(payload));
    }
    if (result.payload.success) {
      dispatch(getSingleRfq(rfqId));
      handleClose();
    }
  };

  const searchMaterial = async (partNumber, isSimilar) => {
    if (partNumber && partNumber?.length >= 3) {
      const result = await dispatch(
        searchMaterialAction({ partNumber: partNumber, materialId: order?.Material_ID?.toString(), isSimilar })
      );

      const { success, data } = get(result, 'payload');
      if (success) {
        if (isSimilar) {
          setSimilarMaterials(data || []);
        } else {
          if (data) {
            setIsEdit(true);
            setPartNumber({ ...data });
          } else {
            setIsEdit(false);
            setPartNumber({});
          }
        }
      } else {
        if (isSimilar) {
          setSimilarMaterials(data || []);
        } else {
          setIsEdit(false);
        }
      }
    } else {
      let message = !partNumber
        ? 'Part Number is required'
        : partNumber?.length < 3
          ? 'Part Number should contain at least 3 characters'
          : '';
      showAlert(dispatch, false, message, true);
    }
  };

  const useMaterial = (material) => {
    setPartNumber({ ...material });
  };

  return (
    <>
      <MaterialModal
        useMaterial={(material) => useMaterial(material)}
        loading={get(singleRfq, 'loading')}
        material={order}
        icon={get(order, 'icon', '')}
        title={get(order, 'RFQ_Name', '')}
        open={isModalOpen}
        handleClose={handleClose}
        searchMaterial={(partNumber, isSimilar) => searchMaterial(partNumber, isSimilar)}
        similarMaterials={similarMaterials}
        confirmButton={(updatedValues) => confirmMaterial(updatedValues)}
        addMaterial={(material) => addMaterial(material)}
        singlePartNumber={singlePartNumber}
        isEdit={isEdit}
      />
      <Button variant={variant} className="material-request-btn" size="small" onClick={() => handleOpen()} disabled={loading}>
        Confirm
      </Button>
    </>
  );
};
export default Confirm;
