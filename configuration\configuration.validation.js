const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  emailValidation,
  urlValidation,
} = require("../utils/validator.util");

const updateAllConfigurationsSchema = Joi.object({
  data: Joi.array()
    .required()
    .items(
      Joi.object({
        ID: numberValidation,
        Day: stringValidation,
        FromTime: stringValidation,
        ToTime: stringValidation,
      })
    ),
});

const updateApproverSchema = Joi.object({
  email: emailValidation,
});

const addTimeSlotSchema = Joi.object({
  UserID: stringValidation,
  timeSlots: Joi.array()
    .required()
    .items(
      Joi.object({
        Day: stringValidation,
        FromTime: stringValidation,
        ToTime: stringValidation,
      })
    ),
});

const getTimeSlotsSchema = Joi.object({
  UserID: stringValidation,
});

const addDashboardSchema = Joi.object({
  Name: stringValidation,
  Link: urlValidation,
  visible_to_country_manager: booleanValidation.optional(),
  visible_to_kam: booleanValidation.optional(),
  visible_to_supervisor: booleanValidation.optional(),
});

const editDashboardSchema = Joi.object({
  ID: numberValidation,
  Name: stringValidation,
  Link: urlValidation,
  visible_to_country_manager: booleanValidation.optional(),
  visible_to_kam: booleanValidation.optional(),
  visible_to_supervisor: booleanValidation.optional(),
});

const deleteDashboardSchema = Joi.object({
  ID: stringValidation,
});

const getContentOfAdjustmentToLegalTermsSchema = Joi.object({
  language: stringValidation,
});

const addOrUpdateContentOfAdjustmentToLegalTermsSchema = Joi.object({
  language: stringValidation,
  content: stringValidation,
});

module.exports = {
  updateAllConfigurationsSchema,
  updateApproverSchema,
  addTimeSlotSchema,
  getTimeSlotsSchema,
  addDashboardSchema,
  editDashboardSchema,
  deleteDashboardSchema,
  getContentOfAdjustmentToLegalTermsSchema,
  addOrUpdateContentOfAdjustmentToLegalTermsSchema,
};
