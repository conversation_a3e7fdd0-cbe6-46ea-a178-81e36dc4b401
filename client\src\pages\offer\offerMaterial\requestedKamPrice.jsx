import { Box, Button, Grid, Typography } from '@mui/material';
import AlertDialog from 'pages/component/dialogbox';
import TextAreaComponent from 'pages/component/textArea';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getOfferDetail, requestPriceAction } from 'redux/reducers/offerReducer';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

const RequestKamPrice = ({ material, rfqId, loading }) => {
  const [loader, setLoader] = useState(false);
  const offer = useSelector((state) => state.offer);
  const [open, setOpen] = useState(false);
  const [notes, setNotes] = useState('');
  const dispatch = useDispatch();

  useEffect(() => {
    setLoader(loading);
  }, [loading]);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = async () => {
    setOpen(false);
    setNotes('');
  };

  const handleSubmit = async () => {
    const { brand, Part_Number, Material_Description, Quantity_Required, materialId } = material || {};
    const { Deadline } = offer?.data || {};

    const payload = {
      Material_ID: materialId?.toString(),
      Notes: notes,
      RFQ_ID: rfqId,
    };

    const response = await dispatch(requestPriceAction(payload));
    const { success } = response?.payload || {};

    if (success) {
      dispatch(getOfferDetail(rfqId));
      handleClose();
    }
  };

  const actionBtns = [
    { label: 'Cancel', onClick: () => handleClose(), color: 'error', disabled: loader, variant: 'contained' },
    { label: 'Confirm', onClick: () => handleSubmit(), color: 'primary', disabled: loader, variant: 'contained' },
  ];

  const renderRequestPriceDialog = () => {
    return (
      <div className="request-price-container">
        <Grid container spacing={2} direction="column" alignItems="flex-start">
          <Grid item xs={12}>
            <Typography variant='h4' color='primary' className="typography-title" gutterBottom>
              Request Price
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <Typography variant='body1' color='textSecondary' className="typography-body">
              Please Add Additional notes or confirm to proceed.
            </Typography>
          </Grid>

          <Grid item xs={12} style={{ width: '100%' }}>
            <TextAreaComponent
              name="notes"
              value={notes !== undefined ? notes : ''}
              className="name-text-field textarea-field requestprice-textarea-field"
              onChange={(name, e) => setNotes(e.target.value)}
              placeholder="Enter Additional Note (Optional)"
              minRows={4}
            />
          </Grid>
        </Grid>
      </div>

    );
  };
  const { centralizedRequestID } = material || {};

  return (
    <>
      <AlertDialog
        buttons={actionBtns}
        Component={renderRequestPriceDialog()}
        open={open}
        borderRadius="20px"
      />

      <Button
        variant={centralizedRequestID ? 'outlined' : 'contained'}
        onClick={() => !centralizedRequestID && handleOpen()}
        disabled={loader}
        color={centralizedRequestID ? 'success' : 'primary'}
        startIcon={centralizedRequestID && <CheckCircleIcon />}
      >
        {centralizedRequestID ? 'Request Submitted' : 'Request Price'}
      </Button>
    </>
  );
};

export default RequestKamPrice;
