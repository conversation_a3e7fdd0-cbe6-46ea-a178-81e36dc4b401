const {
  DATASET_ID_SCRAPE,
  DATASET_ID_MAIN,
  TAX_PERCENT,
  HANDLING_COST,
  DATASET_ID_LANDINGZONE,
  BASE_URL,
  AUSTRALIA_HANDLING_COST
} = require("../constants");
const {
  mainDataset,
  bigQueryClient,
  scrapedDataset,
  landingZoneDataset,
  storageClient,
} = require("../db");
const { apiHandler, apiError, apiResponse } = require("../utils/api.util");
const {
  NOT_FOUND,
  FETCH,
  ADD_SUCCESS,
  STATUS_SUCCESS,
  INVALID,
  CUSTOM_SUCCESS,
  EXISTS,
  CUSTOM_ERROR,
  UPDATE_SUCCESS,
  SUCCESS,
  REQUIRED,
  SERVER_ERROR,
} = require("../utils/message.util");
const {
  isEmpty,
  generateID,
  round,
  setDate,
  calculateOfferAmount,
  applyFloor,
  uploadFileToGCP,
  updateRFQOfferStatus,
  insertWonRFQ,
  deleteFile,
  insertIntoMonday,
} = require("../utils/misc.util");
const {
  mondayRecordUploadFailureTemplate,
  mondayRecordUploadSuccessTemplate,
} = require("../utils/templates");
const { sendMail } = require("../utils/email.util");

const getOfferList = apiHandler(async (req, res) => {
  let { limit, page, filterData } = req.body;
  const { userId, role } = req.user;

  let whereExpression = ``;
  let orderByExpression = ``;
  if (role === "KAM") {
    whereExpression = `WHERE rs.AssignedUser = @userId`;
  }
  filterData?.forEach((filter) => {
    if (!isEmpty(filter.data)) {
      if (filter.type === "text") {
        if (filter.heading === "Search") {
          isEmpty(whereExpression)
            ? (whereExpression = `
                        WHERE (
                            TRIM(UPPER(r.RFQ_Name)) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(r.Company_Name)) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(CAST(r.RFQ_ID AS STRING))) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(CAST(r.RFQ_Number AS STRING))) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(CAST(r.Portal AS STRING))) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(CAST(m.Part_Number AS STRING))) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(pc.ConfirmedBrand)) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(CONCAT(u.FirstName, ' ', u.LastName))) LIKE UPPER('%${filter.data.trim()}%')
                        )
                    `)
            : (whereExpression += `
                        AND (
                            TRIM(UPPER(r.RFQ_Name)) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(r.Company_Name)) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(CAST(r.RFQ_ID AS STRING))) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(CAST(r.RFQ_Number AS STRING))) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(CAST(r.Portal AS STRING))) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(CAST(m.Part_Number AS STRING))) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(pc.ConfirmedBrand)) LIKE UPPER('%${filter.data.trim()}%')
                            OR TRIM(UPPER(CONCAT(u.FirstName, ' ', u.LastName))) LIKE UPPER('%${filter.data.trim()}%')
                        )
                    `);
        }
      }

      if (filter.type === "sort") {
        if (filter.filterName === "offerDateSort") {
          let offerDateFilter;
          switch (filter.data) {
            case "last-7-days":
              offerDateFilter = `${filter.field} >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)`;
              break;
            case "last-month":
              offerDateFilter = `${filter.field} >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH)`;
              break;
            case "last-3-months":
              offerDateFilter = `${filter.field} >= DATE_SUB(CURRENT_DATE(), INTERVAL 3 MONTH)`;
              break;
            case "last-6-months":
              offerDateFilter = `${filter.field} >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH)`;
              break;
            default:
              offerDateFilter = `${filter.field} >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)`;
              break;
          }

          isEmpty(whereExpression)
            ? (whereExpression = `
          WHERE ( ${offerDateFilter} )`)
            : (whereExpression += ` AND ( ${offerDateFilter} )`);
        }
        if (filter.filterName === "freeSort") {
          let freeSortFilter = `${filter.field} ${filter.data} NULLS LAST`;

          isEmpty(orderByExpression)
            ? (orderByExpression = `ORDER BY ${freeSortFilter}`)
            : (orderByExpression += `, ${freeSortFilter}`);
        }
      }
    }

    if (filter.type === "select") {
      if (filter.heading === "Open") {
        isEmpty(whereExpression)
          ? (whereExpression = `WHERE (${filter.field} != 'WON')`)
          : (whereExpression += ` AND (${filter.field} != 'WON')`);
      }
    }
  });

  const [existingOffersCount] = await bigQueryClient.query({
    query: `
      SELECT COUNT(DISTINCT o.OfferID) AS row_count
      FROM ${DATASET_ID_MAIN}.Offers AS o
      LEFT JOIN (
          SELECT RFQID, AssignedUser, StatusChangeDate, 
                ROW_NUMBER() OVER (PARTITION BY RFQID ORDER BY StatusChangeDate DESC) AS rn
          FROM ${DATASET_ID_MAIN}.RFQ_Status
      ) AS rs ON o.RFQID = rs.RFQID AND rs.rn = 1
      LEFT JOIN ${DATASET_ID_SCRAPE}.RFQ AS r ON rs.RFQID = r.RFQ_ID
      LEFT JOIN ${DATASET_ID_MAIN}.Users AS u ON rs.AssignedUser = u.UserID
      LEFT JOIN ${DATASET_ID_SCRAPE}.Material as m ON m.RFQ_ID = r.RFQ_ID
      LEFT JOIN ${DATASET_ID_MAIN}.Predictions_Confirmations as pc ON (m.Material_ID = pc.MaterialID AND m.RFQ_ID = pc.RFQID)
      ${whereExpression}
    `,
    params: {
      userId: parseInt(userId),
    },
  });
  const [existingOffers] = await bigQueryClient.query({
    query: `
      SELECT r.RFQ_ID, r.RFQ_Name, r.RFQ_Number, r.RFQ_Date, r.Portal, r.Delivery_Date, r.URL, r.Company_Name, 
            r.Created_From_RFQ_ID, o.TotalOfferPrice, o.Status, o.OfferCurrency, er.Value AS exchangeRate, 
            o.OfferID, o.OfferDate, CONCAT(u.FirstName, ' ', u.LastName) AS role
      FROM ${DATASET_ID_MAIN}.Offers AS o
      LEFT JOIN (
          SELECT RFQID, AssignedUser, StatusChangeDate, 
                ROW_NUMBER() OVER (PARTITION BY RFQID ORDER BY StatusChangeDate DESC) AS rn
          FROM ${DATASET_ID_MAIN}.RFQ_Status
      ) AS rs ON o.RFQID = rs.RFQID AND rs.rn = 1
      LEFT JOIN ${DATASET_ID_SCRAPE}.RFQ AS r ON rs.RFQID = r.RFQ_ID
      LEFT JOIN ${DATASET_ID_MAIN}.Users AS u ON rs.AssignedUser = u.UserID
      LEFT JOIN (
          SELECT DISTINCT er.From, er.To, er.Value
          FROM ${DATASET_ID_MAIN}.Exchange_Rates AS er
          WHERE UPPER(TRIM(er.To)) = 'CLP'
      ) AS er ON UPPER(TRIM(er.From)) = UPPER(TRIM(o.OfferCurrency))
      LEFT JOIN ${DATASET_ID_SCRAPE}.Material as m ON m.RFQ_ID = r.RFQ_ID
      LEFT JOIN ${DATASET_ID_MAIN}.Predictions_Confirmations as pc ON (m.Material_ID = pc.MaterialID AND m.RFQ_ID = pc.RFQID)
      ${whereExpression}
      QUALIFY ROW_NUMBER() OVER (PARTITION BY o.OfferID ORDER BY o.OfferDate DESC, r.RFQ_ID DESC) = 1
      ${orderByExpression}
      LIMIT @limit OFFSET @offset
    `,
    params: {
      userId: parseInt(userId),
      limit: limit || 10,
      offset: ((page || 1) - 1) * (limit || 10),
    },
  });

  if (isEmpty(existingOffers)) {
    return apiError(NOT_FOUND, "Offers", null, res);
  }

  existingOffers.forEach((offer) => {
    offer.row_count = existingOffersCount[0].row_count;
    if (!isEmpty(offer?.exchangeRate)) {
      offer.TotalOfferPrice = offer?.TotalOfferPrice * offer?.exchangeRate;
    }
  });

  const data = {
    count: existingOffersCount[0].row_count,
    offers: existingOffers,
  };

  return apiResponse(FETCH, "Offers", applyFloor(data), res);
});

const getOffer = apiHandler(async (req, res) => {
  const { id } = req.params;

  // SELECT r.RFQ_Name, r.isManuallyAdded, r.RFQ_Date, r.Delivery_Date, r.Deadline, r.Portal, r.RFQ_Number, r.URL, r.Company_Name, r.Created_By, r.Created_From_RFQ_ID, c.ClientID, rs.CurrentStatus, u.FirstName, u.LastName, c.Name, c.Address, c.Municipality, c.BusinessActivity, o.ValidFor, o.PaymentTerms, o.OfferCurrency, o.Notes, o.OfferID, o.Quotes, o.OfferDate, r.RFQ_ID, u.LastName, u.FirstName, u.Role, u.Email

  const [existingOfferData] = await bigQueryClient.query({
    query: `
      SELECT r.RFQ_Name, r.isManuallyAdded, r.RFQ_Date, r.Delivery_Date, r.Deadline, r.Portal, r.RFQ_Number, r.URL, r.Company_Name, r.Created_By, r.Created_From_RFQ_ID, c.ClientID, rs.CurrentStatus, u.FirstName, u.LastName, c.Name, c.Address, st.State , c.Municipality, c.BusinessActivity, o.ValidFor, o.PaymentTerms, o.OfferCurrency, o.Language As OfferLanguage , o.Notes, o.OfferID, o.Quotes, o.OfferDate, o.Language, r.RFQ_ID, u.LastName, u.FirstName, u.Role, u.Email, cn.Country, t.tax_per
      FROM ${DATASET_ID_MAIN}.Offers as o
      LEFT JOIN ${DATASET_ID_SCRAPE}.RFQ as r ON o.RFQID = r.RFQ_ID
      LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status as rs ON rs.RFQID = r.RFQ_ID
      LEFT JOIN ${DATASET_ID_MAIN}.Users as u ON rs.AssignedUser = u.UserID
      LEFT JOIN ${DATASET_ID_MAIN}.Clients AS c 
      LEFT JOIN ${DATASET_ID_MAIN}.Countries AS cn ON cn.CountryId = c.Country
      LEFT JOIN ${DATASET_ID_MAIN}.States as st on st.StateId = c.State
      LEFT JOIN ${DATASET_ID_MAIN}.Tax_Percentage as t on t.country = cn.Country
      ON UPPER(TRIM(r.Company_Name)) = UPPER(TRIM(c.Name))
      OR UPPER(TRIM(r.ClientID)) = UPPER(TRIM(c.ClientID))
      OR UPPER(TRIM(r.Created_By)) = UPPER(TRIM(c.ClientID))
      OR UPPER(TRIM(r.Created_By)) = UPPER(TRIM(c.Name))
      WHERE o.OfferID = @offerId
    `,
    params: {
      offerId: parseInt(id),
    },
  });

  if (isEmpty(existingOfferData)) {
    return apiError(NOT_FOUND, "Offer", null, res);
  }
  if (isEmpty(existingOfferData[0].tax_per)) {
    return apiError(NOT_FOUND, "Client's country tax", null, res);
  }
  let tax_rate = existingOfferData[0].tax_per;
  let client_country = existingOfferData[0].Country;

  let handling_cost = client_country === "Australia" ? AUSTRALIA_HANDLING_COST :
    HANDLING_COST

  let subTotal = 0;
  let pdfSubTotal = 0;
  const offerTax = tax_rate / 100;
  const Quotes = JSON.parse(existingOfferData[0].Quotes);
  Quotes.forEach((quote) => {
    subTotal += quote.totalCostAfterMargin;

    quote.tempUnitOfferPrice = round(
      quote.totalCostAfterMargin / quote.quantity,
      2
    );

    quote.tempUnitOfferPriceWithHandlingCost = round(
      (quote.totalCostAfterMargin + handling_cost) / quote.quantity,
      2
    );

    quote.pdfUnitOfferPriceWithHandlingCost = applyFloor(
      quote.unitOfferPriceWithHandlingCost
    );

    quote.pdfExchangedOfferCostWithHandlingCost = applyFloor(
      quote.pdfUnitOfferPriceWithHandlingCost * quote.quantity
    );

    pdfSubTotal += quote.pdfExchangedOfferCostWithHandlingCost;
  });

  const { taxCost, grandTotalAfterTax } = calculateOfferAmount(
    subTotal,
    offerTax
  );

  const { taxCost: pdfTaxCost, grandTotalAfterTax: pdfGrandTotalAfterTax } =
    calculateOfferAmount(pdfSubTotal, offerTax);

  let existingRFQ;
  try {
    [existingRFQ] = await bigQueryClient.query({
      query: `
          WITH quote_data AS (
      SELECT
        q.MaterialID,
        ARRAY_AGG(
          STRUCT(
            q.QuoteID as quoteId,
            q.RFQID AS rfqId,
            q.MaterialID as materialId,
            q.SupplierID as supplierId,
            q.QuoteDate,
            q.UnitPrice as unitPrice,
            q.UnitCurrency as unitCurrency,
            q.Quantity as quantity,
            q.ShippingCost as shippingCost,
            q.Tax,
            q.TotalCost as totalCost,
            q.OfferedPrice,
          CASE 
              WHEN q.Quantity = 0 THEN NULL 
              ELSE ROUND(q.OfferedPrice / q.Quantity, 2) 
          END AS unitOfferPrice, 
            q.DeliveryDate,
            'REGISTERED' AS status,
            q.KAM,
            q.Offered,
            q.Notes as notes,
            q.LeadTime,
            q.Weight
          )
        ) AS supplier_json_array
      FROM
        ${DATASET_ID_MAIN}.Quotes AS q
      GROUP BY
        q.MaterialID
    ),
    material_data AS (
      SELECT
        r.RFQ_ID,
        r.RFQ_Number,
        r.RFQ_Name,
        r.RFQ_Date,
        r.Delivery_Date,
        r.Deadline,
        r.Portal,
        r.URL,
        r.Company_Name,
        u.FirstName,
        u.LastName,
        rs.CurrentStatus,
        m.Material_ID AS MaterialID,
        m.RFQ_ID as RFQID,
        pc.ConfirmedPartNumber AS Part_Number,
        m.Quantity_Required AS Quantity_Required,
        m.Material_Description AS Material_Description,
        m.showSuppliers,
        pc.ConfirmedBrand AS brand,
        (SELECT supplier_json_array FROM quote_data WHERE quote_data.MaterialID = m.Material_ID) AS supplier_json_array
      FROM
        ${DATASET_ID_SCRAPE}.RFQ AS r
      LEFT JOIN
        ${DATASET_ID_SCRAPE}.Material AS m
        ON m.RFQ_ID = r.RFQ_ID
      LEFT JOIN
        ${DATASET_ID_MAIN}.Predictions_Confirmations AS pc
        ON pc.MaterialID = m.Material_ID
      LEFT JOIN
        ${DATASET_ID_MAIN}.RFQ_Status AS rs
        ON r.RFQ_ID = rs.RFQID
      LEFT JOIN
        ${DATASET_ID_MAIN}.Users AS u
        ON u.UserID = rs.AssignedUser
      WHERE
        r.RFQ_ID = @rfqId
    ),
    final_result AS (
      SELECT
        RFQ_ID,
        RFQ_Number,
        RFQ_Name,
        RFQ_Date,
        Delivery_Date,
        Deadline,
        Portal,
        URL,
        Company_Name,
        FirstName,
        LastName,
        CurrentStatus,
        ARRAY_AGG(
          STRUCT(
            MaterialID as Material_ID,
            RFQID as RFQ_ID,
            Part_Number,
            Quantity_Required,
            Material_Description,
            brand,
            showSuppliers,
            supplier_json_array as suppliers
          )
        ) AS material_json_array
      FROM
        material_data
      GROUP BY
        RFQ_ID,
        RFQ_Number,
        RFQ_Name,
        RFQ_Date,
        Delivery_Date,
        Deadline,
        Portal,
        URL,
        Company_Name,
        FirstName,
        LastName,
        CurrentStatus
    )
    SELECT
      RFQ_ID,
      RFQ_Number,
      RFQ_Name,
      RFQ_Date,
      Delivery_Date,
      Deadline,
      Portal,
      URL,
      Company_Name,
      FirstName,
      LastName,
      CurrentStatus,
      material_json_array AS materials,
    
    FROM
      final_result;
        `,
      params: {
        rfqId: parseInt(existingOfferData[0].RFQ_ID),
      },
    });
  } catch (error) {
    return apiError(
      CUSTOM_ERROR,
      `Quote could not be fetched from database, please check for data integrity issues : ${error}`,
      null,
      res
    );
  }

  if (isEmpty(existingRFQ[0])) {
    return apiError(NOT_FOUND, "RFQ", null, res);
  }
  const RFQ = existingRFQ[0];

  let materials = [];
  let materialIds = [];

  for (const material of existingRFQ[0].materials) {
    let suppliers = [];
    let supplierIds = [];
    for (const supplier of material.suppliers) {
      if (!supplierIds.includes(supplier.QuoteID)) {
        suppliers.push(supplier);
        supplierIds.push(supplier.QuoteID);
      }
    }
    if (!materialIds.includes(material.Material_ID)) {
      materials.push(material);
      materialIds.push(material.Material_ID);
    }
    materials.suppliers = suppliers;
  }
  RFQ.materials = materials;

  for (const material of RFQ.materials) {
    for (const quote of Quotes) {
      if (material.Material_ID.toString() === quote.materialId.toString()) {
        material.suppliers = material.suppliers.filter(
          (supplier) => supplier.quoteId.toString() !== quote.quoteId.toString()
        );
        material.suppliers.push(quote);
      }
    }
  }

  const existingOffer = {
    ...existingOfferData[0],
    SubTotal: round(subTotal, 2),
    Handling: handling_cost,
    Tax: taxCost,
    GrandTotal: grandTotalAfterTax,
    pdfTax: applyFloor(pdfTaxCost),
    pdfGrandTotal: applyFloor(pdfGrandTotalAfterTax),
    pdfSubTotal: pdfSubTotal,
    materials: RFQ.materials,
    Quotes,
  };

  return apiResponse(FETCH, "Offer", existingOffer, res);
});

const generateOffer = apiHandler(async (req, res) => {
  const {
    clientId,
    rfqId,
    offerCurrency,
    validFor,
    paymentTerms,
    notes,
    materials,
    language
  } = req.body;
  const { firstName, lastName } = req.user;

  const [existingOffer] = await mainDataset.query({
    query: `
      SELECT OfferID
      FROM Offers
      WHERE RFQID = @rfqId
    `,
    params: {
      rfqId: parseInt(rfqId),
    },
  });

  // Fetch country tax_per
  const [existingClient] = await mainDataset.query({
    query: `
      SELECT c.Name, cn.Country, t.tax_per
      FROM Clients as c
      LEFT JOIN Countries as cn ON cn.CountryId = c.Country 
      LEFT JOIN Tax_Percentage as t on t.country = cn.Country 
      WHERE ClientID = @ClientID
    `,
    params: {
      ClientID: clientId,
    },
  });

  if (isEmpty(existingClient[0].tax_per)) {
    return apiError(NOT_FOUND, "Client's country tax", null, res);
  }
  let tax_rate = existingClient[0].tax_per;

  if (!isEmpty(existingOffer)) {
    const Quotes = [];
    let subTotal = 0;
    const offerTax = tax_rate / 100;
    let quoteID = generateID();
    for (const material of materials) {
      const {
        materialId,
        partNumber,
        brand,
        materialDescription,
        Quantity_Required,
      } = material;
      const {
        quoteId,
        supplierId,
        unitPrice,
        unitOfferPrice,
        shippingCost,
        tax,
        quantity,
        totalCost,
        unitCurrency,
        margin,
        totalOfferPrice,
        weight,
        leadTime,
        notes: Notes,
        isTax,
        exchangedOfferCostWithHandlingCost,
        exchangedOfferCost,
        exchangeOfferHandlingCost,
        unitOfferPriceHandlingCost,
        unitOfferPriceWithHandlingCost,
      } = material.supplier;

      subTotal += totalOfferPrice;
      Quotes.push({
        quoteId,
        rfqId,
        materialId,
        partNumber,
        brand,
        materialDescription,
        Quantity_Required,
        kam: `${firstName} ${lastName}`,
        supplierId,
        unitPrice,
        unitOfferPrice,
        shippingCost,
        taxCost: tax,
        quantity,
        totalCost,
        unitCurrency,
        margin,
        weight,
        leadTime,
        notes: Notes || "",
        isTax,
        totalCostAfterMargin: totalOfferPrice,
        offerCurrency,
        exchangedOfferCostWithHandlingCost,
        exchangedOfferCost,
        exchangeOfferHandlingCost,
        unitOfferPriceHandlingCost,
        unitOfferPriceWithHandlingCost,
      });
    }

    const { grandTotalAfterTax } = calculateOfferAmount(subTotal, offerTax);
    const [updatedOffer] = await mainDataset.query({
      query: `
        UPDATE Offers SET
        OfferDate = @date, QuoteID = @quoteId, RFQID = @rfqId, MaterialID = @materialId,  
        OfferCurrency = @offerCurrency, ValidFor = @validFor, Margin = @margin, Notes = @notes, Status = @status,
        PaymentTerms = @paymentTerms, TotalOfferPrice = @totalOfferPrice, Quotes = @quotes, Language = @language
        WHERE OfferID=@offerId
      `,
      params: {
        date: setDate(),
        quoteId: quoteID,
        notes: notes || "",
        offerId: existingOffer[0].OfferID,
        offerCurrency,
        validFor,
        paymentTerms,
        totalOfferPrice: grandTotalAfterTax,
        margin: 0,
        status: "",
        rfqId: parseInt(rfqId),
        materialId: parseInt(materials[0].materialId),
        quotes: JSON.stringify(Quotes),
        language
      },
    });

    const [updatedRFQStatus] = await mainDataset.query({
      query: `
        UPDATE RFQ_Status
        SET CurrentStatus = @status, StatusChangeDate = @date
        WHERE RFQID = @rfqId
      `,
      params: {
        rfqId: parseInt(rfqId),
        date: setDate(),
        status: "ANSWERED",
      },
    });

    const [updatedRFQ] = await scrapedDataset.query({
      query: `
        UPDATE RFQ
        SET Created_By = @clientId
        WHERE RFQ_ID = @rfqId
      `,
      params: {
        rfqId: parseInt(rfqId),
        clientId,
      },
    });
  } else {
    const quoteId = generateID();
    const offerId = generateID();
    const Quotes = [];
    let subTotal = 0;
    const offerTax = tax_rate / 100;
    for (const material of materials) {
      const {
        materialId,
        partNumber,
        brand,
        materialDescription,
        Quantity_Required,
      } = material;
      const {
        quoteId,
        supplierId,
        unitPrice,
        unitOfferPrice,
        shippingCost,
        tax,
        quantity,
        totalCost,
        unitCurrency,
        margin,
        weight,
        isTax,
        leadTime,
        notes: Notes,
        totalOfferPrice,
        exchangedOfferCostWithHandlingCost,
        exchangedOfferCost,
        exchangeOfferHandlingCost,
        unitOfferPriceHandlingCost,
        unitOfferPriceWithHandlingCost,
      } = material.supplier;

      subTotal += totalOfferPrice;
      Quotes.push({
        quoteId,
        rfqId,
        materialId,
        partNumber,
        brand,
        materialDescription,
        Quantity_Required,
        kam: `${firstName} ${lastName}`,
        supplierId,
        unitPrice,
        unitOfferPrice,
        shippingCost,
        taxCost: tax,
        quantity,
        totalCost,
        unitCurrency,
        margin,
        weight,
        leadTime,
        notes: Notes || "",
        isTax,
        totalCostAfterMargin: totalOfferPrice,
        offerCurrency,
        exchangedOfferCostWithHandlingCost,
        exchangedOfferCost,
        exchangeOfferHandlingCost,
        unitOfferPriceHandlingCost,
        unitOfferPriceWithHandlingCost,
      });
    }

    const { grandTotalAfterTax } = calculateOfferAmount(subTotal, offerTax);
    const [generatedOffer] = await mainDataset.query({
      query: `
        INSERT INTO Offers (OfferID, OfferDate, QuoteID, RFQID, MaterialID,  
                            OfferCurrency, ValidFor, Margin, Notes, Status,
                            PaymentTerms, TotalOfferPrice, Quotes, Language)
        VALUES (@offerId, @date, @quoteId, @rfqId, @materialId,
                @offerCurrency, @validFor, @margin, @notes, @status,
                @paymentTerms, @totalOfferPrice, @quotes, @language)
      `,
      params: {
        date: setDate(),
        quoteId,
        notes: notes || "",
        offerId,
        offerCurrency,
        validFor,
        paymentTerms,
        totalOfferPrice: grandTotalAfterTax,
        margin: 0,
        status: "",
        rfqId: parseInt(rfqId),
        materialId: parseInt(materials[0].materialId),
        quotes: JSON.stringify(Quotes),
        language
      },
    });

    const [updatedRFQStatus] = await mainDataset.query({
      query: `
        UPDATE RFQ_Status
        SET CurrentStatus = @status, StatusChangeDate = @date
        WHERE RFQID = @rfqId
      `,
      params: {
        rfqId: parseInt(rfqId),
        date: setDate(),
        status: "ANSWERED",
      },
    });

    const [updatedRFQ] = await scrapedDataset.query({
      query: `
        UPDATE RFQ
        SET Created_By = @clientId
        WHERE RFQ_ID = @rfqId
      `,
      params: {
        rfqId: parseInt(rfqId),
        clientId,
      },
    });
  }

  return apiResponse(SUCCESS, "Offer", null, res);
});

const updateOfferStatus = apiHandler(async (req, res) => {
  const { rfqId, offerId, status } = req.body;
  await mainDataset.query({
    query: `
      UPDATE Offers
      SET Status = @status ${status === "Won" ? `, WinningDate = @date` : ``}
      WHERE OfferID = @offerId
    `,
    params: {
      offerId: parseInt(offerId),
      status: status.toUpperCase(),
      date: setDate(),
    },
  });
  await mainDataset.query({
    query: `
      UPDATE RFQ_Status
      SET CurrentStatus = @status, StatusChangeDate = @date
      WHERE RFQID = @rfqId
    `,
    params: {
      rfqId: parseInt(rfqId),
      status: status.toUpperCase(),
      date: setDate(),
    },
  });

  return apiResponse(STATUS_SUCCESS, "Offer", null, res);
});

const calculateOffer = apiHandler(async (req, res) => {
  let { offerCurrency, quotes, clientId } = req.body;
  let subTotal = 0;

  // Fetch country tax_per
  const [existingClient] = await mainDataset.query({
    query: `
        SELECT c.Name, cn.Country, t.tax_per
        FROM Clients as c
        LEFT JOIN Countries as cn ON cn.CountryId = c.Country 
        LEFT JOIN Tax_Percentage as t on t.country = cn.Country 
        WHERE ClientID = @ClientID
      `,
    params: {
      ClientID: clientId,
    },
  });

  if (isEmpty(existingClient[0].tax_per)) {
    return apiError(NOT_FOUND, "Client's country tax", null, res);
  }
  let tax_rate = existingClient[0].tax_per;
  let client_country = existingClient[0].Country;

  let handling_cost = client_country === "Australia" ? AUSTRALIA_HANDLING_COST :
    HANDLING_COST;

  const tax = tax_rate / 100;
  const exchangedQuotes = [];

  let totalCostOfAllQuotes = 0;
  for (let quote of quotes) {
    totalCostOfAllQuotes += quote.totalCost;
  }

  const uniqueCurrencies = [...new Set(quotes.map((quote) => quote.currency))];

  const [exchangeRates] = await mainDataset.query({
    query: `
      SELECT er.From as fromCurrency, er.Value as exchangeRate
      FROM Exchange_Rates as er
      WHERE er.From IN UNNEST(@uniqueCurrencies) AND er.To = @offerCurrency
    `,
    params: {
      uniqueCurrencies,
      offerCurrency,
    },
  });

  const [usdExchangeRate] = await mainDataset.query({
    query: `
      SELECT er.Value as exchangeRate
      FROM Exchange_Rates as er
      WHERE er.From = "USD" AND er.To = @offerCurrency
    `,
    params: { offerCurrency },
  });

  const EXCHANGE_HANDLING_COST = handling_cost * (usdExchangeRate[0]?.exchangeRate ?? 1);

  const exchangeRateMap = {};
  exchangeRates.forEach((rate) => {
    exchangeRateMap[rate.fromCurrency] = rate.exchangeRate;
  });

  for (const quote of quotes) {
    const { totalCost, currency: quoteCurrency, margin, quantity } = quote;
    let exchangeRate = 1;

    if (quoteCurrency !== offerCurrency) {
      exchangeRate = exchangeRateMap[quoteCurrency] || 1;

      if (!exchangeRateMap[quoteCurrency]) {
        return apiError(INVALID, "Currency", null, res);
      }
    }
    const offerCost = !isEmpty(margin)
      ? totalCost / (1 - margin / 100)
      : totalCost;

    const exchangedOfferCost = offerCost * exchangeRate;
    let CostofCurrentQuote = totalCost;
    const weighted_Average =
      (CostofCurrentQuote / totalCostOfAllQuotes) * EXCHANGE_HANDLING_COST;
    const exchangedOfferCostWithHandling =
      exchangedOfferCost + weighted_Average;
    const estimatedOfferCost = round(
      exchangeRate ? (totalCost / 0.7) * exchangeRate : totalCost / 0.7,
      2
    );
    const unitOfferPriceWithHandling =
      exchangedOfferCostWithHandling / quantity;
    const unitOfferPrice = exchangedOfferCost / quantity;
    exchangedQuotes.push({
      totalCost: round(totalCost, 2),
      exchangedOfferCost: round(exchangedOfferCost, 2),
      exchangeOfferHandlingCost: round(
        exchangedOfferCostWithHandling - exchangedOfferCost,
        2
      ),
      exchangedOfferCostWithHandlingCost: round(
        exchangedOfferCostWithHandling,
        2
      ),
      unitOfferPrice: round(unitOfferPrice, 2),
      unitOfferPriceHandlingCost: round(
        unitOfferPriceWithHandling - unitOfferPrice,
        2
      ),
      unitOfferPriceWithHandlingCost: round(unitOfferPriceWithHandling, 2),
    });
    subTotal += exchangedOfferCostWithHandling;
  }
  const { grandTotalBeforeTax, taxCost, grandTotalAfterTax } =
    calculateOfferAmount(subTotal, tax);
  const calculationDetails = {
    quotes: exchangedQuotes,
    subTotal: grandTotalBeforeTax,
    handlingCost: handling_cost,
    taxCost,
    tax: tax_rate,
    grandTotal: grandTotalAfterTax,
  };
  return apiResponse(
    CUSTOM_SUCCESS,
    "Offer calculated successfully",
    calculationDetails,
    res
  );
});

const getMultipleSpecsSheet = apiHandler(async (req, res) => {
  const { offerId } = req.params;

  const [specsSheets] = await bigQueryClient.query({
    query: `
      SELECT pd.group_items_name, pd.group_items_display_name
      FROM ${DATASET_ID_MAIN}.Offers as offer
      INNER JOIN ${DATASET_ID_SCRAPE}.Material as material 
      ON material.Material_ID = offer.MaterialID
      INNER JOIN ${DATASET_ID_LANDINGZONE}.products_master  as pm 
      ON pm.manufacturer_catalog_number = material.Part_Number
      INNER JOIN ${DATASET_ID_LANDINGZONE}.products_downloads as pd
      ON pd.product_id = pm.product_id
      WHERE offer.OfferID = CAST(@offerId AS int)`,
    params: {
      offerId,
    },
  });

  if (isEmpty(specsSheets)) {
    return apiError(NOT_FOUND, "Specs Sheets", null, res);
  }

  return apiResponse(FETCH, "Specs Sheets", specsSheets, res);
});

const getOfferCalculations = apiHandler(async (req, res) => {
  const { userId, role } = req.user;

  let whereExpression = ``;
  if (role === "KAM") {
    whereExpression = `WHERE rs.AssignedUser = @userId`;
  }

  const [existingOffers] = await mainDataset.query({
    query: `
WITH OffersWithRowNumber AS (
    SELECT rs.StatusChangeDate, r.RFQ_ID, r.RFQ_Name, r.RFQ_Number, r.RFQ_Date, r.Portal, r.Delivery_Date, r.URL, r.Company_Name, 
           o.TotalOfferPrice, o.Status, o.OfferCurrency, er.Value AS exchangeRate, o.OfferID, o.OfferDate, o.Language,
           CONCAT(u.FirstName, ' ', u.LastName) AS role,
           ROW_NUMBER() OVER (PARTITION BY o.OfferID ORDER BY o.OfferDate DESC, r.RFQ_ID DESC) AS rn
    FROM ${DATASET_ID_MAIN}.Offers AS o
    LEFT JOIN (
        SELECT RFQID, AssignedUser, StatusChangeDate, 
               ROW_NUMBER() OVER (PARTITION BY RFQID ORDER BY StatusChangeDate DESC) AS rn
        FROM ${DATASET_ID_MAIN}.RFQ_Status
    ) AS rs ON o.RFQID = rs.RFQID AND rs.rn = 1
    LEFT JOIN ${DATASET_ID_SCRAPE}.RFQ AS r ON rs.RFQID = r.RFQ_ID
    LEFT JOIN ${DATASET_ID_MAIN}.Users AS u ON rs.AssignedUser = u.UserID
    LEFT JOIN (
        SELECT DISTINCT er.From, er.To, er.Value
        FROM ${DATASET_ID_MAIN}.Exchange_Rates AS er
        WHERE UPPER(TRIM(er.To)) = 'CLP'
    ) AS er ON UPPER(TRIM(er.From)) = UPPER(TRIM(o.OfferCurrency))
    ${whereExpression}
)
SELECT *
FROM OffersWithRowNumber
WHERE rn = 1
ORDER BY OfferDate DESC, RFQ_ID DESC

    `,
    params: {
      userId: parseInt(userId),
    },
  });
  if (isEmpty(existingOffers)) {
    return apiError(NOT_FOUND, "Offers", null, res);
  }
  let monthRFQWon = 0;
  let wtdOffersWon = 0;
  let wtdOffers = 0;

  const currentDate = new Date();
  const goBack = (currentDate.getDay() + 6) % 7;
  const lastMonday = new Date().setDate(currentDate.getDate() - goBack);
  const lastMondayDate = new Date(lastMonday);
  lastMondayDate.setHours(0, 0, 0, 0);

  // Get the start of the month at 12:00 AM
  const monthStartDate = new Date();
  monthStartDate.setDate(1);
  monthStartDate.setHours(0, 0, 0, 0); // Set to 12:00 AM

  // Set current date to 12:00 PM today
  // const currentDate = new Date();
  currentDate.setHours(12, 0, 0, 0); // Set to 12:00 PM

  // Loop through existing offers
  existingOffers.forEach((offer) => {
    if (!isEmpty(offer?.exchangeRate)) {
      offer.TotalOfferPrice = offer?.TotalOfferPrice * offer?.exchangeRate;
    }

    const offerDateTime = new Date(offer?.StatusChangeDate?.value).getTime();
    const offerStatus = offer?.Status;

    // Condition for monthRFQWon within the start of the month to current date
    if (
      offerDateTime >= monthStartDate.getTime() &&
      offerDateTime <= currentDate.getTime() &&
      offerStatus === "WON"
    ) {
      monthRFQWon += offer?.TotalOfferPrice;
    }

    // Week-to-date conditions
    if (
      offerDateTime >= lastMondayDate.getTime() &&
      offerDateTime <= currentDate.getTime()
    ) {
      if (offerStatus === "WON") {
        wtdOffersWon += offer?.TotalOfferPrice;
      }
      wtdOffers += 1;
    }
  });

  const data = {
    monthRFQWon: round(monthRFQWon, 2),
    wtdOffersWon: round(wtdOffersWon, 2),
    wtdOffers,
  };

  return apiResponse(FETCH, "Offer Calculations", applyFloor(data), res);
});

const winOffer = apiHandler(async (req, res) => {
  const { OC_Number, Amount_CLP, Amount_USD, Client, rfqId, offerId, status } =
    req.body;
  const email = req?.user?.email;
  const date = setDate();
  const RFQ_Link = `https://platform.remiex.com/view-offer/${rfqId}/${offerId}`;

  const OC_File_Path = req?.file?.path;

  if (!OC_File_Path) {
    return apiError(REQUIRED, "OC_File", null, res);
  }

  await updateRFQOfferStatus(offerId, rfqId, status, date);
  const publicUrl = await uploadFileToGCP(OC_File_Path, req.file.filename);
  deleteFile(OC_File_Path);
  const createdItem = await insertIntoMonday(
    OC_Number,
    Client,
    Amount_CLP,
    Amount_USD,
    publicUrl,
    RFQ_Link,
    date
  );

  apiResponse(CUSTOM_SUCCESS, "Offer Marked as Won", createdItem, res);

  if (!isEmpty(createdItem)) {
    let Processed = true;
    await insertWonRFQ(
      OC_Number,
      publicUrl,
      Amount_CLP,
      Amount_USD,
      Client || "",
      rfqId,
      offerId,
      Processed,
      RFQ_Link,
      date
    );
    const uploadSuccessOption = {
      to: email,
      subject: mondayRecordUploadSuccessTemplate.emailSubject(),
      html: mondayRecordUploadSuccessTemplate.htmlTemplate(rfqId),
    };
    await sendMail(uploadSuccessOption);
  } else {
    let Processed = false;
    await insertWonRFQ(
      OC_Number,
      publicUrl,
      Amount_CLP,
      Amount_USD,
      Client || "",
      rfqId,
      offerId,
      Processed,
      RFQ_Link,
      date
    );
    const uploadFailureOption = {
      to: email,
      subject: mondayRecordUploadFailureTemplate.emailSubject(),
      html: mondayRecordUploadFailureTemplate.htmlTemplate(rfqId),
    };
    await sendMail(uploadFailureOption);
  }
});

const mockMondayFetchData = apiHandler(async (req, res) => {
  const createdItem = await insertIntoMonday(
    "something1",
    "something2",
    "something3",
    "something4"
  );
  return apiResponse(
    CUSTOM_SUCCESS,
    "Item Created Successfully",
    createdItem,
    res
  );
});

const copyRFQ = apiHandler(async (req, res) => {
  const { rfqId, offerId, expirationDate } = req.body;
  const { userId } = req.user;
  const newRfqId = generateID(6);

  // Update offer status to VOID
  // console.log("updating offer status")
  await mainDataset.query({
    query: `
      UPDATE Offers
      SET Status = @status
      WHERE OfferID = @offerId
    `,
    params: {
      offerId: parseInt(offerId),
      status: "VOID",
    },
  });

  // console.log("fetching rfq data")
  // Fetch all required RFQ details
  let [existingRFQ] = await bigQueryClient.query({
    query: `
      SELECT *
      FROM ${DATASET_ID_SCRAPE}.RFQ as r
      LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status as rs ON r.RFQ_ID = rs.RFQID
      LEFT JOIN ${DATASET_ID_SCRAPE}.Material as m ON m.RFQ_ID = r.RFQ_ID
      LEFT JOIN ${DATASET_ID_MAIN}.Quotes as q ON (q.RFQID = r.RFQ_ID AND q.MaterialID = m.Material_ID)
      LEFT JOIN ${DATASET_ID_MAIN}.Predictions_Confirmations as pc ON (m.Material_ID = pc.MaterialID AND m.RFQ_ID = pc.RFQID)
      WHERE r.RFQ_ID = @rfqId
    `,
    params: { rfqId: parseInt(rfqId) },
  });
  if (isEmpty(existingRFQ)) {
    return apiError(NOT_FOUND, "RFQ", null, res);
  }

  // send immediate response on status update
  const timeinSeconds = existingRFQ.length * 5 + 10;
  const message = `RFQ will be copied in roughly ${Math.ceil(
    timeinSeconds / 60
  )} min`;
  const response = {
    rfqId: newRfqId,
    quoteURL: `${BASE_URL}/offer/${newRfqId}`,
  };
  apiResponse(CUSTOM_SUCCESS, message, response, res);

  // Generate new RFQ from old RFQ details
  const Created_By = userId;
  const rfqDetails = existingRFQ[0];
  // console.log("generating new rfq")
  const includeClientID = !!rfqDetails.ClientID;

  const columns = [
    "RFQ_ID",
    "RFQ_Number",
    "RFQ_Name",
    "RFQ_Date",
    "Company_Name",
    "Portal",
    "Status",
    "Priority",
    "Deadline",
    "Created_By",
    "Address_Information",
    "Additional_Notes",
    "Delivery_Date",
    "URL",
    "Release_Reason",
    "isManuallyAdded",
    "Created_From_RFQ_ID",
  ];

  const values = [
    "@RFQ_ID",
    "@RFQ_Number",
    "@RFQ_Name",
    "@RFQ_Date",
    "@Company_Name",
    "@Portal",
    "@Status",
    "@Priority",
    "@Deadline",
    "@Created_By",
    "@Address_Information",
    "@Additional_Notes",
    "@Delivery_Date",
    "@URL",
    "@Release_Reason",
    "@isManuallyAdded",
    "@Created_From_RFQ_ID",
  ];

  if (includeClientID) {
    columns.push("ClientID");
    values.push("@ClientID");
  }

  await scrapedDataset.query({
    query: `
    INSERT INTO RFQ (${columns.join(", ")})
    VALUES (${values.join(", ")})
  `,
    params: {
      RFQ_ID: newRfqId,
      RFQ_Number: rfqDetails.RFQ_Number,
      RFQ_Name: rfqDetails.RFQ_Name,
      RFQ_Date: rfqDetails.RFQ_Date,
      Company_Name: rfqDetails.Company_Name,
      Portal: rfqDetails.Portal,
      Status: rfqDetails.Status,
      Priority: rfqDetails.Priority,
      Deadline: expirationDate,
      Created_By: Created_By.toString(),
      Address_Information: rfqDetails.Address_Information,
      Additional_Notes: rfqDetails.Additional_Notes,
      Delivery_Date: rfqDetails.Delivery_Date,
      URL: rfqDetails.URL,
      Release_Reason: rfqDetails.Release_Reason || "",
      isManuallyAdded: true,
      Created_From_RFQ_ID: parseInt(rfqId),
      ...(includeClientID && { ClientID: rfqDetails.ClientID }),
    },
    types: rfqDetails.Delivery_Date ? {} : { Delivery_Date: "date" },
  });

  // Update old RFQ's status to VOID
  // console.log("udpating old rfq status")
  await mainDataset.query({
    query: `
      UPDATE RFQ_Status
      SET CurrentStatus = @CurrentStatus, StatusChangeDate = @StatusChangeDate
      WHERE RFQID = @RFQ_ID
    `,
    params: {
      RFQ_ID: parseInt(rfqId),
      CurrentStatus: "VOID",
      StatusChangeDate: setDate(),
    },
  });

  // Insert new RFQ's status to QUOTING
  // console.log("updating new rfq status")
  await mainDataset.query({
    query: `
      INSERT INTO RFQ_Status (RFQID, CurrentStatus, StatusChangeDate, AssignedUser, timestamp_date) 
      VALUES 
      (@RFQ_ID, @CurrentStatus, @StatusChangeDate, @AssignedUser, CURRENT_TIMESTAMP())
    `,
    params: {
      RFQ_ID: newRfqId,
      CurrentStatus: "QUOTING",
      StatusChangeDate: setDate(),
      AssignedUser: Created_By,
    },
  });

  // Generate necessary data with new RFQ_ID
  const Materials = [];
  const Quotes = [];
  for (const rfq of existingRFQ) {
    // If material is not found then
    if (!Materials.includes(rfq.Material_ID)) {
      Materials.push(rfq.Material_ID);
      // Create new Material with new RFQ_ID
      // console.log("generating new material")
      const materialParams = {
        Material_ID: rfq.Material_ID,
        RFQ_ID: newRfqId,
        Part_Number: rfq.Part_Number,
        Quantity_Required: rfq.Quantity_Required,
        Material_Description: rfq.Material_Description,
        Notes: rfq.Notes,
        Position: rfq.Position || 1,
        showSuppliers: rfq.showSuppliers || "[]",
        orders: Materials.length + 1,
      };
      await scrapedDataset.query({
        query: `
          INSERT INTO Material (Material_ID, RFQ_ID, Part_Number, Quantity_Required, Material_Description, Notes, Position, showSuppliers, orders)

          VALUES (@Material_ID, @RFQ_ID, @Part_Number, @Quantity_Required, @Material_Description, @Notes, @Position, @showSuppliers, @orders)
        `,
        params: materialParams,
      });

      // Create new predictionCconfirmations with new RFQ_ID
      // console.log("genearting new prediction")
      const predictionsConfirmationsParams = {
        MaterialID: rfq.MaterialID,
        RFQID: newRfqId,
        ExtractedBrand: rfq.ExtractedBrand,
        ExtractedPartNumber: rfq.ExtractedPartNumber,
        ConfirmedBrand: rfq.ConfirmedBrand,
        ConfirmedPartNumber: rfq.ConfirmedPartNumber,
        PredictionStatus: rfq.PredictionStatus,
        ConfirmationStatus: rfq.ConfirmationStatus,
        PredictionDate: rfq.PredictionDate,
        ConfirmationDate: rfq.ConfirmationDate,
      };
      await mainDataset.query({
        query: `
          INSERT INTO Predictions_Confirmations (MaterialID, RFQID, ExtractedBrand, ExtractedPartNumber, PredictionStatus, PredictionDate, ConfirmedBrand, ConfirmedPartNumber, ConfirmationDate, ConfirmationStatus) 

          VALUES (@MaterialID, @RFQID, @ExtractedBrand, @ExtractedPartNumber, @PredictionStatus, @PredictionDate, @ConfirmedBrand, @ConfirmedPartNumber, @ConfirmationDate, @ConfirmationStatus)
        `,
        params: predictionsConfirmationsParams,
      });
    }

    // If quote is not found, create a new one
    if (!Quotes.includes(rfq.QuoteID)) {
      Quotes.push(rfq.QuoteID);
      // Create new Quote with new RFQ_ID
      // console.log("generating new quote")
      const quotePatams = {
        QuoteID: rfq.QuoteID,
        MaterialID: rfq.MaterialID,
        RFQID: newRfqId,
        SupplierID: rfq.SupplierID,
        QuoteDate: rfq.QuoteDate,
        UnitPrice: rfq.UnitPrice,
        UnitCurrency: rfq.UnitCurrency,
        Quantity: rfq.Quantity,
        ShippingCost: rfq.ShippingCost,
        Tax: rfq.Tax,
        TotalCost: rfq.TotalCost,
        OfferedPrice: rfq.OfferedPrice,
        DeliveryDate: rfq.DeliveryDate,
        KAM: rfq.KAM,
        Offered: rfq.Offered,
        Notes: rfq.Notes_1,
        LeadTime: rfq.LeadTime,
        Weight: rfq.Weight,
      };
      await mainDataset.query({
        query: `
          INSERT INTO Quotes (QuoteID, RFQID, MaterialID, SupplierID, QuoteDate, UnitPrice, UnitCurrency, Quantity, ShippingCost, Tax, TotalCost, OfferedPrice, DeliveryDate, KAM, Offered, Notes, LeadTime, Weight)

          VALUES (@QuoteID, @RFQID, @MaterialID, @SupplierID, @QuoteDate, @UnitPrice, @UnitCurrency, @Quantity, @ShippingCost, @Tax, @TotalCost, @OfferedPrice, @DeliveryDate, @KAM, @Offered, @Notes, @LeadTime, @Weight)
        `,
        params: quotePatams,
      });
    }
  }

  console.log(`RFQ copied successfully from ${rfqId} to ${newRfqId}`);
});

module.exports = {
  getOfferList,
  getOffer,
  generateOffer,
  calculateOffer,
  updateOfferStatus,
  getMultipleSpecsSheet,
  getOfferCalculations,
  winOffer,
  mockMondayFetchData,
  copyRFQ,
};
