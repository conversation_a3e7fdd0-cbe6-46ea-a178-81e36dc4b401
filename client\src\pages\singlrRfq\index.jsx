import Grid from '@mui/material/Grid';
import Loader from 'components/Loader';
import { get, isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { getSingleRfq } from 'redux/reducers/singleRfq';
import { historyPageUrl, rfqListPageUrl } from 'utils/constant';
import { isCountryManager } from 'utils/helper';
import Materials from './material';
import Overview from './overView';
import './singlrRfq.css';
import ReserveBtn from 'pages/availableRfq/materialFilter.js/component/reserve';
// ==============================|| DASHBOARD - DEFAULT ||============================== //

export default function SingleRfq() {
  const [singleRfq, setSingleRfq] = useState([]);
  const singleRfqData = useSelector((state) => state.singleRfq);
  const quoteOffer = useSelector((state) => state.offer);
  const myoffer = useSelector((state) => state.myOffer);
  const allRfq = useSelector((state) => state.rfq);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const rfqId = useParams()?.id;

  const isLoading = () => {
    const { status } = singleRfqData || {};
    return (
      status === 'loading' ||
      get(singleRfqData, 'loading', false) ||
      get(allRfq, 'loading', false) ||
      myoffer?.status === 'loading' ||
      myoffer?.loading ||
      quoteOffer?.loading
    );
  };

  const buttonsConfig = [
    {
      type: 'component',
      label: 'Reserve',
      hide: isCountryManager(),
      component: ReserveBtn
    },
    {
      type: 'close',
      label: 'Close',
      smallBtn: false,
      disabled: isLoading(),
      link: rfqListPageUrl
    }
  ];

  useEffect(() => {
    if (rfqId && rfqId !== 'undefined') {
      dispatch(getSingleRfq(rfqId));
    } else {
      navigate(rfqListPageUrl);
    }
  }, [rfqId]);

  useEffect(() => {
    if (!isEmpty(get(singleRfqData, 'data', {}))) {
      let updatedData = {
        ...get(singleRfqData, 'data', {}),
        kam:
          (
            (get(singleRfqData, 'data.FirstName') || get(singleRfqData, 'data.firstName') || '') +
            ' ' +
            (get(singleRfqData, 'data.LastName') || get(singleRfqData, 'data.lastName') || '')
          ).trim() || 'N/A'
      };
      setSingleRfq({ ...updatedData });
    }
  }, [singleRfqData?.data]);

  return (
    <div className="rfq-container">
      <Grid container spacing={2}>
        {isLoading() && <Loader />}
        <Grid item xs={12}>
          <Overview rfqDetails={singleRfq} buttonsConfig={buttonsConfig} loading={isLoading()} />
        </Grid>
        <Grid item xs={12}>
          <Materials
            loading={isLoading()}
            materials={get(singleRfq, 'Materials', [])}
            rfqId={rfqId}
            rfqStatus={singleRfq?.CurrentStatus || ''}
            showConfirmBtn={false}
            historyPageUrl={historyPageUrl}
          />
        </Grid>
      </Grid>
    </div>
  );
}
