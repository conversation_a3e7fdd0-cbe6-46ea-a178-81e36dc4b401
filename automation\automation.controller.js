const { bigQueryClient, automationDataset, mainDataset } = require("../db");
const { apiHandler, apiResponse, apiError } = require("../utils/api.util");
const {
  FETCH,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
  SUCCESS,
} = require("../utils/message.util");
const {
  isEmpty,
  pickAutomatedRFQ,
  fetchRFQInformation,
  generateRequestSupplierPayload,
  filterReadyToQuoteMaterials,
  RequestMaterialToSupplier,
  generateID,
  processAutomatedRFQs,
  reserveAutomatedRFQ,
} = require("../utils/misc.util");

const getAutomationInfo = apiHandler(async (req, res) => {
  const { materialID, partNumber } = req.body;

  const response = {};

  const [existingBenchmarks] = await automationDataset.query({
    query: `
      SELECT * 
      FROM benchmark_prices
      WHERE MaterialID = @materialID AND PartNumber = @partNumber
    `,
    params: {
      materialID: parseInt(materialID),
      partNumber,
    },
  });

  const [existingFiles] = await automationDataset.query({
    query: `
      SELECT * 
      FROM files
      WHERE MaterialID = @materialID AND PartNumber = @partNumber
    `,
    params: {
      materialID: parseInt(materialID),
      partNumber,
    },
  });

  response["benchmarks"] = existingBenchmarks || [];
  response["files"] = existingFiles || [];

  return apiResponse(FETCH, "Benchmarks and Files", response, res);
});

const automatedRFQRequest = apiHandler(async (req, res) => {
  const RFQIDs = await pickAutomatedRFQ();
  await reserveAutomatedRFQ();
  const estimatedTimeinSeconds = RFQIDs?.length * 0.2 * 60 * 1000;
  apiResponse(SUCCESS, "Estimated Time", estimatedTimeinSeconds, res);
  await processAutomatedRFQs(RFQIDs);
});

module.exports = {
  getAutomationInfo,
  automatedRFQRequest,
};
