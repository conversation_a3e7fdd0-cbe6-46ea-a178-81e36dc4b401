const app = require("./app");
const { PORT, NODE_ENV } = require("./constants");
const { connectDB } = require("./db");
///////////////////////////////////////////
// tool for handling module dependencies
// const madge = require('madge');

// madge('./server.js').then((res) => {
// 	console.log(res.circular());
// });
///////////////////////////////////////////

connectDB()
  .then(() => {
    app.listen(PORT, () => {
      console.log(`Node environment: ${NODE_ENV}`);
      console.log(`Server running on port: ${PORT}`);
    });
  })
  .catch((err) => console.log(`DB Error: ${err.message}`));
