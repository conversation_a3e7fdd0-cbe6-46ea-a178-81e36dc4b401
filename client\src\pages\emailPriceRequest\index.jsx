import { Box, Card, CardContent, Divider, Grid, IconButton, Typography } from '@mui/material';
import ActionButton from 'pages/component/actionButton';
import AlertDialog from 'pages/component/dialogbox';
import TableComponent from 'pages/component/table/table';
import { useNavigate } from 'react-router';
import { emailResponsesPageUrl, ReservedSingleRfqPageUrl, SUPERVISER } from 'utils/constant';
import Step2 from './step2';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { useEffect, useState } from 'react';
import { getAllEmailPriceRequestAction, updateEmailStatusAction } from 'redux/reducers/centralizedReducer';
import { useDispatch, useSelector } from 'react-redux';
import { isEmpty, update } from 'lodash';
import Loader from 'components/Loader';
import { getAllKamAction } from 'redux/reducers/RfqReducer';
import { getAllUserAction } from 'redux/reducers/userReducer';
import { getUserDetail } from 'utils/auth';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import AddLinkIcon from '@mui/icons-material/AddLink';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import SyncIcon from '@mui/icons-material/Sync';
import { filterFunction } from 'pages/component/table/tableSearch';

const EmailPriceRequest = () => {
  const emailRequestsData = useSelector((state) => state?.requestedPrice);
  const navigate = useNavigate();
  const usersData = useSelector((state) => state?.users);
  const [singleRequest, setSingleRequest] = useState({});
  const [emailRequests, setEmailRequests] = useState([]);
  const [allEmailRequests, setAllEmailRequests] = useState([]);
  const [searchValues, setSearchValues] = useState({});
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(getAllEmailPriceRequestAction());
    dispatch(getAllUserAction());
  }, []);

  useEffect(() => {
    let updatedData = emailRequestsData?.emailPriceRequests?.map((item) => {
      let status = item?.Email_Status;
      return {
        ...item,
        Email_Sent_Timestamp: item?.Email_Sent_Timestamp?.value,
        Email_Response_Timestamp: item?.Email_Response_Timestamp?.value,
        isResponseRecieved: item?.Email_Status === 'REPLIED',
        Email_Status: status,
        isNotProcessedMail: status === 'REPLIED' || (status !== 'PROCESSED' && status !== 'SENT')
      };
    });
    updatedData = !isEmpty(searchValues) ? filterFunction(searchValues, updatedData, customFilters) : updatedData;

    setEmailRequests(updatedData || []);
    setAllEmailRequests(updatedData || []);
  }, [emailRequestsData?.emailPriceRequests]);

  const isSuperUser = () => {
    let { role } = getUserDetail() || {};
    return role === SUPERVISER;
  };

  const headers = [
    {
      name: 'RFQID',
      type: 'text',
      title: 'RFQ ID',
      sortingactive: true,
      minWidth: '100px'
    },
    {
      name: 'Supplier_Name',
      type: 'text',
      title: 'Supplier Name',
      sortingactive: true,
      maxWidth: '100px'
    },
    {
      name: 'KAM_Name',
      type: 'text',
      title: 'KAM',
      sortingactive: true,
      hideColumn: !isSuperUser(),
      maxWidth: '100px'
    },
    {
      name: 'Email_Sent_Timestamp',
      type: 'date',
      title: 'Email Sent Timestamp',
      sortingactive: true,
      includeTime: true,
      maxWidth: '100px'
    },
    {
      name: 'Email_Status',
      type: 'badge',
      title: 'Email Status',
      sortingactive: true,
      className: 'status-badge'
    },
    {
      name: 'Email_Response_Timestamp',
      type: 'date',
      includeTime: true,
      title: 'Email Response Timestamp',
      sortingactive: true
    },
    {
      name: 'actions',
      btnType: 'multipleButton',
      type: 'actions',
      btnName: 'use',
      title: 'Open RFQ',
      sortingactive: false,
      minWidth: '100px',
      component: ActionButton,
      multipleButtons: [
        {
          icon: <OpenInNewIcon fontSize="16px" />,
          type: 'icon',
          buttonOnClick: (type, rowData) => {
            window.open(`${ReservedSingleRfqPageUrl}/${rowData?.RFQID}`, '_blank');
          },
          color: 'primary',
          tooltip: 'Open RFQ'
        }
      ]
    },
    {
      name: 'actions',
      btnType: 'multipleButton',
      type: 'actions',
      btnName: 'use',
      title: 'Process Mail',
      sortingactive: false,
      minWidth: '100px',
      component: ActionButton,
      multipleButtons: [
        {
          icon: <SyncIcon fontSize="16px" />,
          type: 'icon',
          buttonOnClick: async (type, rowData) => {
            let res = await dispatch(updateEmailStatusAction({ id: rowData?.ID, status: 'PROCESSED' }));
            if (res?.payload?.success) {
              dispatch(getAllEmailPriceRequestAction());
            }
          },
          showButton: 'isNotProcessedMail',
          color: 'primary',
          tooltip: 'Process Mail'
        }
      ]
    },
    {
      name: 'actions',
      btnType: 'multipleButton',
      type: 'actions',
      btnName: 'use',
      title: 'Action',
      sortingactive: false,
      minWidth: '100px',
      component: ActionButton,
      multipleButtons: [
        {
          type: 'icon',
          icon: <RemoveRedEyeIcon fontSize="16px" />,
          buttonOnClick: (type, data) => {
            setSingleRequest({
              ...data,
              supplierEmail: data?.Supplier_Email,
              ccEmail: data?.KAM_Email,
              rfq: data?.RFQID ? [data?.RFQID] : [],
              selectedMaterials: {
                [data?.RFQID]:
                  (!isEmpty(data.Materials) &&
                    JSON.parse(data?.Materials || '')?.map((material) => ({
                      ...material,
                      Material_ID: material.materialID,
                      Part_Number: material.partNumber,
                      Quantity_Required: material.quantity
                    }))) ||
                  []
              }
            });
          },
          color: 'primary',
          tooltip: 'View'
        },
        {
          icon: <AddLinkIcon fontSize="16px" />,
          type: 'icon',
          buttonOnClick: (type, rowData) => {
            window.open(`${emailResponsesPageUrl}/${rowData?.RFQID}`, '_blank');
          },
          color: 'error',
          showButton: 'isResponseRecieved',
          tooltip: 'Check replies'
        }
      ]
    }
  ];

  const handleClose = () => {
    setSingleRequest({});
  };

  const customFilters = [
    {
      type: 'autocomplete',
      name: 'KAM_Name',
      placeholder: 'KAM',
      hideFilter: !isSuperUser(),
      items: usersData?.data?.map((user) => ({
        label: `${user?.FirstName} ${user?.LastName}`,
        value: `${user?.FirstName} ${user?.LastName}`
      }))
    },
    {
      type: 'date',
      name: 'Email_Sent_Timestamp',
      label: 'Email_Sent_Timestamp'
    },
    {
      type: 'tab',
      name: 'Email_Status',
      items: [
        {
          label: 'ALL',
          value: 'ALL'
        },
        {
          label: 'SENT',
          value: 'SENT'
        },
        {
          label: 'PROCESSED',
          value: 'PROCESSED'
        },
        {
          label: 'REPLIED',
          value: 'REPLIED'
        }
      ]
    }
  ];
  return (
    <>
      {emailRequestsData?.loading && <Loader />}

      <AlertDialog
        Component={
          <>
            <Box
              display="flex"
              alignItems="center"
              gap={1}
              mb={2}
              position={'sticky'}
              top={0}
              zIndex={2}
              pb={1}
              className="email-request-header"
            >
              <IconButton
                variant="contained"
                color="secondary"
                onClick={() => setSingleRequest(false)}
                className="email-request-icon-button"
              >
                <ArrowBackIosIcon color="primary" className="icon" />
              </IconButton>

              <Typography variant="h4" color="primary">
                Email Price Request
              </Typography>
            </Box>
            <Step2 values={singleRequest} isSentEmail={true} />
          </>
        }
        cancel={handleClose}
        open={!isEmpty(singleRequest) ? true : false}
        showCard={false}
        fullScreen={true}
        borderRadius="20px"
      />

      <Grid item xs={12}>
        <div>
          <Card>
            <Divider />
            <CardContent sx={{ width: '100%', overflow: 'hidden' }}>
              <TableComponent
                customFilters={customFilters}
                maxHeight={'100%'}
                enablePagination={true}
                columns={headers?.filter((item) => !item?.hideColumn) || []}
                rows={emailRequests || []}
                title="MySupplierTable"
                enableSearch={true}
                handleSearch={(data, searchValue) => {
                  setSearchValues({ ...searchValue });
                  setEmailRequests(data);
                }}
                allRows={allEmailRequests || []}
                placeActionButtonsIn="search"
                showDeleteIcon={true}
              />
            </CardContent>
          </Card>
        </div>
      </Grid>
    </>
  );
};
export default EmailPriceRequest;
