import React from 'react';
import { Slider, Typography, Box } from '@mui/material';

const SliderComponent = ({
  value,
  min = 0,
  max = 100,
  step = 1,
  onChange,
  defaultValue = 50,
  showValueLabel = true,
  disabled = false,
  orientation = 'horizontal',
  ...props
}) => {

  const handleChange = (event, newValue) => {
    if (onChange) {
      onChange(newValue);
    }
  };

  return (
    <Box {...props} sx={{ width: orientation === 'horizontal' ? '100%' : 'auto' }}>
      <Slider
        value={value}
        defaultValue={defaultValue}
        min={min}
        max={max}
        step={step}
        onChange={handleChange}
        valueLabelDisplay={showValueLabel ? 'auto' : 'off'}
        disabled={disabled}
        orientation={orientation}
        {...props} // to handle any additional props
      />
      {showValueLabel && <Typography>{value}</Typography>}
    </Box>
  );
};

export default SliderComponent;
