import React from 'react';

// import 'animate.css';

const customSweetAlert = ({ title, showCancelButton, confirmButtonText, cancelButtonText, confirmButton,theme }) => {

  // Swal.fire({
  //   title:  title ,
  //   customClass: {
  //       title: 'custom-title-class' // Custom CSS class for the title
  //     }, 

  //   showClass: {
  //     popup: 'animate__animated animate__fadeInUp animate__faster'
  //   },
  //   hideClass: {
  //     popup: 'animate__animated animate__fadeOutDown animate__faster'
  //   },
  //   showCancelButton:  showCancelButton ,
  //   confirmButtonText:  confirmButtonText ,
  //   confirmButtonColor: theme?.palette?.primary?.main,
  //   cancelButtonColor: theme.palette.error.main,
  //   cancelButtonText: cancelButtonText ,
  //   icon: 'question'
  // }).then((result) => {
  //   if (result.isConfirmed) {
  //     confirmButton();
  //   } else if (result.dismiss === Swal.DismissReason.cancel) {
  //   }
  // });
};

export default customSweetAlert;
