const { validate } = require("../middlewares/validation.middleware");
const {
  getAutomationInfo,
  automatedRFQRequest,
} = require("./automation.controller");
const { fetchInfoSchema } = require("./automation.validation");

const router = require("express").Router();

router.post(
  "/fetch-info",
  validate(fetchInfoSchema, "body"),
  getAutomationInfo
);

router.get("/triggerAutomation", automatedRFQRequest);

module.exports = router;
