import React, { useEffect, useState } from 'react';
import { Typo<PERSON>, Button, Container, Box, Paper, Divider } from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import Loader from 'components/Loader';
import { useDispatch, useSelector } from 'react-redux';
import { getSingleClient, updateClientApprovalStatus } from 'redux/reducers/clientReducer';
import { Person, LocationOn, Business, CalendarToday, AccountCircle } from '@mui/icons-material';
import TextAreaComponent from 'pages/component/textArea';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import ClientDetailTable from './clientDetail';
import './clientApproval.css';
import { showAlert } from 'utils/helper';
import ThankyouMessage from '../thankyou';
import { APPROVED, REJECTED } from 'utils/constant';
import Header from '../header';

const ClientRejectionPage = () => {
  const { id: clientID, token } = useParams();
  const clientData = useSelector((state) => state.client);
  const [reason, setReason] = useState('');
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getSingleClient(clientID));
  }, [clientID]);

  const handleReject = async () => {
    if (reason.trim()) {
      dispatch(updateClientApprovalStatus({ ID: clientID, token: token, status: REJECTED, reason }))
    } else {
      showAlert(dispatch, false, 'Reason is required', true)
    }
  };

  const fields = [
    { label: 'Name', key: 'Name', icon: <Person /> },
    { label: 'Address', key: 'Address', icon: <LocationOn /> },
    { label: 'Municipality', key: 'Municipality', icon: <LocationOn /> },
    { label: 'Business Activity', key: 'BusinessActivity', icon: <Business /> },
    { label: 'Updated Date', key: 'Updated_At', show: 'Updated_By_Name', type: 'date', icon: <CalendarToday /> },
    { label: 'Created By', key: 'Created_By', icon: <AccountCircle /> },
    { label: 'Updated By', key: 'Updated_By_Name', show: 'Updated_By_Name', icon: <AccountCircle /> },
    { label: 'Created Date', key: 'Created_At', type: 'date', icon: <CalendarToday /> },
  ];
  const { loading, singleClient } = clientData || {}
  const { Status, approverName, lastUpdatedStatus } = singleClient || {}

  if (Status === REJECTED || Status === APPROVED) {
    return <ThankyouMessage status={lastUpdatedStatus} name="Client" />
  }
  return (
    <>
      <Header approverName={approverName || ''} />
      <Container maxWidth="md" className="container">
        {loading && <Loader />}
        <Box textAlign="center">
          <CancelOutlinedIcon className="icon" color="error" />

          <Typography variant="h4" gutterBottom color="primary" className="title">
            Reject Client
          </Typography>

          <Divider sx={{ my: 2, bgcolor: 'secondary.main', height: 2, borderRadius: 1 }} />
          <Typography variant="body1" gutterBottom color="textSecondary" className="description">
            You are about to reject a client. Please review the details below and provide a reason for the rejection.
          </Typography>

          {singleClient && (
            <div className="client-details">
              <Typography variant="h5" gutterBottom color="secondary" className="clientDetailsTitle">
                Client Details
              </Typography>
              <ClientDetailTable fields={fields} clientData={singleClient} />
            </div>
          )}

          <TextAreaComponent
            id="rejection-reason"
            placeholder="Enter reason for rejection"
            value={reason}
            name="rejectionReason"
            onChange={(name, e) => setReason(e.target.value)}
            autoComplete="off"
            minRows={4}
            className="name-text-field textArea"
          />

          <Typography variant="body2" color="textSecondary">
            Providing a reason for rejection is mandatory. Please ensure that your reason is clear.
          </Typography>

          <Button
            variant="contained"
            color="error"
            onClick={handleReject}
            className="button"
            disabled={loading}
          >
            Confirm Rejection
          </Button>
        </Box>
      </Container>
    </>
  );
};

export default ClientRejectionPage;
