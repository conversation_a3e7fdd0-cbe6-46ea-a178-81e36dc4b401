import { Box, Grid, List<PERSON>temAvatar, ListItemButton, ListItemSecondaryAction, ListItemText, Select, Stack, Typography } from '@mui/material';
import { capitalize, get } from 'lodash';
import AvatarComponent from 'pages/component/avatar';
import Image from 'pages/component/image';
import { useDispatch } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { IRRELEVANT, QUOTING, RESERVED, addRfqApiUrl, addRfqPageUrl, singleRfqPageUrl } from 'utils/constant';
import { showAlert } from 'utils/helper';
import { assignAlertMessage } from 'utils/validationMessage';
import EditIcon from '@mui/icons-material/Edit';
import './automated.css';
import config from 'config';
import Part_Number_Available_img from '../../assets/images/logo/Part_Number_Available .webp';
import Part_Number_Detected_img from '../../assets/images/logo/Part_Number_Detected .png';
import hot_brand_image_img from '../../assets/images/logo/Hot_Brand.png';
import BadgeInputComponent from 'pages/component/table/badgeInput';

const AutomatedMaterialCard = ({
  filters,
  pageUrl,
  currentPage,
  isReservedRFQ,
  redirectTo,
  materialRequestedData,
  nextId,
  order,
  buttons,
  secondaryContent,
  kamList,
  loading,
  editPageUrl
}) => {
  const { RFQ_ID, RFQ_Name, Logo, Part_Number_Available, Part_Number_Detected, Hot_Brand } = order || {};
  const style = {
    rfqTitle: {
      color: config?.primaryColor
    },
    prioritization_img: {
      width: Hot_Brand && !Part_Number_Available ? '100px' : '80px',
      height: Hot_Brand && !Part_Number_Available ? '65px' : '80px',
      borderRadius: '50%',
      objectFit: 'contain'
    }
  };
  const getImage = () => {
    const { Part_Number_Available, Part_Number_Detected, Hot_Brand } = order || {};
    if (Part_Number_Available) {
      return Part_Number_Available_img;
    } else if (Hot_Brand) {
      return hot_brand_image_img;
    } else if (Part_Number_Detected) {
      return Part_Number_Detected_img;
    }
  };

  const navigateToSingleRfq = () => {
    let url = `${redirectTo}/${get(order, 'RFQ_ID', '#')}`;
    window.open(url, '_blank');
  };

  const handleCardClick = (event) => {
    event.preventDefault();
    navigateToSingleRfq();
  };

  const shouldShowImage = (item) => {
    return item?.type === 'img' && (Part_Number_Available || Part_Number_Detected || Hot_Brand);
  };
  // Function to render data item based on its type
  const renderDataItem = (dataItem) => {
    switch (dataItem?.type) {
      case 'text':
        return (
            <div style={{ display: 'flex' }}>
            <Typography  variant="body2" sx={{ minWidth: '130px' }}>
              {dataItem?.label}
            </Typography>
            <Typography  variant="body2" ml={3} sx={{ textAlign: 'right' }}>
              {dataItem?.value || 'N/A'}
            </Typography>
          </div>
        );
      case 'badge':
        return (
          <Box display="flex" alignItems="center">
            <Typography variant="body2">{dataItem?.label || 'N/A'}</Typography>
            <BadgeInputComponent className="automated-status-badge"  color={dataItem?.value || 'default'} badgeContent={dataItem?.value || 'N/A'} />
          </Box>
        );
      default:
        return ''
    }
  };

  return (
    <ListItemButton divider key={order.id} className="material-card" onClick={handleCardClick}>
      <ListItemAvatar className="material-icon">
        <Image src={Logo || ''} className="company-image" />
      </ListItemAvatar>
      <Grid item xs>
        <ListItemText
          primary={
            <Typography variant="subtitle1" color="secondary">
              <span style={{ ...style?.rfqTitle }}>{RFQ_ID || ''}</span> - {capitalize(RFQ_Name) || 'N/A'}
            </Typography>
          }
          className="secondary-container material-dates"
        />
        <Grid container className="secondary-container" spacing={2} pl={3}>
          {secondaryContent?.map((item, index) => (
            <Grid item xs={12} sm={6} md={4} key={index} p={0}>
              <Box display="flex" flexDirection="row" justifyContent="space-between" p={0} alignItems={'center'}>
                {item?.type === 'text' && (
                  <Typography variant="body2" mt={2}>
                    {`${item?.label}: ${item?.value || 'N/A'}`}
                  </Typography>
                )}

                {item?.type === 'array' && item?.data?.length > 0 && (
                  <Box mt={2}>
                    {item?.data?.map((dataItem, index) => (
                      <div style={{ display: 'flex' }} key={index}>
                        {renderDataItem(dataItem)}
                      </div>
                    ))}
                  </Box>
                )}

                {item?.type === 'badge' && item?.data?.length > 0 && (
                  <Box mt={2}>
                    {item.data.map((dataItem, index) => (
                      <Box key={index} display="flex" alignItems="center" mt={1}>
                        <Typography variant="body2">{dataItem?.label || 'N/A'}</Typography>
                        <BadgeInputComponent
                          className="status-badge"
                          color={dataItem?.value || 'default'}
                          badgeContent={dataItem?.value || 'N/A'}
                        />
                      </Box>
                    ))}
                  </Box>
                )}

                {shouldShowImage(item) && (
                  <Box display="flex" justifyContent="center" alignItems="center" style={{ flex: 1 }}>
                    <img src={getImage()} style={style?.prioritization_img} alt="Part Number" />
                  </Box>
                )}
              </Box>
            </Grid>
          ))}
        </Grid>
      </Grid>
      <div
        onClick={(event) => {
          event.stopPropagation();
        }}
      >
        <Stack alignItems="flex-end" spacing={1}>
          {order?.isManuallyAdded && (
            <Link to={`${editPageUrl}/${RFQ_ID || '#'}`} className="link">
              <EditIcon fontSize="10px" />
            </Link>
          )}

          {buttons?.map((button, index) => {
            switch (button.type) {
              case 'button':
                return (
                  <button.buttonComponent
                    materialRequestedData={materialRequestedData}
                    nextId={nextId}
                    pageUrl={pageUrl}
                    isReservedRFQ={isReservedRFQ}
                    redirectTo={redirectTo}
                    key={index}
                    order={order}
                    variant={button?.variant}
                    selectOptions={kamList}
                    loading={loading}
                    currentPage={currentPage}
                    filters={filters}
                    smallBtn={true}
                  />
                );
              case 'image':
                return (
                  <>
                    <Grid display="flex">
                      <Box display="block" mr={1} style={{ textAlign: 'right' ,marginTop: '8px'}}>
                        <Box display="block">
                          <Typography variant="caption" color="secondary" fontWeight={600} >
                            {get(button, 'name')}
                          </Typography>
                        </Box>
                      </Box>
                      <AvatarComponent src={get(button, 'src')} height="35px" width="35px" alt={get(button, 'name', '')} />
                    </Grid>
                  </>
                );
              default:
                return null;
            }
          })}
        </Stack>
      </div>
    </ListItemButton>
  );
};

export default AutomatedMaterialCard;
