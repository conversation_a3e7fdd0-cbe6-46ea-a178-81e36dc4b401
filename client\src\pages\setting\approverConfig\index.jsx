import { Button, Grid, Typography } from "@mui/material";
import { get } from "lodash";
import InputField from "pages/component/inputField";
import { validate } from "pages/component/validation";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateClientMaintenanace } from "redux/reducers/clientReducer";
import { updateApproverConfigAction } from "redux/reducers/settingReducer";

const initialState = {
  email: '',
}
const styles = {
  inputField: {
    width: '100%',
  },
  errorText: {
    color: 'red',
    marginTop: '4px',
  }
};


const ApproverConfig = () => {
  const [approver, setApprover] = useState(initialState);
  const setting = useSelector((state) => state.setting)
  const [errors, setErrors] = useState({});
  const dispatch = useDispatch()
  const handleChange = (name, value) => {
    setApprover({ ...approver, [name]: value });
    setErrors({ ...errors, [name]: '' });
  };
  const fieldConfigurations = [
    { type: 'text', name: 'email', placeholder: "Enter Approver's Email", label: 'Approver Email', showInput: true },
  ];

  const renderField = (fieldConfig) => {
    const { type, name, placeholder, label, value, rows, items, showInput } = fieldConfig || {};

    if (!showInput) return null;

    switch (type) {
      case 'text':
      case 'email':
        return (
          <Grid item xs={12} sm={4} key={name}>
            <Typography variant="h5" color='secondary' gutterBottom>{label}</Typography>
            <InputField
              type={type}
              name={name}
              placeholder={placeholder}
              value={approver[name] || ''}
              onChange={(e) => handleChange(name, e?.target?.value)}
              errors={errors}
              style={styles.inputField}
              fullWidth
            />
          </Grid>
        );

      default:
        return null;
    }
  };

  useEffect(() => {
    const approverDetail = get(setting, 'data.approver', {})
    setApprover({ ...approverDetail, email: approverDetail?.Email })
  }, [setting?.data])

  const submit = () => {
    const rules = {
      email: { required: true, type: 'email', label: 'Approver Email' },
    };
    const validation = validate(approver, rules);
    setErrors(validation);
    if (!Object.keys(validation).length) {
      dispatch(updateApproverConfigAction(approver?.email));
    }
  }
  return (<>
    <Grid container spacing={3} >
      {fieldConfigurations.map(config => renderField(config))}

    </Grid>
    <div className="button-container">
      <Button variant='contained' onClick={submit} disabled={setting?.loading}>Submit</Button>
    </div>

  </>)
}
export default ApproverConfig