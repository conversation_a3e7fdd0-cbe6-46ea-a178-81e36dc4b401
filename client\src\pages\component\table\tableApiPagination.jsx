/* eslint-disable jsx-a11y/img-redundant-alt */
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableContainer,
  TablePagination,
  Tooltip,
  TableSortLabel,
  Typography,
  Button,
  alpha
} from '@mui/material';
import { getComparator, stableSort } from '../sorting';
import { get } from 'lodash';
import { convertDateToStringFormat, formattedPrice, getDateWithTime } from 'utils/helper';
import './table.css';
import NodataImage from '../../../assets/images/users/no-data-image.png';
import { Link } from 'react-router-dom';
import TableSearch from './tableSearch';
import BadgeInputComponent from './badgeInput';
import ActionCell from './actionCell';
import renderArrayCell from './renderArrayCell';

const TableApiPaginationComponent = ({
  rows,
  applyFilterOnKeyDown,
  columns,
  showDeleteButton,
  enableSearch,
  allRows,
  handleSort,
  pageNo,
  handleSearch,
  enablePagination,
  generatePdf,
  loading,
  defaultPage,
  tableHeading,
  actionBtns,
  tableCardHeaderClass,
  maxHeight,
  isBadgeFilter,
  badgeFilterData,
  placeActionButtonsIn,
  showDeleteIcon,
  columnValues,
  isBackendPagination,
  onPageChange,
  onPageLimitChange,
  totalCount
}) => {
  const [order, setOrder] = useState('desc');
  const [orderBy, setOrderBy] = useState('date');
  const [page, setPage] = useState(pageNo - 1 || 0);
  const [rowsPerPage, setRowsPerPage] = useState(defaultPage || 10);

  useEffect(() => {
    setPage(pageNo - 1 || 0);
  }, [pageNo]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    if (isBackendPagination) {
      onPageChange(newPage);
    }
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
    if (isBackendPagination) {
      onPageLimitChange(+event.target.value);
    }
  };

  const displayedRows = enablePagination
    ? stableSort(rows, getComparator(order, orderBy))
    : stableSort(rows, getComparator(order, orderBy));

  const highlightRow = (row, name) => {
    return row[name] && 'black';
  };

  const ActionButon = () => {
    return (
      <div className="action-buttons">
        {actionBtns?.map((actionBtn) => (
          <Button
            variant={actionBtn?.variant || 'contained'}
            color={actionBtn?.color || 'primary'}
            onClick={actionBtn?.onClick}
            className={actionBtn?.className}
            disabled={actionBtn?.disabled}
          >
            {actionBtn?.title}
          </Button>
        ))}
      </div>
    );
  };

  return (
    <>
      <div className={tableCardHeaderClass || 'table-cardHeader-class'}>
        {tableHeading && (
          <Typography variant="h5" color="secondary">
            {tableHeading}
          </Typography>
        )}
        {placeActionButtonsIn === 'header' && <ActionButon />}
      </div>

      {enableSearch && (
        <div className={tableCardHeaderClass || 'table-cardHeader-class'}>
          <TableSearch
            applyFilterOnKeyDown={applyFilterOnKeyDown}
            allRows={allRows}
            handleSearch={(data, searchValue) => handleSearch(data, searchValue)}
            isBadgeFilter={isBadgeFilter}
            badgeFilterData={badgeFilterData}
          />

          {placeActionButtonsIn === 'search' && <ActionButon />}
        </div>
      )}
      <TableContainer sx={{ maxHeight: maxHeight || 440 }}>
        <Table stickyHeader aria-label="all-products" size="small">
          <TableHead>
            <TableRow>
              {columns?.map((head) => {
                return head.sortingactive ? (
                  <TableCell
                    sortDirection="desc"
                    variant="contained"
                    color="primary"
                    sx={{ minWidth: head?.minWidth, color: 'black', fontWeight: 500 }}
                  >
                    <Tooltip enterDelay={300} title="Sort">
                      <TableSortLabel
                        direction={order}
                        onClick={() => {
                          setOrder(order === 'asc' ? 'desc' : 'asc');
                          setOrderBy(head.name);
                          if (handleSort) {
                            handleSort({
                              type: 'sort',
                              field: head.name,
                              data: order === 'asc' ? 'DESC' : 'ASC'
                            });
                          }
                        }}
                      >
                        {head.title}
                      </TableSortLabel>
                    </Tooltip>
                  </TableCell>
                ) : (
                  <TableCell variant="contained" color="primary" sx={{ color: 'black', fontWeight: 500 }}>
                    <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                      <div>{head.title}</div>
                      {head?.columnAction && (
                        <ActionCell isColumnAction={true} columnValues={columnValues} column={head?.columnAction} loading={loading} />
                      )}
                    </div>
                  </TableCell>
                );
              })}
            </TableRow>
          </TableHead>

          {rows?.length ? (
            <TableBody>
              {displayedRows?.map((data, index) => (
                <TableRow
                  sx={
                    data && data?.status && data?.status === 'VOID' && {
                      bgcolor: (theme) => alpha(theme.palette.error.main ,0.06),
                      '&:hover': {
                        backgroundColor: '#8f292a1a !important'
                      }
                    }
                  }
                  key={data.id}
                  hover
                >
                  {columns?.map((column, colIndex) => {
                    const {
                      name,
                      type,
                      keyName,
                      dateformat,
                      includeTime,
                      showToday,
                      className,
                      onlyTextColour,
                      badgeClassName,
                      showButton,
                      defaultValue,
                      highlight
                    } = column || {};
                    const cellData = data?.[name] === 0 ? '0' : get(data, name, '-');
                    switch (type) {
                      case 'date':
                        return (
                          <TableCell sx={{ color: highlightRow(data, highlight) }} key={colIndex}>
                            {get(data, name, '')
                              ? includeTime
                                ? getDateWithTime(cellData)
                                : convertDateToStringFormat(cellData, dateformat, showToday)
                              : '-'}
                          </TableCell>
                        );
                      case 'price':
                        return (
                          <TableCell sx={{ color: highlightRow(data, highlight) }} key={colIndex}>
                            {get(data, name, '') ? formattedPrice(cellData) : '-'}
                          </TableCell>
                        );

                      case 'email':
                        return (
                          <TableCell key={colIndex} style={{ textTransform: 'lowercase', color: highlightRow(data, highlight) }}>
                            {cellData}
                          </TableCell>
                        );
                      case 'link':
                        return (
                          <TableCell key={colIndex}>
                            <Link to={cellData || '#'} className="link" target="_blank" rel="noopener noreferrer">
                              <TableCell className="link">Link</TableCell>
                            </Link>
                          </TableCell>
                        );
                      case 'badge':
                        return (
                          <TableCell key={colIndex} className={className}>
                            <BadgeInputComponent
                              onlyTextColour={onlyTextColour}
                              badgeContent={cellData}
                              color={cellData}
                              onClick={() => {}}
                              className={badgeClassName}
                            />
                          </TableCell>
                        );
                      case 'array':
                        return renderArrayCell(
                          data[keyName],
                          name,
                          column?.arrayType || 'text',
                          dateformat,
                          includeTime,
                          showToday,
                          column?.actionBtns,
                          column,
                          data
                        );

                      case 'actions':
                        return showButton ? (
                          data[showButton] ? (
                            <ActionCell
                              column={column}
                              data={data}
                              index={index}
                              loading={loading}
                              showDeleteButton={showDeleteButton}
                              generatePdf={generatePdf}
                              showDeleteIcon={showDeleteIcon}
                            />
                          ) : null
                        ) : (
                          <ActionCell
                            column={column}
                            data={data}
                            index={index}
                            loading={loading}
                            showDeleteIcon={showDeleteIcon}
                            showDeleteButton={showDeleteButton}
                            generatePdf={generatePdf}
                          />
                        );
                      default:
                        return (
                          <TableCell sx={{ color: highlightRow(data, highlight), whiteSpace: 'pre-line' }} key={colIndex}>
                            {cellData || '-'}
                          </TableCell>
                        );
                    }
                  })}
                </TableRow>
              ))}
            </TableBody>
          ) : (
            <TableBody>
              <TableRow>
                <TableCell align="center" colSpan={columns?.length}>
                  <Typography variant="h6" m={2}>
                    There are no records to display
                  </Typography>
                </TableCell>
              </TableRow>
            </TableBody>
          )}
        </Table>
      </TableContainer>

      {enablePagination && (
        <TablePagination
          rowsPerPageOptions={[5, 10, 20]}
          component="div"
          count={totalCount || rows?.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      )}
    </>
  );
};

TableApiPaginationComponent.propTypes = {
  rows: PropTypes.arrayOf(PropTypes.object).isRequired,
  columns: PropTypes.arrayOf(PropTypes.object).isRequired,
  showDeleteButton: PropTypes.bool,
  enableSearch: PropTypes.bool,
  allRows: PropTypes.array,
  handleSearch: PropTypes.func.isRequired,
  enablePagination: PropTypes.bool,
  generatePdf: PropTypes.func,
  loading: PropTypes.bool,
  tableHeading: PropTypes.string,
  actionBtns: PropTypes.array,
  tableCardHeaderClass: PropTypes.string,
  minHeight: PropTypes.number,
  isBadgeFilter: PropTypes.bool,
  badgeFilterData: PropTypes.array
};

TableApiPaginationComponent.defaultProps = {
  showDeleteButton: false,
  enableSearch: false,
  allRows: [],
  enablePagination: false,
  generatePdf: () => {},
  loading: false,
  tableHeading: '',
  actionBtns: [],
  tableCardHeaderClass: '',
  minHeight: 440,
  isBadgeFilter: false,
  badgeFilterData: []
};

export default TableApiPaginationComponent;
