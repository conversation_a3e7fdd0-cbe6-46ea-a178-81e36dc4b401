const router = require("express").Router();
const { validate } = require("../middlewares/validation.middleware");
const {
  getAvailableBrands,
  getListofCentralizedMaterialForQuoting,
  requestPrice,
  getRequestedMaterialsPrice,
  addRequestedMaterialsPrice,
} = require("./centralizedMaterialQuoting.controller");
const { getCentralizedMaterialListSchema } = require("./centralizedMaterialQuoting.validation");

router.get("/get-available-brands", getAvailableBrands);

router.post("/get-materials",validate(getCentralizedMaterialListSchema, "body"), getListofCentralizedMaterialForQuoting);

router.post("/request-price", requestPrice);

router.post("/get-requested-materials-price", getRequestedMaterialsPrice);

router.post("/add-requested-materials-price", addRequestedMaterialsPrice);

module.exports = router;
