import React from 'react';
import PropTypes from 'prop-types';
import InputField from 'pages/component/inputField';
import { get } from 'lodash';
const TextFilter = ({ data, handleFilterChange }) => {
  return (
    <>
      <div className='filter-input'>
        <InputField type="text" placeholder="Enter your search" value={get(data,'data')} onChange={handleFilterChange} className="search-field" />
      </div>
    </>
  );
};
TextFilter.propTypes = {
  data: PropTypes.object.isRequired,
  handleFilterChange: PropTypes.func.isRequired
};
export default TextFilter;
