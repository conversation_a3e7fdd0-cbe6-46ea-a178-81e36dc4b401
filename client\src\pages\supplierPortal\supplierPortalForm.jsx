import React, { useEffect, useState } from 'react';
import { Grid, Typography, Select, MenuItem, Button } from '@mui/material';
import MainCard from 'components/MainCard';
import InputField from 'pages/component/inputField';
import SelectComponent from 'pages/component/selectComponent';
import AlertDialog from 'pages/component/dialogbox';
import { get } from 'lodash';
import ErrorMessage from 'pages/component/errorMessage';
import ClearIcon from '@mui/icons-material/Clear';
import TextAreaComponent from 'pages/component/textArea';
import { REQUESTEDQUOTE, RESPONDEDDQUOTE } from 'pages/monitorRfq/constant';
import BadgeInputComponent from 'pages/component/table/badgeInput';
import { useDispatch } from 'react-redux';
import { removeRequestAction } from 'redux/reducers/supplierPortalReducer';

const SupplierPortalForm = ({
  quote,
  handleSubmitPrice,
  quoteList,
  onSubmit,
  supplierID,
  onSave,
  open,
  handleOpen,
  handleClose,
  errors,
  loading
}) => {
  const [formData, setFormData] = useState([]);
  const dispatch = useDispatch();

  useEffect(() => {
    const data = quoteList?.map((item) => {
      const updatedItem = { ...item };
      if (item.currency || item.totalPrice || item.unitPrice || item.productCondition) {
        updatedItem.showValid = true;
      } else {
        updatedItem.showValid = false;
      }
      return updatedItem;
    });
    setFormData(data?.filter((item) => item?.status !== RESPONDEDDQUOTE) || []);
  }, [quoteList]);

  const handleChange = (index, field, value) => {
    const newFormData = formData.map((item, i) => {
      if (i === index) {
        if (field === 'showInput' && value === false) {
          let quote = quoteList?.find((quote) => quote?.materialID === item?.materialID);
          return {
            ...item,
            notes: '',
            [field]: value
          };
        } else {
          const data = {
            ...item,
            [field]: value
          };
          if (field === 'currency' || field === 'unitPrice' || field === 'productCondition') {
            data.showValid = true;
            if (field === 'unitPrice') {
              data.isPriceAvailable = value > 0 ? false : true;
            }
          } else {
            data.showValid = false;
          }
          return data;
        }
      }
      return item;
    });
    setFormData(newFormData);
  };

  const handleBlur = (index, field, value) => {
    const newFormData = formData.map((item, i) => {
      if (i === index) {
        if (field === 'quantity' || field === 'unitPrice') {
          let totalPrice = (Number(item?.quantity || 0) * Number(value || 0)).toFixed(2);

          return {
            ...item,
            totalPrice: Number(totalPrice)
          };
        }
      }
      return item;
    });
    setFormData(newFormData);
  };

  const inputField = [
    { name: 'partNumber', label: 'Part Number', defaultValue: '-' },
    { name: 'brand', label: 'Brand', defaultValue: '-' },
    { name: 'RFQ_Number', label: 'RFQ Number', defaultValue: '-' },
    { name: 'description', label: 'Description', defaultValue: '-' },
    { name: 'quantity', label: 'Quantity', defaultValue: '0' },
    { name: 'status', label: 'Status', inputType: 'number', type: 'badge', onlyTextColour: true },
    {
      label: 'Currency',
      name: 'currency',
      type: 'select',
      menuItem: [
        { label: 'USD', value: 'USD' },
        { label: 'EUR', value: 'EUR' },
        { label: 'GBP', value: 'GBP' },
        { label: 'CLP', value: 'CLP' },
        { label: 'AUD', value: 'AUD' }
      ]
    },
    {
      name: 'unitPrice',
      label: 'Unit price',
      defaultValue: '0',
      type: 'input',
      inputType: 'number',
      onBlur: (index, field, value) => {
        handleBlur(index, field, value);
      }
    },
    { name: 'totalPrice', label: 'Total Price', defaultValue: '0', inputType: 'number' },
    { name: 'leadTime', label: 'Lead Time', defaultValue: '0', inputType: 'number', type: 'input' },
    {
      name: 'productCondition',
      label: 'Product Condition',
      type: 'select',
      minWidth: '150px',
      menuItem: [
        { label: 'NEW', value: 'NEW' },
        { label: 'NEW SURPLUS', value: 'NEW SURPLUS' },
        { label: 'NEW FACTORY SEALED', value: 'NEW FACTORY SEALED' },
        { label: 'NEW OPEN BOX', value: 'NEW OPEN BOX' }
      ]
    },
    {
      name: 'notes',
      label: 'Notes',
      buttonLabel: 'Add Notes',
      btnName: 'showInput',
      minWidth: '150px',
      type: 'button',
      btnType: 'inputType',
      variant: 'contained'
    },
    {
      name: 'notes',
      label: 'Action',
      buttonLabel: 'Mark Unavailable',
      btnName: 'RemoveRequest',
      type: 'button',
      color: 'error',
      variant: 'contained',
      minWidth: '150px',
      onClick: (material) => handleRemoveRequest(material)
    },
    {
      name: 'notes',
      label: 'Action',
      buttonLabel: 'Submit Price',
      btnName: 'RemoveRequest',
      type: 'button',
      variant: 'contained',
      disableBtn: 'isPriceAvailable',
      minWidth: '150px',
      onClick: (material, index) => {
        let updatedFormData = formData.map((item, i) => {
          if (i === index) {
            return {
              ...item,
              isIndividualPrice: true
            };
          }
          return item;
        });
        onSubmit(updatedFormData, true);
      }
    }
  ];

  const handleRemoveRequest = (material) => {
    let { materialID, partNumber } = material || {};

    dispatch(removeRequestAction({ data: quote, supplierId: supplierID, materialID: materialID?.toString(), partNumber }));
  };

  const renderField = (item, index, material, error) => {
    const { type, name, inputType, menuItem, variant, disableBtn, color, onClick, btnType, buttonLabel, btnName, onBlur, onlyTextColour } =
      item;
    const value = material[name];

    const errormessage = get(error, `${name}`, '');
    switch (type) {
      case 'input':
        return (
          <InputField
            type={inputType}
            style={{ width: '100%' }}
            value={value}
            name={name}
            errorMessage={errormessage}
            onChange={(e) => handleChange(index, name, e.target.value)}
            handleBlur={(e) => onBlur(index, name, e.target.value)}
          />
        );
      case 'select':
        return (
          <>
            <SelectComponent
              value={value}
              onChange={(e) => handleChange(index, name, e.target.value)}
              name={name}
              error={errormessage}
              fullWidth
              items={menuItem || []}
            />
            {errormessage && <ErrorMessage message={errormessage} />}
          </>
        );
      case 'badge':
        return <BadgeInputComponent onlyTextColour={onlyTextColour} badgeContent={value} color={value} />;
      case 'button':
        return (
          <>
            {!material[btnName] ? (
              <Button
                variant={variant}
                size="small"
                disabled={material[disableBtn] || loading}
                color={color || 'primary'}
                onClick={() => {
                  btnType === 'inputType' ? handleChange(index, btnName, true) : onClick(material, index, btnName, true);
                }}
              >
                {buttonLabel}
              </Button>
            ) : (
              <div className="add-notes">
                <TextAreaComponent
                  minRows={2}
                  style={{ width: '100%', paddingTop: '10px', marginLeft: '5px' }}
                  value={value}
                  name={name}
                  className="name-text-field"
                  onChange={(name, e) => handleChange(index, name, e.target.value)}
                />

                {material?.crossBtn && (
                  <div>
                    <ClearIcon
                      color="error"
                      className="cross-icon"
                      onClick={() => {
                        handleChange(index, btnName, false);
                      }}
                    />
                  </div>
                )}
              </div>
            )}
          </>
        );
      default:
        return (
          <Typography
            variant="body1"
            sx={{
              overflowWrap: 'break-word',
              wordWrap: 'break-word',
              marginTop: '10px'
            }}
          >
            {value}
          </Typography>
        );
    }
  };

  const buttons = [
    { label: 'Submit', onClick: () => onSubmit(formData), variant: 'contained', disabled: loading },
    { label: 'Add Later', onClick: () => onSave(formData), variant: 'contained', disabled: loading }
  ];
  const checkStatus = () => {
    return formData?.some((material) => !material?.status || material.status === REQUESTEDQUOTE);
  };
  return (
    <>
      <AlertDialog
        cancel={() => handleClose()}
        borderRadius="20px"
        showCancelBtn={true}
        buttons={buttons || []}
        Component={
          <div style={{ minWidth: '350px' }}>
            <Typography variant="h5" color="secondary">
              Do you want to continue adding data later or are you done?
            </Typography>
          </div>
        }
        open={open}
      />

      <MainCard className="maincard-border" boxShadow={true} sx={{ mt: 1 }}>
        <div className="submit-btn">
          <Button
            variant="contained"
            sx={{ mr: 1 }}
            onClick={() => {
              handleOpen();
            }}
            disabled={loading || !checkStatus()}
          >
            Save
          </Button>
          <Button variant="contained" onClick={() => onSubmit(formData)} disabled={loading || !checkStatus()}>
            Submit
          </Button>
        </div>
        {checkStatus() ? (
          <div className="scrollable-container">
            {formData && formData.length > 0 ? (
              <>
                {/* Fixed header */}
                <Grid container spacing={1} className="fixed-header">
                  {inputField.map((item, fieldIndex) => (
                    <Grid item key={fieldIndex} sx={{ minWidth: item?.minWidth || '100px', width: '100%' }}>
                      <Typography color="secondary" variant="subtitle1">
                        {item.label}
                      </Typography>
                    </Grid>
                  ))}
                </Grid>

                {/* Scrollable content */}
                {formData.map((material, index) => (
                  <Grid
                    container
                    spacing={1}
                    sx={{ mb: index === formData?.length - 1 && 2 }}
                    className="supplier-portal-content-container"
                    key={index}
                  >
                    {inputField.map((item, fieldIndex) => (
                      <Grid item key={fieldIndex} sx={{ minWidth: item?.minWidth || '100px', width: '100%' }}>
                        {!material?.status || material?.status !== RESPONDEDDQUOTE
                          ? renderField(item, index, material, errors[index])
                          : null}
                      </Grid>
                    ))}
                  </Grid>
                ))}
              </>
            ) : (
              <p className="no-data">No materials request found</p>
            )}
          </div>
        ) : (
          !loading &&
          (formData && formData?.length > 0 ? (
            <Typography variant="h6" color="textSecondary" align="center">
              All materials have been responded.
            </Typography>
          ) : (
            <p className="no-data">No materials request found</p>
          ))
        )}
      </MainCard>
    </>
  );
};

export default SupplierPortalForm;
