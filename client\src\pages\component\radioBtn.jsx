import * as React from 'react';
import PropTypes from 'prop-types';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';

const propTypes = {
  labels: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    })
  ).isRequired,
  defaultValue: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const RadioButtonsGroup = ({ labels, defaultValue, name, onChange,value ,row}) => {
  return (
    <FormControl>
      <RadioGroup
        aria-labelledby={`${name}-label`}
        defaultValue={defaultValue}
        value={value}
        name={name}
        onChange={(value) => onChange(value)}
        row={row}
      >
        {labels?.map((label) => (
          <FormControlLabel
          sx={{ 
            '& .MuiSvgIcon-root': {
              fontSize: 14,  // Adjust the size as needed
            },
          }}
            key={label?.value}
            value={label?.value}
            control={<Radio />}
            label={label.label}
          />
        ))}
      </RadioGroup>
    </FormControl>
  );
};

RadioButtonsGroup.propTypes = propTypes;

export default RadioButtonsGroup;
