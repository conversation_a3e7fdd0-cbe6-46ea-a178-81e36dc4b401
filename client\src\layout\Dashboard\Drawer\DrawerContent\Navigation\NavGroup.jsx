import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
// material-ui
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Collapse from '@mui/material/Collapse';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';

import NavItem from './NavItem';
import { useGetMenuMaster } from 'api/menu';

export default function NavGroup({ item, type, handleClick, open }) {
  const { menuMaster } = useGetMenuMaster();
  const drawerOpen = menuMaster.isDashboardDrawerOpened;

  const renderNavItems = (items, level = 1) => {
    return items?.map((menuItem) => {
      if (menuItem.type === 'collapse') {
        return (
          <NavCollapse
            key={menuItem.id}
            menuItem={menuItem}
            level={level}
            title={menuItem?.title}
            Icon={menuItem?.icon}
            handleClick={handleClick}
            open={open}
          />
        );
      } else if (menuItem.type === 'item') {
        return <NavItem key={menuItem.id} item={menuItem} level={level} />;
      } else {
        return (
          <Typography key={menuItem.id} variant="h6" color="error" align="center">
            Fix - Group Collapse or Items
          </Typography>
        );
      }
    });
  };

  const Icon = item.icon;
  const itemIcon = item.icon ? <Icon style={{ fontSize: drawerOpen ? '1rem' : '1.25rem' }} /> : null;
  const isOpen = open === item?.title;

  return (
    <>
      {type === 'collapse' ? (
        <>
          <List
            onClick={() => handleClick(item)}
            subheader={
              item.title &&
              drawerOpen && (
                <Box className="nav-collapse">
                  <div style={{ display: 'flex', gap: '10px' }}>
                    {itemIcon}
                    <Typography variant="h6">{item.title}</Typography>
                  </div>
                  {isOpen ? <ExpandLess className="collapse-icon" /> : <ExpandMore className="collapse-icon" />}
                </Box>
              )
            }
            sx={{ mt: '10px', py: 0, zIndex: 0 }}
          />
          <Collapse in={isOpen} timeout="auto" unmountOnExit>
            <div style={{ marginLeft: '30px', backgroundColor: '#f7fcff' }}>{renderNavItems(item?.children)}</div>
          </Collapse>
        </>
      ) : (
        <List
          subheader={
            item.title &&
            drawerOpen && (
              <Box sx={{ pl: 3, mb: 1.5 }}>
                <Typography variant="subtitle2" color="textSecondary">
                  {item.title}
                </Typography>
              </Box>
            )
          }
          sx={{ mb: drawerOpen ? 1.5 : 0, py: 0, zIndex: 0 }}
        >
          {renderNavItems(item?.children)}
        </List>
      )}
    </>
  );
}

const NavCollapse = ({ menuItem, level, title, Icon }) => {
  const { menuMaster } = useGetMenuMaster();
  const [open, setOpen] = useState(false);
  const drawerOpen = menuMaster.isDashboardDrawerOpened;
  const itemIcon = Icon ? <Icon style={{ fontSize: drawerOpen ? '1rem' : '1.25rem' }} /> : null;

  const handleClick = () => {
    setOpen(!open);
  };

  return (
    <>
      <List
        component="div"
        onClick={handleClick}
        disablePadding
        subheader={
          title && (
            <Box className="nav-collapse">
              <div className="nav-collapse-icon">
                {itemIcon}
                <Typography variant="h6">{title}</Typography>
              </div>
              {open ? <ExpandLess className="collapse-icon" /> : <ExpandMore className="collapse-icon" />}
            </Box>
          )
        }
        sx={{ mt: '10px' }}
      ></List>
      <Collapse in={open} timeout="auto" unmountOnExit>
        {menuItem.children?.map((subItem) =>
          subItem.type === 'collapse' ? (
            <NavCollapse key={subItem.id} menuItem={subItem} level={level + 1} />
          ) : (
            <div className="nav-item-label">
              <NavItem key={subItem.id} item={subItem} level={level + 1} />
            </div>
          )
        )}
      </Collapse>
    </>
  );
};

NavGroup.propTypes = {
  item: PropTypes.object.isRequired
};

NavCollapse.propTypes = {
  menuItem: PropTypes.object.isRequired,
  level: PropTypes.number.isRequired
};
