import { lazy } from 'react';

// project import
import Loadable from 'components/Loadable';
import MinimalLayout from 'layout/MinimalLayout';
import { clientApprovePageUrl, clientRejectPageUrl, supplierApprovePageUrl, supplieRejectPageUrl, supplierPageUrl } from 'utils/constant';

// render - login
const SupplierPortal = Loadable(lazy(() => import('pages/supplierPortal')));
const ClientApprovalPage = Loadable(lazy(() => import('pages/setting/approve/client/clientApprove')));
const ClientRejectionPage = Loadable(lazy(() => import('pages/setting/approve/client/clientReject')));
const SupplierApprovalPage = Loadable(lazy(() => import('pages/setting/approve/supplier/supplierApprove')));
const SupplierRejectionPage = Loadable(lazy(() => import('pages/setting/approve/supplier/supplierReject')));

// ==============================|| AUTH ROUTING ||============================== //

const SupplierPortalLayout = {
  path: '/',
  element: <MinimalLayout />,
  children: [
    {
      path: `${supplierPageUrl}/:id`,
      element: <SupplierPortal />
    },
    {
      path: `${clientApprovePageUrl}/:id/:token`,
      element: <ClientApprovalPage />
    },
    {
      path: `${clientRejectPageUrl}/:id/:token`,
      element: <ClientRejectionPage />
    },
    {
      path: `${supplierApprovePageUrl}/:id/:token`,
      element: <SupplierApprovalPage />
    },
    {
      path: `${supplieRejectPageUrl}/:id/:token`,
      element: <SupplierRejectionPage />
    }
  ]
};

export default SupplierPortalLayout;
