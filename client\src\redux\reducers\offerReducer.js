import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { NOPRICEAVAILABLEQUOTE, RECIEVEDQUOTE, REGISTEREDQUOTE, REQUESTEDQUOTE } from 'pages/offer/offerMaterial/constant';
import { deleletRequest, getRequest, patchRequest, post } from 'utils/axios';
import {
  assignStatusApiUrl,
  calculateMarginApiUrl,
  calculateQuoteApiUrl,
  deleteQuoteApiUrl,
  duplicateOfferApiUrl,
  generateOffer,
  getClientListApiUrl,
  getEmailSupplierListApiUrl,
  getRequesteQuoteList,
  getSupplierListApiUrl,
  myOfferPageUrl,
  requestPriceApiUrl,
  sendQuoteRequestApiUrl,
  singleQuoteApiUrl,
  singleRfqApiUrl,
  updateQuoteStatusApiUrl,
  useQuoteApiUrl
} from 'utils/constant';
import { generateUniqueId, notification, showAlert } from 'utils/helper';

// Asynchronous thunk action to fetch profile data
export const getOfferDetail = createAsyncThunk('getOfferDetail/fetch', async (rfqId, { rejectWithValue, dispatch }) => {
  try {
    dispatch(offerSlice.actions.setLoading(true));
    const response = await post(singleQuoteApiUrl(rfqId), {}, true, dispatch);
    if (response) {
      const { error, success, message, data } = get(response, 'data', {});
      dispatch(offerSlice.actions.setLoading(false));
      if (success) {
        const modifiedMaterials = get(data, 'materials', [])?.map((material) => {
          const modifiedSuppliers = material?.suppliers?.reduce((result, supplier) => {
            const {
              QuoteID,
              supplierID,
              QuoteDate,
              unitPrice,
              UnitCurrency,
              quantity,
              Weight = '',
              ShippingCost,
              unitOfferPrice,
              Tax,
              TotalCost,
              OfferedPrice,
              Offered,
              LeadTime = '',
              leadTime,
              status,
              Price,
              notes,
              date
            } = supplier;
            const isDisabled = material?.suppliers?.some((otherSupplier) => otherSupplier?.Offered && otherSupplier?.QuoteID !== QuoteID);
            result.push({
              date: QuoteDate?.value ? new Date(QuoteDate?.value || null) : date || '',
              supplier: supplierID,
              sh: ShippingCost,
              totalCost: TotalCost,
              tax: Tax,
              weight: Weight,
              leadTime: LeadTime || leadTime,
              isTax: Tax !== 0,
              unitPrice: unitPrice || Price,
              supplierId: supplierID,
              id: QuoteID || generateUniqueId(),
              quantity: quantity,
              offer: Offered,
              currency: UnitCurrency,
              unitOfferPrice: unitOfferPrice,
              estimatedOfferCost: OfferedPrice,
              status: status || REGISTEREDQUOTE,
              isDisabled,
              notes
            });
            return result;
          }, []);

          return {
            ...material,
            materialId: material?.Material_ID,
            showSuppliers: material?.showSuppliers,
            suppliers: modifiedSuppliers
          };
        });

        const { Materials, ...modifiedData } = { ...data, materials: modifiedMaterials };
        return modifiedData;
      } else {
        dispatch(notification(false, message || response?.message || '', true));
        return rejectWithValue(message || 'Failed to fetch RFQ data');
      }
    } else {
      dispatch(offerSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(offerSlice.actions.setLoading(false));
    return rejectWithValue('An error occurred while fetching RFQ data');
  }
});

export const getSupplierList = createAsyncThunk('offer/getSupplierList', async (data, { dispatch, rejectWithValue }) => {
  try {
    dispatch(offerSlice.actions.setLoading(true));
    const response = await post(getSupplierListApiUrl, data, true, dispatch);
    if (response) {
      dispatch(offerSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        return { success: true, data: data?.suppliers };
      } else {
        dispatch(offerSlice.actions.setLoading(false));
        return { success: false, data: [] };
        // return rejectWithValue(message || 'Failed to fetch supplier list');
      }
    }
  } catch (error) {
    dispatch(offerSlice.actions.setLoading(false));
    return { success: false, data: [] };
    // return rejectWithValue('An error occurred while fetching supplier list');
  }
});

export const getSupplierEmailList = createAsyncThunk('offer/getSupplierEmailList', async (data, { dispatch, rejectWithValue }) => {
  let { setLoading, setSupplierListAction } = offerSlice?.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await post(getEmailSupplierListApiUrl, data, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(setSupplierListAction(data?.suppliers));
        return { success: true, data: data?.suppliers };
      } else {
        dispatch(setLoading(false));
        dispatch(setSupplierListAction([]));
        return { success: false, data: [] };
      }
    } else {
      dispatch(setSupplierListAction([]));
      return { success: false, data: [] };
    }
  } catch (error) {
    dispatch(setSupplierListAction([]));
    dispatch(setLoading(false));
    return { success: false, data: [] };
    // return rejectWithValue('An error occurred while fetching supplier list');
  }
});

export const getRequestedQuoteList = createAsyncThunk('offer/getRequestedQuoteList', async (data, { dispatch, rejectWithValue }) => {
  const { requestedQuote, ...rest } = data;
  const payload = { ...rest };
  try {
    dispatch(offerSlice.actions.setLoading(true));
    const response = await post(getRequesteQuoteList, payload, true, dispatch);
    if (response) {
      dispatch(offerSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        const updatedRequestData = data?.map((requested) => {
          return {
            ...requested,
            id: generateUniqueId(),
            supplierId: requested?.supplierID,
            isPriceAvailable: requested?.status === NOPRICEAVAILABLEQUOTE,
            use: requested?.use || false,
            select: requested?.status === REQUESTEDQUOTE || false,
            price: requested?.unitPrice || '-',
            date: requested?.date?.value || '',
            isDisabled: requested?.status === REQUESTEDQUOTE ? true : false
          };
        });
        dispatch(offerSlice.actions.setRequestedQuoteList(updatedRequestData));
        return { success: true, data: data }; // Return the supplier list data
      } else {
        dispatch(notification(false, message || response?.message || '', true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(offerSlice.actions.setLoading(false));
      return { success: false, data: [] };
    }
  } catch (error) {
    dispatch(offerSlice.actions.setLoading(false));
    return { success: false, data: [] };
  }
});

export const getClientList = createAsyncThunk('offer/getClientList', async (data, { dispatch, rejectWithValue }) => {
  try {
    const response = await getRequest(getClientListApiUrl, data, true, dispatch);
    if (response) {
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        return data?.clients; // Return the supplier list data
      } else {
        return rejectWithValue(message || 'Failed to fetch supplier list');
      }
    }
  } catch (error) {
    return rejectWithValue('An error occurred while fetching supplier list');
  }
});
export const duplicateOffer = createAsyncThunk('offer/duplicateOffer', async (payload, { dispatch, rejectWithValue }) => {
  try {
    const response = await patchRequest(duplicateOfferApiUrl, payload, true, dispatch);
    if (response) {
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        return { ...get(response, 'data'), offerId: get(payload, 'offerId') }; // Return the supplier list data
      } else {
        return rejectWithValue(message || 'Failed to fetch supplier list');
      }
    }
  } catch (error) {
    return rejectWithValue('An error occurred while fetching supplier list');
  }
});

export const generateOfferAction = createAsyncThunk(generateOffer, async (data, { dispatch }) => {
  try {
    dispatch(offerSlice?.actions?.setLoading(true));
    const response = await post(generateOffer, data, true, dispatch);
    if (response) {
      dispatch(offerSlice?.actions?.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(notification(success, message, error));
        window.location.href = myOfferPageUrl;
        return data;
      }
      if (!success) {
        dispatch(notification(false, message || response?.message || '', true));
        return [];
      }
    } else {
      dispatch(offerSlice?.actions?.setLoading(false));
    }
  } catch (error) {
    dispatch(offerSlice?.actions?.setLoading(false));
    return [];
  }
});

export const sendQuoteRequest = createAsyncThunk(sendQuoteRequestApiUrl, async (data, { dispatch }) => {
  const { rfqID } = data || {};
  try {
    dispatch(offerSlice.actions.setLoading(true));
    const response = await post(sendQuoteRequestApiUrl, data, true, dispatch);
    if (response) {
      dispatch(offerSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(offerSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(offerSlice.actions.setLoading(false));
    return { succes: false };
  }
});

export const requestPriceAction = createAsyncThunk(requestPriceApiUrl, async (data, { dispatch }) => {
  const { rfqID } = data || {};
  try {
    dispatch(offerSlice.actions.setLoading(true));
    const response = await post(requestPriceApiUrl, data, true, dispatch);
    if (response) {
      dispatch(offerSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(offerSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(offerSlice.actions.setLoading(false));
    return { succes: false };
  }
});

export const useQuoteAction = createAsyncThunk(useQuoteApiUrl, async (data, { dispatch }) => {
  const { RFQ_ID, ...rest } = data || {};
  let payload = { ...rest };
  try {
    dispatch(offerSlice.actions.setLoading(true));
    const response = await post(useQuoteApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(offerSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        dispatch(getOfferDetail(RFQ_ID));
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(offerSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(offerSlice.actions.setLoading(false));
    return { succes: false };
  }
});

export const calculateQuoteAction = createAsyncThunk(calculateQuoteApiUrl, async (data, { dispatch }) => {
  let rfqId = data?.rfqId;
  const { isGeneratedOfferPage, ...rest } = data || {};
  let payload = { ...rest };
  try {
    dispatch(offerSlice.actions.setLoading(true));
    const response = await post(calculateQuoteApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(offerSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        if (!isGeneratedOfferPage) {
          dispatch(getOfferDetail(rfqId));
        }
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(offerSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(offerSlice.actions.setLoading(false));
    return { succes: false };
  }
});

export const deleteQuoteAction = createAsyncThunk('deleteQuote/delete', async (data, { dispatch }) => {
  let rfqId = data?.rfqId;
  let quoteId = data?.quoteId;
  try {
    dispatch(offerSlice.actions.setLoading(true));
    const response = await deleletRequest(deleteQuoteApiUrl(quoteId), quoteId, true, dispatch);
    if (response) {
      dispatch(offerSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        dispatch(getOfferDetail(rfqId));
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(offerSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(offerSlice.actions.setLoading(false));
    return { succes: false };
  }
});

export const updateQuoteStatusAction = createAsyncThunk(updateQuoteStatusApiUrl, async (data, { dispatch }) => {
  const { rfqId, quoteId, isOffered } = data || {};
  try {
    dispatch(offerSlice.actions.setLoading(true));
    const response = await post(updateQuoteStatusApiUrl, { quoteId, isOffered }, true, dispatch);
    if (response) {
      dispatch(offerSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        dispatch(getOfferDetail(rfqId));
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(offerSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(offerSlice.actions.setLoading(false));
    return { succes: false };
  }
});

export const calculateMarginAction = createAsyncThunk('calculateMarginAction', async (data, { dispatch }) => {
  const { setLoading, setCalculatedMargin } = offerSlice?.actions || {};
  dispatch(setLoading(true));
  try {
    const response = await post(calculateMarginApiUrl, data, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        dispatch(setCalculatedMargin(data));
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        dispatch(setCalculatedMargin(''));
        return { success: false, data: '' };
      }
    } else {
      dispatch(setLoading(false));
      dispatch(setCalculatedMargin(''));
      return { success: false, data: '' };
    }
  } catch (error) {
    dispatch(setLoading(false));
    dispatch(setCalculatedMargin(''));
    return { succes: false, data: '' };
  }
});

const offerSlice = createSlice({
  name: 'offer',
  initialState: {
    data: [],
    status: 'idle',
    supplierList: [],
    requestedQuote: [],
    useQuoteList: [],
    error: null,
    duplicateResponseData: null,
    loading: false,
    isDuplicateOfferLoading: false
  },
  reducers: {
    addSupplierAction: (state, action) => {
      state.data = action.payload;
    },
    selectedOfferAction: (state, action) => {
      state.selectedOffer = action.payload;
    },
    setSupplierListAction: (state, action) => {
      state.supplierList = action.payload;
    },
    storeOfferDetail: (state, action) => {
      state.allquote = action.payload;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setRequestedQuoteList: (state, action) => {
      state.requestedQuote = action.payload;
    },
    setUseQuoteList: (state, action) => {
      state.useQuoteList = action.payload;
    },
    setCalculatedMargin: (state, action) => {
      state.calculatedMargin = action.payload;
    },
    removeClientFromList: (state, action) => {
      state.clientList = state.clientList?.filter(client => client.ClientID !== action.payload);
    }
  },

  extraReducers: (builder) => {
    builder
      .addCase(getOfferDetail.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getOfferDetail.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload;
      })
      .addCase(getOfferDetail.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload || 'Failed to fetch RFQ data';
      })
      .addCase(getSupplierList.pending, (state) => {})
      .addCase(getSupplierList.fulfilled, (state, action) => {
        state.supplierList = action.payload.data || []; // Update supplierList with the fetched data
      })
      .addCase(getSupplierList.rejected, (state, action) => {
        state.error = action.payload || 'Failed to fetch supplier list';
      })
      .addCase(duplicateOffer.pending, (state) => {
        state.isDuplicateOfferLoading = true;
      })
      .addCase(duplicateOffer.fulfilled, (state, action) => {
        state.isDuplicateOfferLoading = false;
        state.duplicateResponseData = action.payload;
      })
      .addCase(duplicateOffer.rejected, (state, action) => {
        state.isDuplicateOfferLoading = false;
      })
      .addCase(getClientList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getClientList.fulfilled, (state, action) => {
        state.loading = false;
        state.clientList = action.payload;
      })
      .addCase(getClientList.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch supplier list';
      });
  }
});

export const {
  addSupplierAction,
  selectedOfferAction,
  setCalculatedMargin,
  storeOfferDetail,
  setUseQuoteList,
  setLoading,
  setRequestedQuoteList,
  setSupplierListAction,
  removeClientFromList
} = offerSlice.actions;
export default offerSlice.reducer;
