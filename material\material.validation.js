const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
} = require("../utils/validator.util");

const confirmMaterialSchema = Joi.object({
  rfqId: stringValidation,
  materialId: stringValidation,
  partNumber: stringValidation,
  brand: stringValidation,
  description: stringValidation.allow("").optional(),
});

const searchMaterialSchema = Joi.object({
  materialId: stringValidation.allow("").optional(),
  partNumber: stringValidation,
  isSimilar: booleanValidation.optional(),
});

const addMaterialSchema = Joi.object({
  // materialId: stringValidation,
  rfqId: stringValidation,
  partNumber: stringValidation,
  manufacturer: stringValidation,
  productName: stringValidation,
  description: stringValidation.allow("").optional(),
}).unknown();

const editMaterialSchema = Joi.object({
  materialId: stringValidation,
  rfqId: stringValidation,
  partNumber: stringValidation,
  manufacturer: stringValidation,
  productName: stringValidation,
  description: stringValidation.allow("").optional(),
}).unknown();

const materialHistorySchema = Joi.object({
  id: stringValidation,
});

const specsSheetSchema = Joi.object({
  partNumber: stringValidation,
});

module.exports = {
  confirmMaterialSchema,
  searchMaterialSchema,
  addMaterialSchema,
  materialHistorySchema,
  specsSheetSchema,
  editMaterialSchema,
};
