export const DEADLINE_DESC = 'deadlineDESC';
export const DEADLINE_ASC = 'deadlineASC';
export const RFQDATE_DESC = 'rfqDateDESC';
export const RFQDATE_ASC = 'rfqDateASC';
export const RFQNumber_DESC = 'rfqNumberDESC';
export const RFQNumber_ASC = 'rfqNumberASC';
export const CLIENT_DESC = 'clentDESC';
export const CLIENT_ASC = 'clientASC';
export const ASC = 'ASC';
export const DESC = 'DESC';
export const defaultSorting = {
  heading: 'sort',
  label: 'Sort by RFQ Date (Newest First)',
  value: RFQDATE_DESC,
  field: ['r.RFQ_Date'],
  type: DESC
};
export const sortingData = [
  // {
  //   heading: 'sort',
  //   label: 'None',
  //   value:'none',
  //   field: ['none'],
  //   type: ASC,
  // },
  {
    heading: 'sort',
    label: 'Sort by RFQ Date (Newest First)',
    value: RFQDATE_DESC,
    field: ['r.RFQ_Date'],
    type: DESC
  },

  {
    heading: 'sort',
    label: 'Sort by RFQ Date (Oldest First)',
    value: RFQDATE_ASC,
    field: ['r.RFQ_Date'],
    type: ASC
  },

  {
    heading: 'sort',
    label: 'Sort by Dead Line (Earliest First)',
    value: DEADLINE_ASC,
    field: ['r.Deadline'],
    type: ASC
  },

  // {
  //   heading: 'sort',
  //   label: 'Sort by Dead Line (Latest First)',
  //   value: DEADLINE_DESC,
  //   field: ['r.Deadline'],
  //   type: DESC
  // },
  {
    heading: 'sort',
    label: 'Sort by RFQ Number (Lowest First)',
    value: RFQNumber_ASC,
    field: ['r.RFQ_Number'],
    type: ASC
  },
  {
    heading: 'sort',
    label: 'Sort by RFQ Number (Highest First)',
    value: RFQNumber_DESC,
    field: ['r.RFQ_Number'],
    type: DESC
  },
  {
    heading: 'sort',
    label: 'Sort by Client Name (A to Z)',
    value: CLIENT_ASC,
    field: ['r.Company_Name'],
    type: ASC
  },
  {
    heading: 'sort',
    label: 'Sort by Client Name (Z to A)',
    value: CLIENT_DESC,
    field: ['r.Company_Name'],
    type: DESC
  }
];
