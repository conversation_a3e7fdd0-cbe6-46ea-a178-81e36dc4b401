import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import InputField from '../inputField';
import BadgeFilter from './badgeFilter';
import { Box, Button } from '@mui/material';
import SelectComponent from '../selectComponent';
import DatePickerComponent from '../dateField';
import dayjs from 'dayjs';
import DynamicAutocomplete from '../autoComplete';

export const filterFunction = (searchQuery, allRows, filters) => {
  let filtered = allRows;
  const { search, status } = searchQuery || {};

  if (filters && filters?.length > 0) {
    Object.keys(searchQuery).forEach((key) => {
      let filterObject = filters?.find((filter) => filter?.name === key);
      const searchValue = searchQuery?.[key]?.toString()?.trim();

      if (searchValue && searchValue !== 'ALL') {
        switch (filterObject?.type) {
          case 'select':
          case 'autocomplete':
          case 'tab':
            filtered = filtered.filter((data) => {
              return data[key] === searchValue;
            });
            break;

          case 'date':
            filtered = filtered.filter((data) => dayjs(data[key]).format('YYYY-MM-DD') === dayjs(searchValue).format('YYYY-MM-DD'));
            break;

          default:
            filtered = filtered.filter((data) =>
              Object.values(data).some((val) => {
                if (Array.isArray(val)) {
                  return val.some((item) =>
                    Object.values(item).some((nestedVal) => String(nestedVal).toLowerCase().includes(searchValue.toLowerCase()))
                  );
                }
                return String(val).toLowerCase().includes(searchValue.toLowerCase());
              })
            );

            break;
        }
      }
    });
  } else {
    if (search) {
      const searchStr = String(search).toLowerCase();
      filtered = filtered?.filter((data) => Object.values(data).some((val) => String(val).toLowerCase().includes(searchStr)));
    }
    // ...existing code...
    if (status && status !== 'ALL') {
      const statusStr = String(status).toLowerCase();
      filtered = filtered.filter((data) => Object.values(data).some((val) => String(val).toLowerCase() === statusStr));
    }
  }
  return filtered;
};

const TableSearch = ({ applyFilterOnKeyDown, customFilters, allRows, handleSearch, isBadgeFilter, badgeFilterData, defaultStatus }) => {
  const [searchQuery, setSearchQuery] = useState({
    search: '',
    status: 'ALL'
  });

  useEffect(() => {
    let filtered = filterFunction(searchQuery, allRows, customFilters);

    if (!applyFilterOnKeyDown) {
      handleSearch(filtered, searchQuery);
    }
  }, [searchQuery]);

  const handleInputChange = (name, value) => {
    setSearchQuery({ ...searchQuery, [name]: value });
  };

  const clearSearch = () => {
    setSearchQuery({ search: '', status: 'ALL' });
  };

  const clearInputSearch = () => {
    setSearchQuery({ ...searchQuery, search: '' });
    if (!searchQuery?.status || searchQuery?.status === 'ALL') {
      handleSearch(allRows, '');
    } else {
      const { search, status } = searchQuery || {};
      filtered = filtered.filter((data) => String(data.status).toLowerCase() === status.toLowerCase());
      handleSearch(filtered, search, status);
    }
  };

  const showClearBtn = () => {
    return (
      (isBadgeFilter && (searchQuery?.search || searchQuery?.status !== 'ALL')) ||
      customFilters?.some((filter) => {
        let filterValue = searchQuery?.[filter.name];
        return filterValue
          ? ((filter?.type === 'select' || filter?.type === 'autocomplete' || filter?.type === 'date') && filterValue) ||
              (filter?.type === 'tab' && filterValue !== 'ALL')
          : false;
      })
    );
  };

  const handleKeyDown = (e) => {
    if (applyFilterOnKeyDown && e.key === 'Enter') {
      const filtered = filterFunction(searchQuery, allRows, customFilters);
      handleSearch(filtered, searchQuery.search, searchQuery.status);
    }
  };

  const renderFilter = (filter) => {
    switch (filter?.type) {
      case 'select':
        return (
          <Box minWidth={'250px'}>
            <SelectComponent
              label={filter?.label}
              value={searchQuery?.[filter?.name] || ''}
              onChange={(e) => handleInputChange(filter?.name, e?.target?.value)} // handleInputChange(e)}
              items={filter?.items}
            />
          </Box>
        );
      case 'autocomplete':
        return (
          <Box minWidth={'250px'}>
            <DynamicAutocomplete
              options={filter?.items || []}
              label={filter?.label}
              placeholder={`Search KAM ${filter?.label?.toLowerCase() || 'option'}`}
              value={filter?.items?.find((item) => item.value === searchQuery?.[filter?.name]) || null}
              onChange={(event, newValue) => {
                const value = newValue ? newValue.value : '';
                handleInputChange(filter?.name, value);
              }}
              getOptionLabel={(option) => option?.label || ''}
              isLoading={false}
            />
          </Box>
        );
      case 'date':
        return (
          <Box ml={'12px'}>
            <DatePickerComponent
              startDateHandleChange={(date) => handleInputChange(filter.name, new Date(date))}
              startDate={searchQuery?.[filter?.name]}
              className={filter?.className}
              fullWidth
            />
          </Box>
        );
      case 'tab':
        return (
          <Box ml={'12px'}>
            <BadgeFilter
              badgeFilterData={filter?.items || []}
              handleChange={(name, value) => handleInputChange(name, value)}
              name={filter?.name}
              value={searchQuery?.[filter?.name]}
            />
          </Box>
        );
    }
  };

  return (
    <Box className="search-wrapper">
      <div className="search-container box">
        <div className="input-container searchWrapper">
          <InputField
            style={{ marginLeft: 1, flex: 1 }}
            type="text"
            placeholder="Enter your search"
            value={searchQuery?.search}
            onKeyDown={handleKeyDown}
            onChange={(e) => handleInputChange('search', e?.target?.value)}
            className="search-field"
          />
          <div className="closeIcon">
            <CloseIcon onClick={clearInputSearch} />
          </div>
        </div>
      </div>
      {isBadgeFilter && (
        <BadgeFilter
          badgeFilterData={badgeFilterData || []}
          handleChange={(name, value) => handleInputChange(name, value)}
          name="status"
          value={searchQuery?.status}
        />
      )}

      {customFilters?.length > 0 && customFilters?.map((filter) => !filter?.hideFilter && renderFilter(filter))}

      {showClearBtn() && (
        <Button color="error" variant="outlined" onClick={() => clearSearch()} sx={{ mt: '8px', ml: 3 }}>
          Clear
        </Button>
      )}
    </Box>
  );
};

TableSearch.propTypes = {
  allRows: PropTypes.array.isRequired,
  handleSearch: PropTypes.func.isRequired
};

export default TableSearch;
