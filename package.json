{"name": "rfq", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "server": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google-cloud/bigquery": "^7.7.1", "@google-cloud/storage": "^7.14.0", "animate.css": "^4.1.1", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^3.1.7", "dotenv": "^16.4.5", "express": "^4.19.2", "googleapis": "^148.0.0", "joi": "^17.13.1", "jsonwebtoken": "^9.0.2", "monday-sdk-js": "^0.5.6", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.14", "sweetalert2": "^11.12.0"}, "devDependencies": {"madge": "^7.0.0", "nodemon": "^3.1.3"}}