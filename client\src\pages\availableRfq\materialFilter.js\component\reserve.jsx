import { Button } from '@mui/material';
import { get } from 'lodash';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { getOfferDetail } from 'redux/reducers/offerReducer';
import { assignRfqAction } from 'redux/reducers/RfqReducer';
import { getTimeSlots } from 'redux/reducers/settingReducer';
import { getSingleRfq } from 'redux/reducers/singleRfq';
import { getUserDetail } from 'utils/auth';
import { IRRELEVANT, KAM, offerPageUrl, RESERVED, ReservedSingleRfqPageUrl, reserveRfqPageUrl, singleRfqPageUrl } from 'utils/constant';
import { handleReserveClick, isCountryManager } from 'utils/helper';

const ReserveBtn = ({ pageUrl, currentPage, order, variant, loading, filters, smallBtn }) => {
  const setting = useSelector((state) => state.setting);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const rfqId = useParams()?.id;
  const assignRfq = async () => {
    const { RFQ_ID } = order;
    const user = getUserDetail();
    const response = await dispatch(
      assignRfqAction({
        rfqId: RFQ_ID?.toString(),
        status: RESERVED,
        userId: user?.userId?.toString(),
        currentPage: currentPage,
        pageUrl: pageUrl,
        filters
      })
    );

    if (get(response, 'payload.success', false)) {
      if (pageUrl !== ReservedSingleRfqPageUrl && pageUrl !== offerPageUrl) {
        if (get(response, 'payload.success', false)) {
          navigate(pageUrl || reserveRfqPageUrl);
        }
      }
      if (pageUrl === ReservedSingleRfqPageUrl) {
        if (rfqId) {
          dispatch(getSingleRfq(rfqId));
        }
      }
      if (pageUrl === offerPageUrl) {
        if (rfqId) {
          dispatch(getOfferDetail(rfqId));
        }
      }
    }
  };
  const handleClick = async () => {
    const response = await handleReserveClick(dispatch, order?.isManuallyAdded);
    if (response) {
      assignRfq();
    }
  };

  return (
    <>
      {' '}
      {!isCountryManager() && (
        <Button
          variant={variant}
          size="small"
          className={`material-request-btn ${smallBtn && 'small-btn'}`}
          onClick={handleClick}
          disabled={loading}
        >
          Reserve
        </Button>
      )}
    </>
  );
};
export default ReserveBtn;
