/* eslint-disable no-unused-vars */
import { Button } from '@mui/material';
import { get } from 'lodash';
import AlertDialog from 'pages/component/dialogbox';
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { assignRfqAction, releaseRfqAction } from 'redux/reducers/RfqReducer';
import { getUserDetail } from 'utils/auth';
import { NEW, rfqListPageUrl } from 'utils/constant';
import { checkIsRfqReserved, showAlert } from 'utils/helper';
import { rfqNotReservedMessage } from 'utils/validationMessage';
import ReleaseReason from './releaseResons';
import { validation } from './validation';

const Release = ({ order, variant, loading }) => {
  const [open, setOpen] = useState(false);
  const dispatch = useDispatch();
  const rfqId = useParams()?.id;
  const navigate = useNavigate();
  const [selectedReason, setSelectedReasons] = useState({});

  const reasons = [
    { label: 'No part number available', value: 'No part number available' },
    { label: 'Part number discontinued', value: 'Part number discontinued' },
    { label: 'Suppliers don’t have stock available', value: 'Suppliers don’t have stock available' },
    { label: 'Suppliers cannot meet requested delivery time', value: 'Suppliers cannot meet requested delivery time' },
    { label: 'Suppliers did not provide quote within available time', value: 'Suppliers did not provide quote within available time' },
    { label: 'RFQ cancelled by client', value: 'RFQ cancelled by client' },
    { label: 'Reserved by mistake', value: 'Reserved by mistake' },
    { label: 'Missing Information', value: 'Missing Information' },
    { label: 'Not Complying with Remiex Policies', value: 'Not Complying with Remiex Policies' },
    { label: 'Order Amount Below Minimum', value: 'Order Amount Below Minimum' },
  ];

  const assignRfq = async () => {
    // if (checkIsRfqReserved(order?.CurrentStatus)) {
    const result = validation(selectedReason);
    if (result?.isValid) {
      const { reason, returnToPool } = selectedReason || {};
      const user = getUserDetail();
      const response = await dispatch(
        releaseRfqAction({
          rfqId: rfqId?.toString(),
          changeStatusToNew: returnToPool || false,
          reason: reason
        })
      );
      const { success } = get(response, 'payload', {});

      if (success) {
        navigate(rfqListPageUrl);
      }
    } else {
      showAlert(dispatch, false, result?.message, true);
    }

    // } else {
    //   showAlert(dispatch, false, rfqNotReservedMessage, true);
    // }
  };

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);

    setSelectedReasons({ ...selectedReason, reason: '', returnToPool: false });
  };

  const handleChange = (name, value) => {
    setSelectedReasons({ ...selectedReason, [name]: value });
  };

  return (
    <>
      {' '}
      <AlertDialog
        confirm={() => assignRfq()}
        cancel={() => handleClose()}
        showCard={true}
        borderRadius="20px"
        showCancelBtn={true}
        showConfirmBtn={true}
        Component={
          <ReleaseReason
            reasons={reasons}
            handleChange={(name, value) => handleChange(name, value)}
            selectedReason={selectedReason?.reason}
            returnToPool={selectedReason?.returnToPool}
          />
        }
        open={open}
      />
      <Button variant={variant} size="small" className="material-request-btn" onClick={() => handleOpen()} disabled={loading}>
        Release
      </Button>
    </>
  );
};
export default Release;
