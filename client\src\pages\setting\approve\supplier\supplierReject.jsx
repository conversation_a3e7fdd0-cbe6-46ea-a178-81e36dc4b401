import React, { useEffect, useState } from 'react';
import { Typo<PERSON>, Button, Container, Box, Paper, Divider } from '@mui/material';
import { useParams } from 'react-router-dom';
import Loader from 'components/Loader';
import { useDispatch, useSelector } from 'react-redux';
import { Person, LocationOn, Business, AccountCircle, CalendarToday } from '@mui/icons-material';
import TextAreaComponent from 'pages/component/textArea';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import './supplierApprover.css';
import SupplierDetailTable from './supplierDetailTable';
import { getSingleSupplier, updateSupplierApprovalAction } from 'redux/reducers/supplierMaintenance';
import { showAlert } from 'utils/helper';
import ThankyouMessage from '../thankyou';
import { APPROVED, REJECTED } from 'utils/constant';
import Header from '../header';
import LanguageIcon from '@mui/icons-material/Language';
import EmailIcon from '@mui/icons-material/Email';


const SupplierRejectionPage = () => {
  const { id: supplierId, token } = useParams();
  const supplierData = useSelector((state) => state.supplier);
  const [reason, setReason] = useState('');
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getSingleSupplier(supplierId));
  }, [supplierId]);

  const handleReject = async () => {
    if (reason.trim()) {
      dispatch(updateSupplierApprovalAction({ ID: supplierId, token, status: REJECTED, reason }))
    } else {
      showAlert(dispatch, false, 'Reason is required', true)
    }
  };

  const fields = [
    { label: 'Name', key: 'Name', icon: <Person /> },
    { label: 'Country', key: 'Country', icon: <LocationOn /> },
    { label: 'Shipping', key: 'Shipping', icon: <LocationOn /> },
    { label: 'Email', key: 'Email', icon: <EmailIcon /> },
    { label: 'Web', key: 'Web', type: 'link', icon: <LanguageIcon /> },
    { label: 'Created By', key: 'Created_By', icon: <AccountCircle /> },
    { label: 'Updated By', key: 'Updated_By_Name',show: 'Updated_By_Name', icon: <AccountCircle /> },
    { label: 'Created Date', key: 'Created_At', type: 'date', icon: <CalendarToday /> },
    { label: 'Updated Date', key: 'Updated_At', show: 'Updated_By_Name', type: 'date', icon: <CalendarToday /> },

  ];

  const { loading, singleSupplier } = supplierData || {}
  const { Status, approverName, lastUpdatedStatus } = singleSupplier || {}

  if (Status === REJECTED || Status === APPROVED) {
    return <ThankyouMessage status={lastUpdatedStatus} name="Supplier" />
  }

  return (<>
    <Header approverName={approverName || ''} />
    <Container maxWidth="md" className="container">
      {loading && <Loader />}
      <Box textAlign="center">
        <CancelOutlinedIcon className="icon" color="error" />

        <Typography variant="h4" gutterBottom color="primary" className="title">
          Reject Supplier
        </Typography>

        <Divider sx={{ my: 2, bgcolor: 'secondary.main', height: 2, borderRadius: 1 }} />
        <Typography variant="body1" gutterBottom color="textSecondary" className="description">
          You are about to reject a Supplier. Please review the details below and provide a reason for the rejection.
        </Typography>

        {singleSupplier && (
          <div className="client-details">
            <Typography variant="h5" gutterBottom color="secondary" className="clientDetailsTitle">
              Supplier Details
            </Typography>
            <SupplierDetailTable fields={fields} supplierData={singleSupplier} />
          </div>
        )}

        <TextAreaComponent
          id="rejection-reason"
          placeholder="Enter reason for rejection"
          value={reason}
          name="rejectionReason"
          onChange={(name, e) => setReason(e.target.value)}
          autoComplete="off"
          minRows={4}
          className="name-text-field textArea"
        />

        <Typography variant="body2" color="textSecondary">
          Providing a reason for rejection is mandatory. Please ensure that your reason is clear.
        </Typography>

        <Button
          variant="contained"
          color="error"
          onClick={handleReject}
          className="button"
          disabled={loading}
        >
          Confirm Rejection
        </Button>
      </Box>
    </Container>
  </>
  );
};

export default SupplierRejectionPage;
