import React from 'react';
import PropTypes from 'prop-types';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import '../availableRfq/material.css'

const Pagination = ({ currentPage, totalPages, handlePageChange }) => {
  const handlePrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  const renderPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 10;
    const halfMaxPagesToShow = Math.floor(maxPagesToShow / 2);

    if (totalPages <= maxPagesToShow) {
      // Display all page numbers if totalPages is less than or equal to maxPagesToShow
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(
          <button key={i} onClick={() => handlePageChange(i)} className={`page-number ${currentPage === i ? 'active' : ''}`}>
            {i}
          </button>
        );
      }
    } else {
      // Show ellipsis and handle more than maxPagesToShow pages
      let startPage = Math.max(currentPage - halfMaxPagesToShow, 1);
      let endPage = Math.min(currentPage + halfMaxPagesToShow, totalPages);

      if (startPage === 1) {
        endPage = maxPagesToShow;
      } else if (endPage === totalPages) {
        startPage = totalPages - maxPagesToShow + 1;
      }

      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(
          <button key={i} onClick={() => handlePageChange(i)} className={`page-number ${currentPage === i ? 'active' : ''}`}style={{ marginLeft:'10px' }}>
            {i}
          </button>
        );
      }

      if (startPage > 1) {
        pageNumbers.unshift(
          <button key={1} onClick={() => handlePageChange(1)} className="page-number">
            1
          </button>
        );
        if (startPage > 2) {
          pageNumbers.splice(1, 0, <span key="start-ellipsis">...</span>);
        }
      }

      if (endPage < totalPages) {
        pageNumbers.push(
          <button key={totalPages} onClick={() => handlePageChange(totalPages)} className="page-number" style={{ marginLeft:'10px' }}>
            {totalPages}
          </button>
        );
        if (endPage < totalPages - 1) {
          pageNumbers.splice(-1, 0, <span key="end-ellipsis">...</span>);
        }
      }
    }

    return pageNumbers;
  };

  return (
    <div className="pagination-container">
      <button onClick={handlePrevPage} disabled={currentPage === 1}>
        <ChevronLeftIcon />
      </button>
      {renderPageNumbers()}
      <button onClick={handleNextPage} disabled={currentPage === totalPages}>
        <ChevronRightIcon />
      </button>
    </div>
  );
};

Pagination.propTypes = {
  currentPage: PropTypes.number.isRequired,
  totalPages: PropTypes.number.isRequired,
  handlePageChange: PropTypes.func.isRequired
};

export default Pagination;
