import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { getRequest, post } from 'utils/axios';
import {
  priceMaitenanceApiUrl,
} from 'utils/constant';
import { showAlert } from 'utils/helper';


export const updatePriceMaitenance = createAsyncThunk(priceMaitenanceApiUrl, async (data, { dispatch }) => {
  const { RequestID } = data || {}
  try {
    dispatch(priceMaitenanceSlice.actions.setLoading(true));

    const response = await post(priceMaitenanceApiUrl, data, true, dispatch);
    if (response) {
      dispatch(priceMaitenanceSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        return { success: true, data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false, data };
      }
    } else {
      dispatch(priceMaitenanceSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(priceMaitenanceSlice.actions.setLoading(false));
    return { succes: false };
  }
});


const priceMaitenanceSlice = createSlice({
  name: 'priceMaitenance',
  initialState: {
    data: [],
    status: 'idle',
    error: null,
    loading: false,
  },
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
  }
});
export const { setLoading } = priceMaitenanceSlice.actions;
export default priceMaitenanceSlice.reducer;
