import React, { useEffect, useState } from 'react';
import MainCard from 'components/MainCard';
import './setting.css';
import { getSetting } from 'redux/reducers/settingReducer';
import { useDispatch, useSelector } from 'react-redux';

import 'dayjs/locale/en-gb';
import { settingTabs } from './constant';
import DynamicTabs from 'pages/component/tabs';
import { Box, Typography } from '@mui/material';
import Loader from 'components/Loader';

const Setting = () => {
  const dispatch = useDispatch();
  const [tab, setTab] = useState('0');
  const setting = useSelector((state) => state.setting);

  useEffect(() => {
    dispatch(getSetting());
  }, []);

  const handleChange = (value) => {
    setTab(value);
  };

  const ActiveComponent = settingTabs.find((t) => t.value === tab)?.component || null;
  const isLoading = () => {
    return setting?.loading || setting?.status === 'loading';
  };

  return (
    <MainCard>
      {isLoading() && <Loader />}
      <Box className="tabs-header">
        <DynamicTabs tabs={settingTabs || []} handleChange={(name, value) => handleChange(value)} value={tab} tabClassName="tab-label" />
      </Box>
      <Box className="content-container">{ActiveComponent && <ActiveComponent />}</Box>
    </MainCard>
  );
};

export default Setting;
