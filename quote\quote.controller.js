const { api<PERSON><PERSON><PERSON>, api<PERSON>rror, apiResponse } = require("../utils/api.util");
const { CUSTOMS_TAX, HANDLING_COST, AUSTRALIA_HANDLING_COST } = require("../constants");
const {
  isEmpty,
  round,
  setDate,
  generateID,
  applyFloor,
} = require("../utils/misc.util");
const {
  DELETE_SUCCESS,
  ADD_SUCCESS,
  UPDATE_SUCCESS,
  NOT_FOUND,
  FETCH,
  CUSTOM_ERROR,
  CUSTOM_SUCCESS,
} = require("../utils/message.util");
const { mainDataset, bigQueryClient, scrapedDataset } = require("../db");

const {
  DATASET_ID_MAIN,
  DATASET_ID_SCRAPE,
  DATASET_ID_AUTOMATION,
} = require("../constants");

const getQuote = apiHandler(async (req, res) => {
  const { RFQ_ID } = req.params;
  let existingRFQ;
  try {
    [existingRFQ] = await bigQueryClient.query({
      query: `
      WITH quote_data AS (
    SELECT
      q.MaterialID,
      ARRAY_AGG(
        STRUCT(
          q.QuoteID,
          q.RFQID AS RFQID,
          q.MaterialID AS MaterialID,
          q.SupplierID AS supplierID,
          s.Name AS supplierName,
          q.QuoteDate,
          q.UnitPrice AS unitPrice,
          q.UnitCurrency,
          q.Quantity AS quantity,
          q.ShippingCost,
          q.Tax,
          q.TotalCost,
          q.OfferedPrice,
          CASE 
              WHEN q.Quantity = 0 THEN NULL 
              ELSE ROUND(q.OfferedPrice / q.Quantity, 2) 
          END AS unitOfferPrice, 
          q.DeliveryDate,
          'REGISTERED' AS status,
          q.KAM,
          q.Offered,
          q.Notes AS notes,
          q.LeadTime,
          q.Weight
        )
        ORDER BY s.Name 
      ) AS supplier_json_array
    FROM
      ${DATASET_ID_MAIN}.Quotes AS q
    LEFT JOIN ${DATASET_ID_MAIN}.Suppliers AS s 
    ON q.SupplierID = s.SupplierID
    GROUP BY
      q.MaterialID
  ),
  material_data AS (
    SELECT
      r.RFQ_ID,
      r.RFQ_Number,
      r.RFQ_Name,
      r.RFQ_Date,
      r.Delivery_Date,
      r.Deadline,
      ar.RFQID AS RFQID_2,
      ar.Status,
      ar.logMessage,
      ar.logDate,
      r.Portal,
      r.URL,
      r.Company_Name,
      r.Created_From_RFQ_ID,
      c.ClientID,
      c.Name,
      u.FirstName,
      u.LastName,
      u.UserID,
      u.Email,
      rs.CurrentStatus,
      m.Material_ID AS MaterialID,
      m.RFQ_ID AS RFQID,
      pc.ConfirmedPartNumber AS Part_Number,
      m.Quantity_Required AS Quantity_Required,
      m.Material_Description AS Material_Description,
      mw.WeightInKilos AS prepopulatedWeight,
      m.showSuppliers,
      m.orders,
      pc.ConfirmedBrand AS brand,
      cr.ID AS centralizedRequestID,
      (
        SELECT supplier_json_array
        FROM quote_data
        WHERE quote_data.MaterialID = m.Material_ID
      ) AS supplier_json_array
    FROM
      ${DATASET_ID_SCRAPE}.RFQ AS r
    LEFT JOIN ${DATASET_ID_SCRAPE}.Material AS m
      ON m.RFQ_ID = r.RFQ_ID
    LEFT JOIN ${DATASET_ID_MAIN}.Predictions_Confirmations AS pc
      ON pc.MaterialID = m.Material_ID AND pc.RFQID = r.RFQ_ID
    LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status AS rs
      ON r.RFQ_ID = rs.RFQID
    LEFT JOIN ${DATASET_ID_MAIN}.Users AS u
      ON u.UserID = rs.AssignedUser
    LEFT JOIN ${DATASET_ID_MAIN}.Clients AS c
      ON UPPER(TRIM(r.Company_Name)) = UPPER(TRIM(c.Name)) 
         OR UPPER(TRIM(r.ClientID)) = UPPER(TRIM(c.ClientID))
    LEFT JOIN ${DATASET_ID_MAIN}.Centralized_Requests AS cr
      ON TRIM(cr.PartNumber) = TRIM(pc.ConfirmedPartNumber) 
         AND TRIM(UPPER(cr.Brand)) = TRIM(UPPER(pc.ConfirmedBrand))
    LEFT JOIN ${DATASET_ID_AUTOMATION}.automated_RFQ_logs AS ar
      ON ar.RFQID = r.RFQ_ID
    LEFT JOIN (
      SELECT MaterialID, WeightInKilos, Timestamp
      FROM ${DATASET_ID_AUTOMATION}.material_weight
      QUALIFY ROW_NUMBER() OVER (PARTITION BY MaterialID ORDER BY Timestamp DESC) = 1
    ) AS mw
      ON mw.MaterialID = m.Material_ID
    WHERE
      r.RFQ_ID = @rfqId
  ),
  final_result AS (
    SELECT
      RFQ_ID,
      RFQ_Number,
      RFQ_Name,
      RFQ_Date,
      Delivery_Date,
      Deadline,
      Portal,
      URL,
      Company_Name,
      RFQID_2,
      Status,
      logMessage,
      logDate,
      ClientID,
      Name,
      FirstName,
      LastName,
      Email,
      UserID,
      CurrentStatus,
      ARRAY_AGG(
        STRUCT(
          MaterialID AS Material_ID,
          RFQID AS RFQ_ID,
          Part_Number,
          Quantity_Required,
          prepopulatedWeight,
          Material_Description,
          brand,
          showSuppliers,
          orders,
          centralizedRequestID,
          supplier_json_array AS suppliers
        )
      ) AS material_json_array
    FROM
      material_data
    GROUP BY
      RFQ_ID,
      RFQ_Number,
      RFQ_Name,
      RFQ_Date,
      Delivery_Date,
      Deadline,
      Portal,
      URL,
      UserID,
      Company_Name,
      ClientID,
      Name,
      FirstName,
      LastName,
      Email,
      RFQID_2,
      Status,
      logMessage,
      logDate,
      CurrentStatus
  )
  SELECT
    RFQ_ID,
    RFQ_Number,
    RFQ_Name,
    RFQ_Date,
    Delivery_Date,
    Deadline,
    Portal,
    RFQID_2,
    UserID,
    Status,
    logMessage,
    logDate,
    URL,
    Company_Name,
    ClientID,
    Name,
    FirstName,
    LastName,
    Email,
    CurrentStatus,
    material_json_array AS materials
  FROM
    final_result;
  
      `,
      params: {
        rfqId: parseInt(RFQ_ID),
      },
    });
  } catch (error) {
    return apiError(
      CUSTOM_ERROR,
      `Quote could not be fetched from database, please check for data integrity issues : ${error}`,
      null,
      res
    );
  }

  if (isEmpty(existingRFQ[0])) {
    return apiError(NOT_FOUND, "RFQ", null, res);
  }
  const RFQ = existingRFQ[0];
  // delete RFQ.materials;

  let materials = [];
  let materialIds = [];

  for (const material of existingRFQ[0].materials) {
    let suppliers = [];
    let supplierIds = [];
    material.suppliers = material.suppliers.filter(
      (sup) => sup.RFQID === parseInt(RFQ_ID)
    );
    for (const supplier of material.suppliers) {
      if (!supplierIds.includes(supplier.QuoteID)) {
        suppliers.push(supplier);
        supplierIds.push(supplier.QuoteID);
      }
    }
    if (!materialIds.includes(material.Material_ID)) {
      !isEmpty(material.Part_Number) ? materials.push(material) : null;
      materialIds.push(material.Material_ID);
    }
    materials.suppliers = suppliers;
  }
  RFQ.materials = materials;

  if (isEmpty(RFQ.materials)) {
    return apiError(
      CUSTOM_ERROR,
      "Please Quote the RFQ with Confirmed Materials",
      null,
      res
    );
  }

  const brands = RFQ.materials.map((material) =>
    material.brand.toUpperCase().trim()
  );
  const partNumbers = RFQ.materials.map((material) =>
    material.Part_Number.trim()
  );

  const brandsString = brands.map((brand) => `"${brand}"`).join(", ");
  const partNumbersString = partNumbers
    .map((partNumber) => `"${partNumber}"`)
    .join(", ");

  let query = `
  SELECT sb.SupplierID as supplierID, supplier.name, sb.brand, pn.manufacturer_catalog_number as Part_Number, pn.manufacturer_name, sp.*, sr.materialRequested, sr.status as statusOfRequest, sp.currency as UnitCurrency
  FROM ${DATASET_ID_MAIN}.Suppliers_Brand as sb
  LEFT JOIN ${DATASET_ID_MAIN}.Suppliers as supplier ON supplier.SupplierID = sb.SupplierID
  LEFT JOIN ${DATASET_ID_MAIN}.partnumbers as pn ON TRIM(UPPER(sb.Brand)) = TRIM(UPPER(pn.manufacturer_name))
  LEFT JOIN ${DATASET_ID_MAIN}.supplier_price as sp ON CAST(sp.supplierID as string) = CAST(sb.supplierID as string) AND TRIM(UPPER(sp.brand)) = TRIM(UPPER(sb.Brand)) AND TRIM(sp.partNumber) = TRIM(pn.manufacturer_catalog_number)
  LEFT JOIN ${DATASET_ID_MAIN}.Supplier_Requests as sr ON CAST(sr.supplierID as string) = CAST(sb.supplierID as string)
  WHERE TRIM(UPPER(sb.Brand)) IN (${brandsString})
  AND TRIM(pn.manufacturer_catalog_number) IN (${partNumbersString})
  `;

  const [quoteSuppliers] = await bigQueryClient.query({
    query: query,
  });

  let [bulkSuppliers] = await mainDataset.query({
    query: `SELECT CAST(bsp.supplierID as INT) as supplierID, supplier.name, Price as unitPrice, PartNumber as Part_Number, bsp.Date as date, bsp.Currency as UnitCurrency FROM bulk_supplier_price as bsp
    LEFT JOIN Suppliers as supplier ON CAST(supplier.SupplierID as STRING) = CAST(bsp.supplierID as STRING)
    WHERE bsp.PartNumber IN (${partNumbersString})`,
  });

  if (!isEmpty(bulkSuppliers)) {
    for (const bulkSupplier of bulkSuppliers) {
      for (const quoteSupplier of quoteSuppliers) {
        if (
          bulkSupplier.supplierID === quoteSupplier.supplierID &&
          bulkSupplier.Part_Number === quoteSupplier.Part_Number
        ) {
          const bulkSupplierDate = new Date(bulkSupplier.date.value);
          const quoteSupplierDate = new Date(quoteSupplier?.date?.value);

          if (
            bulkSupplierDate > quoteSupplierDate ||
            isEmpty(quoteSupplierDate)
          ) {
            quoteSupplier.unitPrice = bulkSupplier.unitPrice;
            quoteSupplier.date = bulkSupplier.date;
            quoteSupplier.UnitCurrency = bulkSupplier.UnitCurrency;
          }

          bulkSuppliers = bulkSuppliers.filter(
            (supplier) => supplier.supplierID !== bulkSupplier.supplierID
          );
        }
      }
    }
  }

  let allSuppliers = [];
  if (!isEmpty(bulkSuppliers)) {
    allSuppliers = quoteSuppliers.concat(bulkSuppliers);
  } else {
    allSuppliers = quoteSuppliers;
  }

  for (const material of RFQ.materials) {
    let filteredSuppliers = allSuppliers.filter(
      (supplier) => supplier.Part_Number === material.Part_Number
    );

    let showSuppliers = JSON.parse(material.showSuppliers);

    let filteredSupplierstoShow = [];
    if (!isEmpty(showSuppliers)) {
      let selectedSuppliers = filteredSuppliers.filter((supplier) =>
        showSuppliers?.includes(supplier.supplierID)
      );

      filteredSupplierstoShow = selectedSuppliers;
    }

    const filteredsupplierIDs = [];
    const filteredSuppliersList = [];

    for (const supplier of filteredSupplierstoShow) {
      if (!filteredsupplierIDs.includes(supplier.supplierID)) {
        filteredSuppliersList.push(supplier);
        filteredsupplierIDs.push(supplier.supplierID);
      }
    }

    filteredSupplierstoShow = filteredSuppliersList;

    if (!isEmpty(filteredSuppliers)) {
      material.supplierPricesFound = true;
      for (const supplier of filteredSupplierstoShow) {
        if (!isEmpty(supplier.Price)) {
          supplier.unitPrice = supplier.Price;
          supplier.date = supplier.bulk_date;
        }
        if (!supplier.unitPrice) {
          supplier.status = "NOPRICE";
        } else {
          supplier.status = "RECIEVED";
        }
      }
      if (!isEmpty(showSuppliers)) {
        material.suppliers = material?.suppliers?.concat(
          filteredSupplierstoShow
        );
      }
    } else {
      material.supplierPricesFound = false;
    }
  }

  for (const material of RFQ.materials) {
    let i = 0;
    for (const supplier of material.suppliers) {
      supplier.counter = i;
      i++;
      if (!isEmpty(supplier.materialRequested)) {
        const materialRequested = JSON.parse(supplier.materialRequested);
        supplier.materialRequested = JSON.parse(supplier.materialRequested);
        for (const existingMaterial of materialRequested) {
          if (
            // existingMaterial.rfqID === material.RFQ_ID.toString() &&
            // existingMaterial.materialID === material.Material_ID.toString() &&
            existingMaterial.partNumber === material.Part_Number.toString() &&
            existingMaterial.status !== "RESPONDED"
            // existingMaterial.brand ===
            // material.brand.toString().toUpperCase() &&
            // supplier.statusOfRequest !== "RESPONDED"
          ) {
            supplier.status = "REQUESTED";
          }
        }
      }
    }
    if (!isEmpty(material.suppliers)) {
      material.suppliers = material.suppliers.sort((a, b) => {
        return a.counter - b.counter;
      });

      // material.suppliers = material.suppliers.filter(
      //   (supplier) => !isEmpty(supplier.name)
      // );
    }
  }

  if (!isEmpty(RFQ.materials)) {
    RFQ.materials = RFQ.materials.sort((a, b) => {
      return a.orders - b.orders;
    });
  }

  const suggestedSuppliersQuery = `
  SELECT sb.SupplierID as supplierID, supplier.name, sb.brand , supplier.Is_Email_Supplier
  FROM ${DATASET_ID_MAIN}.Suppliers_Brand as sb
  LEFT JOIN ${DATASET_ID_MAIN}.Suppliers as supplier ON supplier.SupplierID = sb.SupplierID
  WHERE TRIM(UPPER(sb.Brand)) IN (${brandsString}) AND supplier.Is_Email_Supplier = TRUE
`;

  const [suggestedSuppliers] = await bigQueryClient.query({
    query: suggestedSuppliersQuery,
  });

  // Attach Suggested Suppliers to each material
  for (const material of RFQ.materials) {
    let matchingSuppliers = suggestedSuppliers
      .filter(
        (s) =>
          s.brand.toUpperCase().trim() === material.brand.toUpperCase().trim()
      )
      .map((s) => s.name);

    material.Suggested_Suppliers = matchingSuppliers.length
      ? matchingSuppliers.join(", ")
      : null; // Set null if there are no matches
  }

  return apiResponse(FETCH, "Quote", RFQ, res);
});

const updateQuoteOfferStatus = apiHandler(async (req, res) => {
  const { quoteId, isOffered } = req.body;

  const [existingQuote] = await mainDataset.query({
    query: `
      SELECT QuoteID
      FROM Quotes
      WHERE QuoteID = @quoteId
    `,
    params: {
      quoteId: parseInt(quoteId),
      isOffered,
    },
  });

  if (isEmpty(existingQuote)) {
    return apiError(NOT_FOUND, "Quote", null, res);
  }

  await mainDataset.query({
    query: `
      UPDATE Quotes
      Set Offered = @isOffered
      WHERE QuoteID = @quoteId
    `,
    params: {
      quoteId: parseInt(quoteId),
      isOffered,
    },
  });

  return apiResponse(UPDATE_SUCCESS, "Quote", null, res);
});

const calculateQuote = apiHandler(async (req, res) => {
  const {
    quoteId,
    rfqId,
    materialId,
    supplierId,
    unitPrice,
    quantity,
    weight,
    currency,
    isOffered,
    isTax,
    leadTime,
    isEdit,
    notes,
    ClientID,
  } = req.body;
  const { firstName, lastName } = req.user;
  // Fetch country tax_per
  const [existingClient] = await mainDataset.query({
    query: `
          SELECT c.Name, cn.Country, c.Region
          FROM Clients as c
          LEFT JOIN Countries as cn ON cn.CountryId = c.Country 
          WHERE ClientID = @ClientID
        `,
    params: {
      ClientID,
    },
  });
  if (isEmpty(existingClient)) {
    return apiError(NOT_FOUND, "Client", null, res);
  }

  let selectExpression = "sr.Rate as shippingRate, er.Value as exchangeRate";
  let whereExpression =
    "WHERE s.SupplierID = @supplierId AND sr.Currency = @currency AND sr.Destination = @clientRegion AND er.To = 'USD'";

  if (currency === "USD") {
    selectExpression = selectExpression.replace(
      ", er.Value as exchangeRate",
      ""
    );
    whereExpression = whereExpression.replace(" AND er.To = 'USD'", "");
  }

  const fetchQuery = `
      SELECT ${selectExpression}
      FROM Suppliers as s
      LEFT JOIN Shipping_Rates as sr ON UPPER(sr.Origin) = UPPER(s.Shipping)
      ${currency !== "USD"
      ? `LEFT JOIN Exchange_Rates as er ON er.From = @currency`
      : ``
    }
      ${whereExpression}
    `
  const [calculationRates] = await mainDataset.query({
    query: fetchQuery,
    params: {
      supplierId: parseInt(supplierId),
      currency,
      clientRegion: existingClient[0]?.Region
    },
  });
  if (isEmpty(calculationRates)) {
    return apiError(NOT_FOUND, "Exchange Rates for Supplier", null, res);
  }

  const [getAV_Tax] = await mainDataset.query({
    query: `
      SELECT tax_per as Tax
      FROM AV_Tax
      WHERE country = @clientCountry
    `,
    params: {
      clientCountry: existingClient[0]?.Country,
    },
  });

  // check if av tax not found 
  if (isEmpty(getAV_Tax[0])) {
    return apiError(
      CUSTOM_ERROR,
      "AV Tax percentage not found for client country",
      null,
      res
    );
  }

  const { shippingRate, exchangeRate } = calculationRates[0];
  const shippingCost = round(weight * shippingRate, 2);

  let unitPriceBeforeTax;
  let avTax;
  let unitPriceAfterTax;
  let totalCost;
  let estimatedOfferCost;

  if (existingClient[0].Country === "Australia") {
    unitPriceBeforeTax = round(unitPrice * 1.01, 2);
    avTax = round(isTax ? (getAV_Tax[0]?.Tax / 100) * unitPriceBeforeTax : 0, 2);
    unitPriceAfterTax = round(unitPriceBeforeTax + avTax, 2);
    const unitPriceAfterShippingCost = round(unitPriceAfterTax + shippingCost, 2);
    totalCost = round(unitPriceAfterShippingCost * quantity, 2);
    estimatedOfferCost = round(
      exchangeRate ? (totalCost / 0.7) * exchangeRate : totalCost / 0.7,
      2
    );
  } else {
    
    unitPriceBeforeTax = round(unitPrice + shippingCost, 2);
    avTax = round(isTax ? (getAV_Tax[0]?.Tax / 100) * unitPriceBeforeTax : 0, 2);
    unitPriceAfterTax = round(unitPriceBeforeTax + avTax, 2);
    totalCost = round(unitPriceAfterTax * quantity, 2);
    estimatedOfferCost = round(
      exchangeRate ? (totalCost / 0.7) * exchangeRate : totalCost / 0.7,
      2
    );
  }

  const calculationDetails = {
    shippingCost,
    unitPriceBeforeTax,
    avTax,
    unitPriceAfterTax,
    totalCost,
    estimatedOfferCost,
  };

  if (isEdit) {
    await mainDataset.query({
      query: `
        UPDATE Quotes 
        SET SupplierID = @supplierId, QuoteDate = @quoteDate, UnitPrice = @unitPrice,
            UnitCurrency = @currency, Quantity = @quantity, ShippingCost = @shippingCost,
            Tax = @tax, TotalCost = @totalCost, OfferedPrice = @offerPrice, LeadTime = @leadTime,
            DeliveryDate = @deliveryDate, KAM = @kam, Offered = @offered, Weight = @weight, Notes = @notes
        WHERE QuoteID = @quoteId 
      `,
      params: {
        quoteId: parseInt(quoteId),
        supplierId: parseInt(supplierId),
        quoteDate: setDate(),
        unitPrice,
        currency,
        quantity,
        shippingCost,
        tax: avTax,
        totalCost,
        weight,
        offerPrice: estimatedOfferCost,
        deliveryDate: setDate(leadTime),
        leadTime,
        kam: `${firstName}.${lastName}`,
        offered: isOffered,
        notes: notes || "",
      },
    });

    const updatedQuoteCalculations = {
      QuoteID: parseInt(quoteId),
      totalCost,
    };

    return apiResponse(UPDATE_SUCCESS, "Quote", updatedQuoteCalculations, res);
  } else {
    await mainDataset.query({
      query: `
        INSERT INTO Quotes (QuoteID, RFQID, MaterialID, SupplierID, QuoteDate, UnitPrice,
                            UnitCurrency, Quantity, ShippingCost, Tax, TotalCost, OfferedPrice, 
                            DeliveryDate, KAM, Offered, Notes, LeadTime, Weight)
        VALUES (@quoteId, @rfqId, @materialId, @supplierId, @quoteDate, @unitPrice,
                @currency, @quantity, @shippingCost, @tax, @totalCost, @offerPrice,
                @deliveryDate, @kam, @offered, @notes, @leadTime, @weight)
      `,
      params: {
        quoteId: generateID(),
        rfqId: parseInt(rfqId),
        materialId: parseInt(materialId),
        supplierId: parseInt(supplierId),
        quoteDate: setDate(),
        unitPrice,
        currency,
        quantity,
        shippingCost,
        tax: avTax,
        totalCost,
        weight,
        offerPrice: estimatedOfferCost,
        deliveryDate: setDate(leadTime),
        kam: `${firstName.toLowerCase()}.${lastName.toLowerCase()}`,
        offered: false,
        leadTime,
        notes: notes || "",
      },
    });

    const [showSuppliers] = await scrapedDataset.query({
      query: `
        SELECT showSuppliers FROM Material
        WHERE Material_ID = @materialId
      `,
      params: { materialId: parseInt(materialId) },
    });

    if (!isEmpty(showSuppliers[0])) {
      let existingSuppliers = JSON.parse(showSuppliers[0].showSuppliers);

      if (existingSuppliers.includes(parseInt(supplierId))) {
        existingSuppliers = existingSuppliers.filter(
          (item) => item !== parseInt(supplierId)
        );

        const existingSuppliersString = JSON.stringify(existingSuppliers);

        await scrapedDataset.query({
          query: `
          UPDATE Material SET
          showSuppliers = @existingSuppliersString
          WHERE Material_ID = @materialId
          `,

          params: {
            materialId: parseInt(materialId),
            existingSuppliersString,
          },
        });
      }
    }

    return apiResponse(ADD_SUCCESS, "Quote", null, res);
  }
});

const deleteQuote = apiHandler(async (req, res) => {
  const { id } = req.params;
  await mainDataset.query({
    query: `
      DELETE
      FROM Quotes
      WHERE QuoteID = @quoteId
    `,
    params: { quoteId: parseInt(id) },
  });

  return apiResponse(DELETE_SUCCESS, "Quote", null, res);
});

const calculateMargin = apiHandler(async (req, res) => {
  const {
    offerCurrency,
    desiredUnitPrice,
    quoteCurrency,
    unitPrice,
    supplierId,
    weight,
    isTax,
    ClientID
  } = req.body;

  console.log(req.body);
  // Fetch country tax_per
  const [existingClient] = await mainDataset.query({
    query: `
            SELECT c.Name, cn.Country, c.Region
            FROM Clients as c
            LEFT JOIN Countries as cn ON cn.CountryId = c.Country 
            WHERE ClientID = @ClientID
          `,
    params: {
      ClientID,
    },
  });

  if (isEmpty(existingClient)) {
    return apiError(NOT_FOUND, "Client's country or region", null, res);
  }
  let client_country = existingClient[0].Country;
  
  let handling_cost = client_country === "Australia" ? AUSTRALIA_HANDLING_COST :
    HANDLING_COST;

  const [rateData] = await mainDataset.query({
    query: `
      SELECT 
        er1.Value as exchangeRate,
        sr.Rate as shippingRate,
        er2.Value as usdExchangeRate
      FROM Suppliers s
      LEFT JOIN Shipping_Rates sr 
        ON UPPER(sr.Origin) = UPPER(s.Shipping) AND sr.Currency = @quoteCurrency
      LEFT JOIN Exchange_Rates er1 
        ON er1.From = @quoteCurrency AND er1.To = @offerCurrency
      LEFT JOIN Exchange_Rates er2 
        ON er2.From = "USD" AND er2.To = @offerCurrency
      WHERE s.SupplierID = @supplierId AND sr.Destination = @clientRegion
      LIMIT 1;
    `,
    params: {
      supplierId : parseInt(supplierId),
      quoteCurrency,
      offerCurrency,
      clientRegion: existingClient[0].Region,
    },
  });

  const [getAV_Tax] = await mainDataset.query({
    query: `
      SELECT tax_per as Tax
      FROM AV_Tax
      WHERE country = @clientCountry
    `,
    params: {
      clientCountry: existingClient[0].Country,
    },
  });
  
  if (isEmpty(getAV_Tax[0])) {
    return apiError(
      CUSTOM_ERROR,
      "AV Tax percentage not found for client country",
      null,
      res
    );
  }
  const shippingRate = rateData[0]?.shippingRate ?? 0;
  const usdExchangeRate = rateData[0]?.usdExchangeRate ?? 1;

  const shippingCost = round(weight * shippingRate, 2);
  const avTax = round(
    isTax ? (getAV_Tax[0]?.Tax / 100) * (unitPrice + shippingCost) : 0,
    2
  );
  const EXCHANGE_HANDLING_COST = round(handling_cost * usdExchangeRate, 2);

  const unitPriceWithHandling =
    unitPrice + shippingCost + avTax + EXCHANGE_HANDLING_COST;

  // Margin calculation in quoteCurrency
  const margin =
    ((desiredUnitPrice - unitPriceWithHandling) / desiredUnitPrice) * 100;

  const result = {
    desiredUnitPrice,
    shippingCost,
    avTax,
    handlingCost: EXCHANGE_HANDLING_COST,
    calculatedMargin: round(margin, 2),
    margin: Math.round(margin),
  };

  return apiResponse(
    CUSTOM_SUCCESS,
    "Margin calculated successfully",
    { result },
    res
  );
});

module.exports = {
  getQuote,
  updateQuoteOfferStatus,
  calculateQuote,
  deleteQuote,
  calculateMargin,
};
