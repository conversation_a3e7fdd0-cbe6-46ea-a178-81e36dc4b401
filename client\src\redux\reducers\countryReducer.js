import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { getRequest } from 'utils/axios';
import { getCountryListApiUrl, getRegionListApiUrl, getStateList } from 'utils/constant';
import { notification } from 'utils/helper';

export const getCountriesAction = createAsyncThunk('countrySlice/getCountriesAction', async (rfqId, { rejectWithValue, dispatch }) => {
  let { setCountriesAction, setLoading } = countrySlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await getRequest(getCountryListApiUrl, {}, true, dispatch);
    const { error, success, message, data } = get(response, 'data', {});

    if (success) {
      dispatch(setLoading(false));
      dispatch(setCountriesAction(data));
      return data || [];
    } else {
      dispatch(setLoading(false));
      dispatch(setCountriesAction([]));
      return rejectWithValue(message || 'Failed to fetch RFQ data');
    }
  } catch (error) {
    dispatch(setLoading(false));

    dispatch(setCountriesAction([]));
    return rejectWithValue('An error occurred while fetching RFQ data');
  }
});

export const getRegionsAction = createAsyncThunk('countrySlice/getRegionsAction', async (rfqId, { rejectWithValue, dispatch }) => {
  let { setRegionsAction, setLoading } = countrySlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await getRequest(getRegionListApiUrl, {}, true, dispatch);
    const { error, success, message, data } = get(response, 'data', {});

    if (success) {
      dispatch(setLoading(false));
      dispatch(setRegionsAction(data));
      return data || [];
    } else {
      dispatch(setLoading(false));
      dispatch(setRegionsAction([]));
      return rejectWithValue(message || 'Failed to fetch RFQ data');
    }
  } catch (error) {
    dispatch(setLoading(false));
    dispatch(setRegionsAction([]));
    return rejectWithValue('An error occurred while fetching RFQ data');
  }
});

export const getStatesAction = createAsyncThunk('countrySlice/getCountriesAction', async (countryID, { rejectWithValue, dispatch }) => {
  let { setStateAction, setLoading } = countrySlice.actions || {};
  try {
    dispatch(setLoading(true));

    const response = await getRequest(getStateList(countryID), {}, true, dispatch);
    const { error, success, message, data } = get(response, 'data', {});

    if (success) {
      dispatch(setLoading(false));

      dispatch(setStateAction(data));
      return { success: true, data: data | [] };
    } else {
      dispatch(setLoading(false));
      dispatch(setStateAction([]));
      return { success: false, data: [] };
    }
  } catch (error) {
    dispatch(setLoading(false));
    dispatch(setStateAction([]));
    return { success: false, data: [] };
  }
});

const countrySlice = createSlice({
  name: 'countrySlice',
  initialState: {
    data: [],
    loading: false,
    countries: [],
    regions: [],
    states: []
  },
  reducers: {
    setStateAction: (state, action) => {
      state.states = action.payload;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setCountriesAction: (state, action) => {
      state.countries = action.payload;
    },
    setRegionsAction: (state, action) => {
      state.regions = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getCountriesAction.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getCountriesAction.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload;
      })
      .addCase(getCountriesAction.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      });
  }
});
export const { setStateAction, setCountriesAction } = countrySlice.actions;
export default countrySlice.reducer;
