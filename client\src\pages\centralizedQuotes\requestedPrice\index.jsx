import { get } from 'lodash';
import ActionButton from 'pages/component/actionButton';
import TableComponent from 'pages/component/table/table';
import { checkIsAllBrandSelected } from '../constant';
import OpenRequest from './openRequest';
import TableApiPaginationComponent from 'pages/component/table/tableApiPagination';

const RequestedPrice = ({ setFormData, onPageChange, onPageLimitChange, formData }) => {
  const { tableData, totalCount } = get(formData, 'requestedPrice', {});
  const { page } = get(formData, 'filters', {});

  const headers = [
    {
      name: 'brand',
      type: 'text',
      title: 'Brand',
      sortingactive: true
    },
    {
      name: 'partNumber',
      type: 'text',
      title: 'Part Number',
      sortingactive: true
    },
    { name: 'RFQ_Number',
      type: 'text', 
      title: 'RFQ Number', 
      minWidth: '150px', 
      sortingactive: true 
    },
    {
      name: 'description',
      type: 'text',
      title: 'Description',
      sortingactive: true
    },
    {
      name: 'quantity',
      type: 'text',
      title: 'Qty',
      sortingactive: true
    },
    {
      name: 'kam',
      type: 'text',
      title: 'Kam',
      sortingactive: true
    },
    {
      name: 'date',
      type: 'date',
      title: 'Date',
      dateformat: true,
      sortingactive: true
    },
    {
      name: 'dueDate',
      type: 'date',
      title: 'Due Date',
      dateformat: true,
      sortingactive: true
    },
    {
      name: 'rfqId',
      type: 'text',
      title: 'RFQ ID',
      sortingactive: true
    },
    {
      name: 'requestedCount',
      type: 'text',
      title: 'Requests',
      sortingactive: true,
      defaultValue: 0
    },
    {
      name: 'respondedCount',
      type: 'text',
      title: 'Received',
      sortingactive: true,
      defaultValue: 0
    },
    {
      name: 'actions',
      btnType: 'checkbox',
      type: 'actions',
      title: 'Select',
      checkboxName: 'select',
      sortingactive: false,
      component: ActionButton,
      buttonOnClick: (type, id, materialId, index, data) => {
        const updatedData = tableData?.map((previousRequest) =>
          previousRequest?.id === data?.id ? { ...previousRequest, select: !data?.select } : previousRequest
        );
        setFormData({
          ...formData,
          requestedPrice: {
            ...formData?.requestedPrice,
            tableData: updatedData || [],
            selectedBrands: updatedData || [],
            selectAllBrand: checkIsAllBrandSelected(updatedData)
          }
        });
      }
    },
    {
      name: 'actions',
      btnType: 'infoIcon',
      type: 'actions',
      toolTipName: 'notes',
      title: 'Notes',
      sortingactive: false,
      component: ActionButton,
      buttonOnClick: (type, id, materialId, index, data) => {}
    },
    {
      name: 'actions',
      btnType: 'update',
      type: 'actions',
      buttonTitle: 'Open',
      title: 'Action',
      sortingactive: false,
      component: ActionButton,
      showButton: 'isSupplierAvailable',
      disableBtn: false,
      buttonOnClick: (type, id, materialId, index, data) => {
        let updatedRequest = tableData
          ?.find((allData) => allData?.ID === data?.ID)
          ?.filteredExistingRequests?.map((openRequest) => ({
            ...openRequest,
            date: openRequest?.requestDate || openRequest?.respondDate
          }));
        setFormData({
          ...formData,
          requestedPrice: { ...formData?.requestedPrice, open: data?.ID, openRequestData: updatedRequest || [] }
        });
      }
    }
  ];

  return (
    <>
      <TableApiPaginationComponent
        columns={headers}
        rows={tableData || []}
        title="Vovier"
        totalCount={totalCount}
        pageNo={page}
        titleLink="dashboard"
        isBackendPagination={true}
        onPageChange={(page) => onPageChange(page)}
        onPageLimitChange={(page) => onPageLimitChange(page)}
        enablePagination={true}
      />
    </>
  );
};
export default RequestedPrice;
