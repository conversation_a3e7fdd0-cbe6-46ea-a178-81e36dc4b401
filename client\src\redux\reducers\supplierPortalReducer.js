import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { deleletRequest, getRequest, post } from 'utils/axios';
import {
  fetchShippingApiUrl,
  getAllRequestedQuoteApiUrl,
  getRequestedSuppplierQuoteApiUrl,
  inviteExipiredSupplierApiUrl,
  removeRequestApiUrl,
  saveQuoteRequestApiUrl,
  singleSupplierApiUrl,
  submitQuoteRequestApiUrl
} from 'utils/constant';
import { showAlert } from 'utils/helper';

export const getSupplierRequestedQuotes = createAsyncThunk(
  'getSupplierRequestedQuotes/fetch',
  async (supplierId, { rejectWithValue, dispatch }) => {
    try {
      const response = await post(getRequestedSuppplierQuoteApiUrl(supplierId), {}, false, dispatch);
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        return data;
      } else {
        return rejectWithValue(message || 'Failed to fetch RFQ data');
      }
    } catch (error) {
      return rejectWithValue('An error occurred while fetching RFQ data');
    }
  }
);
export const getAllSuppliersRequestedQuotes = createAsyncThunk(
  'getAllSuppliersRequestedQuotes/fetch',
  async (supplierId, { rejectWithValue, dispatch }) => {
    try {
      const response = await getRequest(getAllRequestedQuoteApiUrl, {}, true, dispatch);
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(supplierPortalSlice.actions.setAllSupplierQuotes(data));
        return { success: true, data: data };
      } else {
        return rejectWithValue(message || 'Failed to fetch RFQ data');
      }
    } catch (error) {
      return rejectWithValue('An error occurred while fetching RFQ data');
    }
  }
);

export const inviteExpiredSupplier = createAsyncThunk(submitQuoteRequestApiUrl, async (data, { dispatch }) => {
  const { isRequestPage, supplierId } = data;
  try {
    dispatch(supplierPortalSlice.actions.setLoading(true));

    const response = await post(inviteExipiredSupplierApiUrl(supplierId), supplierId, true, dispatch);
    if (response) {
      dispatch(supplierPortalSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});

      if (success) {
        showAlert(dispatch, success, message, error);
        if (!isRequestPage) {
          dispatch(getAllSuppliersRequestedQuotes());
          await dispatch(getSupplierRequestedQuotes(RequestID));
        }

        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(supplierPortalSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(supplierPortalSlice.actions.setLoading(false));
    return { succes: false };
  }
});

export const submitQuoteRequest = createAsyncThunk(submitQuoteRequestApiUrl, async (data, { dispatch }) => {
  const { RequestID } = data || {};
  try {
    dispatch(supplierPortalSlice.actions.setLoading(true));

    const response = await post(submitQuoteRequestApiUrl, data, false, dispatch);
    if (response) {
      dispatch(supplierPortalSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        dispatch(getSupplierRequestedQuotes(RequestID));
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(supplierPortalSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(supplierPortalSlice.actions.setLoading(false));
    return { succes: false };
  }
});

export const saveQuoteRequest = createAsyncThunk(saveQuoteRequestApiUrl, async (data, { dispatch }) => {
  try {
    dispatch(supplierPortalSlice.actions.setLoading(true));

    const response = await post(saveQuoteRequestApiUrl, data, false, dispatch);
    if (response) {
      dispatch(supplierPortalSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        showAlert(dispatch, success, message, error);
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(supplierPortalSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(supplierPortalSlice.actions.setLoading(false));
    return { succes: false };
  }
});
export const removeRequestAction = createAsyncThunk(removeRequestApiUrl, async (data, { dispatch }) => {
  let { supplierId, ...rest } = data || {};
  let payload = { ...rest };

  try {
    dispatch(supplierPortalSlice.actions.setLoading(true));
    const response = await deleletRequest(removeRequestApiUrl, data, false, dispatch);
    if (response) {
      dispatch(supplierPortalSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(getSupplierRequestedQuotes(supplierId));
        showAlert(dispatch, success, message, error);
        return { success: true, data: data };
      } else {
        let errorMessage = message || response?.message || 'Something went wrong';
        showAlert(dispatch, success, errorMessage, true);
        return { success: false };
      }
    } else {
      dispatch(supplierPortalSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(supplierPortalSlice.actions.setLoading(false));
    return { succes: false };
  }
});

const supplierPortalSlice = createSlice({
  name: 'supplier',
  initialState: {
    data: [],
    allSupplierQuotes: [],
    status: 'idle',
    error: null,
    loading: false,
    allShipping: []
  },
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setAllSupplierQuotes: (state, action) => {
      state.allSupplierQuotes = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSupplierRequestedQuotes.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getSupplierRequestedQuotes.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload;
      })
      .addCase(getSupplierRequestedQuotes.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload || 'Failed to fetch RFQ data';
      });
  }
});
export const { setLoading } = supplierPortalSlice.actions;
export default supplierPortalSlice.reducer;
