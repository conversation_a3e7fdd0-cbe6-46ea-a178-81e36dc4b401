import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { post } from 'utils/axios';
import { historyVoiverApiUrl } from 'utils/constant';
export const getMaterialHistory = createAsyncThunk('history/fetch', async (materialId, { rejectWithValue, dispatch }) => {
  try {
    const response = await post(historyVoiverApiUrl(materialId), {}, true, dispatch);
    const { error, success, message, data } = get(response, 'data', {});

    if (success) {
      return data || [];
    } else {
      return rejectWithValue(message || 'Failed to fetch RFQ data');
    }
  } catch (error) {
    return rejectWithValue('An error occurred while fetching RFQ data');
  }
});
const materialHistory = createSlice({
  name: 'history',
  initialState: {
    data: [],
    status: 'idle',
    error: null
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getMaterialHistory.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getMaterialHistory.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload;
      })
      .addCase(getMaterialHistory.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      });
  }
});

export default materialHistory.reducer;
