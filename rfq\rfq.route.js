const router = require("express").Router();
const { validate } = require("../middlewares/validation.middleware");

const {
  getAllRFQ,
  getSingleRFQ,
  updateRFQStatus,
  discardRFQ,
  updateRFQ,
  generateRFQ,
  getPortalNames,
  getReasons,
  releaseRFQ,
  getAutomatedRFQ,
  getRFQWithConfirmedMaterials,
} = require("../rfq/rfq.controller");

const {
  getAllRFQSchema,
  getSingleRFQSchema,
  updateStatusSchema,
  updateRFQSchema,
  discardRFQSchema,
  generateRFQSchema,
  releaseRFQSchema,
} = require("../rfq/rfq.validation");

router.post("/get", validate(getAllRFQSchema, "body"), getAllRFQ);

router.post(
  "/get/automated",
  validate(getAllRFQSchema, "body"),
  getAutomatedRFQ
);

router.post(
  "/get/confirmed-materials",
  validate(getAllRFQSchema, "body"),
  getRFQWithConfirmedMaterials
);

router.post("/get/:id", validate(getSingleRFQSchema, "params"), getSingleRFQ);

router.patch(
  "/update/status",
  validate(updateStatusSchema, "body"),
  updateRFQStatus
);

router.patch("/update", validate(updateRFQSchema, "body"), updateRFQ);

router.post("/add", validate(generateRFQSchema, "body"), generateRFQ);

router.get("/get-portal", getPortalNames);

router.patch("/discard/:id", validate(discardRFQSchema, "params"), discardRFQ);

router.get("/get-reasons", getReasons);

router.patch("/release", validate(releaseRFQSchema, "body"), releaseRFQ);

router.ois;

module.exports = router;
