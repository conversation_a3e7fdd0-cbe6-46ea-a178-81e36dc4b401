import React from 'react';
import { Typography, Box, Paper, Container } from '@mui/material';
import { CheckCircleOutline, CancelOutlined } from '@mui/icons-material';
import { APPROVED } from 'utils/constant';

const ThankyouMessage = ({ status, name, color }) => {
  const isApproved = status === APPROVED;
  const Icon = isApproved ? CheckCircleOutline : CancelOutlined;

  return (
    <Container maxWidth="sm" sx={styles.container}>
      <Paper sx={styles.paper}>
        <Box sx={styles.iconBox}>
          <Icon sx={styles.icon} color={status === APPROVED ? 'success' : 'error'} />
        </Box>

        <Typography variant="h4" color="secondary" sx={styles.title}>
          {`${name} ${isApproved ? 'Approved!' : 'Rejected!'}`}
        </Typography>

        <Typography variant="body1" color="textSecondary" sx={styles.message}>
          Thank you for your response. The {name} has been successfully {isApproved ? 'approved' : 'rejected'}.
        </Typography>
      </Paper>
    </Container>
  );
};

export default ThankyouMessage;

const styles = {
  container: { mt: 8, mb: 4 },
  paper: { p: 4, textAlign: 'center' },
  iconBox: { mb: 2 },
  icon: { fontSize: 80 },
  title: { fontWeight: 'bold', mb: 2 },
  message: { mb: 4 }
};