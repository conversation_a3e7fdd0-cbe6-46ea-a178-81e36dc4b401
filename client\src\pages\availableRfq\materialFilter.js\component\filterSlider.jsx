
import { get } from 'lodash';
import SliderComponent from 'pages/component/sliderInput';
import React, { useState } from 'react';
const FilterSlider = ({ data, handleFilterChange }) => {
  return (
    <div className="radio-buttons">
      <SliderComponent
        value={get(data, 'data', '')}
        min={0}
        max={10}
        step={1}
        onChange={(value) => handleFilterChange(value)}
        showValueLabel={true}
        orientation="horizontal"
      />
    </div>
  );
};

export default FilterSlider;
