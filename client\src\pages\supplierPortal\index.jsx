import { Grid, Typography } from '@mui/material';
import { Box } from '@mui/system';
import Loader from 'components/Loader';
import MainCard from 'components/MainCard';
import './supplierPortal.css';
import SupplierPortalForm from './supplierPortalForm';
import SupplierPortalHeader from './supplierPortalHeader';
import { useDispatch, useSelector } from 'react-redux';
import { getSupplierRequestedQuotes, saveQuoteRequest, submitQuoteRequest } from 'redux/reducers/supplierPortalReducer';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { get } from 'lodash';
import { validate, validateNested } from 'pages/component/validation';
import { RESPONDEDDQUOTE } from 'pages/monitorRfq/constant';
import ThankyouPage from './thankyou';
import { REQUESTEDPRICE } from 'pages/centralizedQuotes/constant';
import { showAlert } from 'utils/helper';
const SupplierPortal = () => {
  const requestedQuote = useSelector((state) => state.requestedQuote);
  const [quote, setQuote] = useState({});
  const [errors, setErrors] = useState([]);
  const dispatch = useDispatch();
  const id = useParams()?.id;
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (id) {
      dispatch(getSupplierRequestedQuotes(id));
    }
  }, [id]);

  const handleClose = () => {
    setOpen(false);
  };
  const handleOpen = () => {
    setOpen(true);
  };

  useEffect(() => {
    let quotes = get(requestedQuote, 'data', {});

    let updatedQuotes = {
      ...quotes,

      materialRequested: quotes?.materialRequested?.map((material) => ({
        ...material,
        crossBtn: true,
        Delivery_Date: typeof material?.Delivery_Date === 'object' ? material?.Delivery_Date?.value || '' : material?.Delivery_Date || '',
        Deadline: typeof material?.Deadline === 'object' ? material?.Deadline?.value || '' : material?.Deadline || '',
        RFQ_Date: typeof material?.RFQ_Date === 'object' ? material?.RFQ_Date?.value || '' : material?.RFQ_Date || '',
        isPriceAvailable: !material?.unitPrice,
        showInput: material?.notes ? true : false
      }))
    };
    setQuote({ ...updatedQuotes });
  }, [requestedQuote?.data]);

  const handleSubmit = async (data, isIndividual) => {
    const rules = {
      unitPrice: { required: true, label: 'Unit Price', number: true },
      totalPrice: { required: true, label: 'Total Price' },
      productCondition: { required: true, label: 'Condition' },
      currency: { required: true, label: 'Currency' }
    };

    const validationErrors = validateNested(
      data.filter((item) => item?.status !== RESPONDEDDQUOTE),
      rules
    );

    const hasAtLeastOnePrice = data.some((item) => item?.status !== RESPONDEDDQUOTE && item.unitPrice > 0);
    if (!hasAtLeastOnePrice) {
      showAlert(dispatch, false, 'Please fill atleast one material.', true);
      return;
    }

    setErrors(validationErrors);

    if (validationErrors?.length === 0) {
      let updatedData = quote?.materialRequested?.map((material) => {
        const matchingDataItem = data?.find((item) => item?.materialID === material?.materialID);
        if (matchingDataItem) {
          // If a match is found, return the matching item from data
          return matchingDataItem;
        } else {
          // If no match is found, return the original material
          return material;
        }
      });

      let payload = {
        RequestID: id,
        supplierID: quote?.supplierID,

        data: updatedData?.map(({ rfqID, showInput, crossBtn, ...singleQuote }) => ({
          ...singleQuote,
          quantity: singleQuote.quantity || 0,
          unitPrice: singleQuote.unitPrice || 0,
          leadTime: Number(singleQuote?.leadTime) || 0
        }))
      };
      if (isIndividual) {
        payload = { ...payload, isIndividual: isIndividual };
      }

      const response = await dispatch(submitQuoteRequest(payload));
      const { success } = get(response, 'payload', {});
      if (success) {
        handleClose();
      }
    } else {
      handleClose();
    }
  };
  const triggerError = (validationErrors) => {
    if (validationErrors?.unitPrice) {
      showAlert(dispatch, false, validationErrors?.unitPrice, true);
    }
    if (validationErrors?.totalPrice) {
      showAlert(dispatch, false, validationErrors?.totalPrice, true);
    }
    if (validationErrors?.productCondition) {
      showAlert(dispatch, false, validationErrors?.productCondition, true);
    }
    if (validationErrors?.currency) {
      showAlert(dispatch, false, validationErrors?.currency, true);
    }
  };

  const handleSubmitPrice = async (material, index) => {
    const rules = {
      unitPrice: { required: true, label: 'Unit Price', number: true },
      totalPrice: { required: true, label: 'Total Price' },
      productCondition: { required: true, label: 'Condition' },
      currency: { required: true, label: 'Currency' }
    };
    const validationErrors = validate(material, rules);

    if (validationErrors && Object.keys(validationErrors).length === 0) {
      let payload = {
        RequestID: id,
        supplierID: quote?.supplierID,
        data: [{ ...material }]
      };
      const response = await dispatch(submitQuoteRequest(payload));
      const { success } = get(response, 'payload', {});
    } else {
      triggerError(validationErrors);
    }
  };

  const handleSave = async (data) => {
    let validationErrors = [];
    let payload = {
      RequestID: id,
      supplierID: quote?.supplierID,
      data: data?.map(({ showInput, crossBtn, unitPrice, ...singleQuote }) => {
        return {
          ...singleQuote,
          unitPrice: unitPrice || 0,
          quantity: singleQuote.quantity || 0
        };
      })
    };

    const response = await dispatch(saveQuoteRequest(payload));
    const { success } = get(response, 'payload', {});
    if (success) {
      handleClose();
    }
  };

  const isLoading = () => {
    const { loading, status } = requestedQuote || {};
    return status === 'loading' || loading;
  };

  return (
    <>
      <Grid className="supplier-portal">
        {isLoading() && <Loader />}
        <SupplierPortalHeader />
        <Box className="supplier-portal-form-wrapper">
          {quote?.status === RESPONDEDDQUOTE ? (
            <ThankyouPage />
          ) : (
            <SupplierPortalForm
              loading={isLoading()}
              quote={quote}
              quoteList={quote?.materialRequested || []}
              errors={errors}
              handleSubmitPrice={(material, index) => handleSubmitPrice(material, index)}
              onSubmit={(data, isIndividual) => handleSubmit(data, isIndividual)}
              onSave={(data) => handleSave(data)}
              handleOpen={handleOpen}
              supplierID={id}
              open={open}
              handleClose={handleClose}
            />
          )}
        </Box>
      </Grid>
    </>
  );
};
export default SupplierPortal;
