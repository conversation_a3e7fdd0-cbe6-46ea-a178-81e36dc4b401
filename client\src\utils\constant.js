export const SUPERVISER = 'SUPERVISOR';
export const COUNTRY_MANAGER = 'COUNTRY_MANAGER';
export const COUNTRY_MANAGER_LABEL = 'COUNTRY MANAGER';
export const KAM = 'KAM';
export const RESERVED = 'RESERVED';
export const NEW = 'NEW';
export const QUOTING = 'QUOTING';
export const ANSWERED = 'ANSWERED';
export const FORGOTTEN = 'FORGOTTEN';
export const WON = 'WON';
export const LOST = 'LOST';
export const EXPIRED = 'EXPIRED';
export const IRRELEVANT = 'IRRELEVANT';
export const RFQPERPAGELIMIT = 10;
export const APPROVED = 'APPROVED';
export const REJECTED = 'REJECTED';
export const PENDING = 'PENDING';
export const CurrencyList = [
  { label: 'USD', value: 'USD' },
  { label: 'EUR', value: 'EUR' },
  { label: 'GBP', value: 'GBP' },
  { label: 'CLP', value: 'CLP' },
  { label: 'AUD', value: 'AUD' }
];

export const googleLink = 'https://www.google.com/search?q=';
// ----------------------------------------------------API URL-------------------------------------------------------------------

export const loginApiUrl = 'users/login';
export const rfqApiUrl = 'rfq/get';
export const rfqConfirmedMaterialApiUrl = 'rfq/get/confirmed-materials';
export const automatedRfqApiUrl = 'rfq/get/automated';
export const assignStatusApiUrl = 'rfq/update/status';
export const singleRfqApiUrl = (rfqId) => `rfq/get/${rfqId || ''}`;
export const getCountryListApiUrl = 'clients/countries';
export const getRegionListApiUrl = 'clients/regions';
export const getStateList = (countryID) => `clients/states/${countryID}`;
export const singleSupplierApiUrl = (supplierId) => `supplier/get/${supplierId || ''}`;
export const singleQuoteApiUrl = (rfqId) => `quote/get/${rfqId || ''}`;
export const historyVoiverApiUrl = (partNumber) => `material/history/${partNumber || ''}`;
export const confirmMaterialApiUrl = 'material/confirm';
export const myOfferApiUrl = 'offer/get';
export const getMyOfferAnalyticsApiUrl = 'offer/getOfferCalculations';
export const getSpecsSheetApiUrl = (partNumber) => `material/specs-sheet/${partNumber}`;
export const getMultipleSpecsSheetApiUrl = (offerId) => `offer/specs-sheet/${offerId}`;
export const singleOfferApiUrl = (offerId) => `offer/get/${offerId}`;
export const generateOffer = 'offer/add';
export const updateOfferStatusApiUrl = 'offer/update/status';
export const saveOrderPurchaseApiUrl = 'offer/winOffer';
export const getSupplierListApiUrl = 'supplier/get';
export const getEmailSupplierListApiUrl = 'supplier/email-supplier';
export const getRequesteQuoteList = 'supplier/fetch';
export const getClientListApiUrl = 'clients/get';
export const duplicateOfferApiUrl = 'offer/copy/rfq';
export const fetchAutomationInfo = 'automation/fetch-info';
export const calculateQuoteApiUrl = 'quote/calculate';
export const calculateOfferApiUrl = 'offer/calculate';
export const searchMaterialApiUrl = 'material/search';
export const getKamApiUrl = 'users/get';
export const addMaterialApiUrl = 'material/add';
export const updateQuoteStatusApiUrl = 'quote/update/status';
export const calculateMarginApiUrl = 'quote/calculate-margin';
export const addRfqApiUrl = 'rfq/add';
export const getPortalListApiUrl = 'rfq/get-portal';
export const updateRfqApiUrl = 'rfq/update';
export const deleteQuoteApiUrl = (qoteId) => `quote/delete/${qoteId}`;
export const getSingleRfqApiUrl = (rfqId) => `rfq/get/${rfqId}`;
export const discardRfqApiUrl = (rfqId) => `rfq/discard/${rfqId}`;
export const getRequestedSuppplierQuoteApiUrl = (supplierID) => `supplier/suppliers-requests/${supplierID}`;
export const getAllRequestedQuoteApiUrl = 'supplier/all-suppliers-requests';
export const notificationApiUrl = 'users/notifications';
export const updateNotificationApiUrl = 'users/read-notifications';
export const releaseRfqApiUrl = 'rfq/release';
export const materialUpdateApiUrl = 'material/edit';
export const sendQuoteRequestApiUrl = 'supplier/request';
export const requestPriceApiUrl = 'centralizedQuoting/add-centralized-request';
export const getAllCentralizedRequestApiUrl = 'centralizedQuoting/get-all-centralized-requests';
export const getAllEmailPriceRequestApiUrl = 'centralizedQuoting/get-email-supplier-requests';
export const getEmailResponsesByRFQIDApiUrl = 'centralizedQuoting/get-supplier-replies';
export const sentBulkQuteRequesApiUrl = 'centralizedQuoting/bulk-request-supplier';
export const usePriceApiUrl = 'centralizedQuoting/use-quote';
export const addMaualPriceApiUrl = 'centralizedQuoting/addManualPrice';
export const bulkEmailPriceApiUrl = 'centralizedQuoting/bulk-request-email-supplier';
export const updatedEmailPriceReqStatusApiUrl = 'centralizedQuoting/update-email-status';
export const useQuoteApiUrl = 'supplier/use-supplier';
export const submitQuoteRequestApiUrl = 'supplier/add-price';
export const saveQuoteRequestApiUrl = 'supplier/save-price';
export const removeRequestApiUrl = 'supplier/markUnavailable';
export const priceMaitenanceApiUrl = 'supplier/import-price';
export const fetchShippingApiUrl = 'supplier/shipping';
export const updateSupplierApiUrl = 'supplier/edit';
export const addSupplierApiUrl = 'supplier/add';
export const deleteSupplierApiUrl = (supplierId) => `supplier/delete/${supplierId}`;
export const updateClientApiUrl = 'clients/edit';
export const addClientApiUrl = 'clients/add';
export const updateClientApprovalApiUrl = 'clients/update-status';
export const updateSupplierApprovalApiUrl = 'supplier/update-status';
export const deleteClientApiUrl = (clientId) => `clients/delete/${clientId}`;
export const singleClientApiUrl = (clientId) => `clients/get/${clientId || ''}`;
export const updateUserApiUrl = 'users/edit';
export const getSettingApiUrl = 'configuration/fetch-all-configurations';
export const getTimeSlotApiUrl = (userId) => `configuration/getTimeSlots/${userId}`;
export const updateTimeSettingApiUrl = 'configuration/update-all-configurations';
export const addTimeSlotSettingApiUrl = 'configuration/addTimeSlot';
export const updateApproverSettingApiUrl = (approverEmail) => `configuration/update-approver/${approverEmail}`;
export const deleteDashboardApiUrl = (dashboardId) => `configuration/deleteDashboard/${dashboardId}`;
export const changeUserPasswordApiUrl = 'users/update-password';
export const addDashboardApiUrl = 'configuration/addDashboard';
export const updateDashboardApiUrl = 'configuration/editDashboard';
export const forgotPasswordApiUrl = 'users/send-password-link';
export const resetPasswordApiUrl = 'users/recover-password';
export const addUserApiUrl = 'users/add';
export const getAllUserApiUrl = 'users/get-all-users';
export const triggerAutomationApiUrl = 'automation/triggerAutomation';
export const deleteUserApiUrl = (userId) => `users/delete/${userId}`;
export const singleUserApiUrl = (userId) => `users/get-single-user/${userId || ''}`;
export const inviteExipiredSupplierApiUrl = (supplierId) => `supplier/follow-up/supplier-price/${supplierId}`;
export const updateLegalTermsApiUrl = 'configuration/updateLegalTerms';
export const getLegalTermsApiUrl = (language) => `configuration/getLegalTerms/${language}`;

// ----------------------------------------------------Route URL-------------------------------------------------------------------
export const rfqListPageUrl = '/rfq-list';
export const singleRfqPageUrl = '/single-rfq';
export const ReservedSingleRfqPageUrl = '/reserve-single-rfq';
export const dashboardPageUrl = '/dashboard';
export const offerRfqPageUrl = '/offer';
export const updateGenerateOfferPageUrl = '/generate-offer';
export const generateOfferPageUrl = '/generate-offer';
export const viewOfferPageUrl = '/view-offer';
export const offerPageUrl = '/offer';
export const supplierUrl = '/supplier';
export const addSupplierUrl = '/add-supplier';
export const historyPageUrl = '/voiver';
export const reservedHistoryPageUrl = '/reserve-voiver';
export const myOfferPageUrl = '/my-offers';
export const emailPricesRequestsPageUrl = '/email-price-request';
export const emailResponsesPageUrl = '/email-price-response';
export const addEmailPricesRequestsPageUrl = '/add-email-price-request';
export const automatedRfqPageUrl = '/automated-rfq';
export const reserveAddEditRfq = '/reserve-addRfq';
export const availableAddEditRfq = '/available-addRfq';
export const addRfqPageUrl = '/addRfq';
export const reserveRfqPageUrl = '/reserve-rfq';
export const monitorRfqPageUrl = '/monitor-rfq';
export const supplierPageUrl = '/supplier-portal';
export const singleRequestedQuotePageUrl = '/single-supplier';
export const showAllNotificationPageUrl = '/show-all-notification';
export const dashboard1 = '/dashboard-1';
export const dashboard2 = '/dashboard-2';
export const priceMaitenancePageUrl = '/price-maintenance';
export const updateSupplierPageUrl = '/update-supplier';
export const updateClientPageUrl = '/update-client';
export const addClientPageUrl = '/add-client';
export const clientPageUrl = '/clients';
export const updateUserPageUrl = '/update-user';
export const addUserPageUrl = '/add-user';
export const userPageUrl = '/users';
export const centralizedPageUrl = '/requested-price';
export const priceTablePageUrl = '/price-table';
export const editProfilePageUrl = '/edit-profile';
export const forgotPasswordPageUrl = '/forgot-password';
export const resetPasswordPageUrl = '/change-password';
export const settingPageUrl = '/settings';
export const clientApprovePageUrl = '/client-approve';
export const clientRejectPageUrl = '/client-reject';
export const supplierApprovePageUrl = '/supplier-approve';
export const supplieRejectPageUrl = '/supplier-reject';
