const Joi = require("joi");
const {
  stringValidation,
  numberValidation,
  currencyValidation,
  conditionValidation,
  dateValidation,
  emailValidation,
  urlValidation,
  booleanValidation,
} = require("../utils/validator.util");

const addCentralizedRequestSchema = Joi.object({
  Notes: stringValidation.optional().allow(""),
  RFQ_ID: numberValidation,
  Material_ID: stringValidation,
});

const updateEmailRequestStatusSchema = Joi.object({
  id: numberValidation,
  status: stringValidation,
});

const bulkRequestSupplierSchema = Joi.object({
  supplier: numberValidation,
  materials: Joi.array()
    .required()
    .items(
      Joi.object({
        materialID: stringValidation,
        rfqID: stringValidation,
        partNumber: stringValidation,
        brand: stringValidation,
        quantity: numberValidation,
        Material_Description: stringValidation.allow("").optional(),
        RFQ_Number: stringValidation.allow("").optional(),
        RFQ_Name: stringValidation,
        // RFQ_Date: dateValidation,
        // Delivery_Date: dateValidation.allow("").optional(),
        // Deadline: dateValidation,
        Portal: stringValidation.allow("").optional(),
        URL: stringValidation.allow("").optional(),
        // FirstName: stringValidation.allow("").optional(),
        // LastName: stringValidation.allow("").optional(),
      }).unknown()
    ),
}).unknown();

const bulkRequestEmailSupplierSchema = Joi.object({
  suppliers: Joi.array().required().items(numberValidation),
}).unknown();

const useQuoteSchema = Joi.object({
  supplierID: stringValidation,
  partNumber: stringValidation,
}).unknown();

const getAllCentralizedRequestsSchema = Joi.object({
  limit: numberValidation.optional(),
  page: numberValidation.optional(),
}).unknown();

const addManualPriceSchema = Joi.object({
  suppliers: Joi.array().required().items(Joi.number().required()),
  data: Joi.object({
    status: stringValidation,
    description: stringValidation.optional().allow(""),
    partNumber: stringValidation,
    brand: stringValidation,
    quantity: numberValidation,
    unitPrice: numberValidation,
    productCondition: stringValidation.optional().allow(""),
    notes: stringValidation.optional().allow(""),
    currency: stringValidation,
    RFQ_ID: numberValidation,
    RFQ_Number: stringValidation.optional().allow(""),
    RFQ_Name: stringValidation.optional().allow(""),
    RFQ_Date: dateValidation,
    Deadline: dateValidation,
    Portal: stringValidation.optional().allow(""),
    URL: stringValidation.optional().allow(""),
    totalPrice: numberValidation,
  }).unknown(),
});

module.exports = {
  addCentralizedRequestSchema,
  bulkRequestSupplierSchema,
  updateEmailRequestStatusSchema,
  useQuoteSchema,
  getAllCentralizedRequestsSchema,
  addManualPriceSchema,
  bulkRequestEmailSupplierSchema,
};
