const jwt = require("jsonwebtoken");
const { isEmpty } = require("../utils/misc.util");
const { apiError, apiHandler } = require("../utils/api.util");
const { UNAUTHORIZED } = require("../utils/message.util");
const { JWT_SECRET } = require("../constants");
const { mainDataset } = require("../db");

const AuthGuard = apiHandler(async (req, res, next) => {
  if (
    req.url === "/login" ||
    req.url === "/add-price" ||
    req.url === "/save-price" ||
    req.url === "/send-password-link" ||
    req.url === "/recover-password" ||
    req.url === "/markUnavailable" ||
    req.url.startsWith("/suppliers-requests/") ||
    req.originalUrl.startsWith("/api/supplier/get") ||
    // req.originalUrl.startsWith("/api/clients/get") ||
    req.originalUrl.startsWith("/api/clients/update-status") ||
    req.originalUrl.startsWith("/api/supplier/update-status")
  ) {
    next();
    return;
  }

  const token = req.header("Authorization");
  if (isEmpty(token)) {
    return apiError(UNAUTHORIZED, "User", null, res);
  }

  let decodedToken;
  try {
    decodedToken = jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return apiError(UNAUTHORIZED, "User", null, res);
  }
  req.user = decodedToken;
  next();
});

module.exports = {
  AuthGuard,
};
