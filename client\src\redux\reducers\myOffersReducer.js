/* eslint-disable no-unused-vars */
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { getUserDetail } from 'utils/auth';
import { getRequest, patchRequest, post } from 'utils/axios';
import {
  getMultipleSpecsSheetApiUrl,
  getMyOfferAnalyticsApiUrl,
  getSpecsSheetApiUrl,
  myOfferApiUrl,
  saveOrderPurchaseApiUrl,
  singleOfferApiUrl,
  updateOfferStatusApiUrl
} from 'utils/constant';
import { notification } from 'utils/helper';
export const getMyOfferDetail = createAsyncThunk(myOfferApiUrl, async (data, { dispatch }) => {
  try {
    dispatch(myOfferSlice.actions.setLoading(true));
    const response = await post(myOfferApiUrl, data, true, dispatch);
    if (response) {
      dispatch(myOfferSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        return data;
      }
      if (!success) {
        return [];
      }
    } else {
      dispatch(myOfferSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(myOfferSlice.actions.setLoading(false));
    return [];
  }
});
export const getMyOfferAnalytics = createAsyncThunk(getMyOfferAnalyticsApiUrl, async (data, { dispatch }) => {
  const { setLoading, setAnalyticsData } = myOfferSlice.actions || {};
  try {
    dispatch(setLoading(true));
    const response = await getRequest(getMyOfferAnalyticsApiUrl, data, true, dispatch);
    if (response) {
      dispatch(setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(setAnalyticsData(data || {}));
        return data;
      }
      if (!success) {
        return [];
      }
    } else {
      dispatch(setLoading(false));
    }
  } catch (error) {
    dispatch(setLoading(false));
    return [];
  }
});

export const getSpecsSheetAction = createAsyncThunk(getSpecsSheetApiUrl, async (data, { dispatch }) => {
  try {
    dispatch(myOfferSlice.actions.setLoading(true));
    const response = await getRequest(getSpecsSheetApiUrl(data), data, true, dispatch);
    if (response) {
      dispatch(myOfferSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(myOfferSlice.actions.setSpecsSheet(data || []));
        return data;
      }
      if (!success) {
        dispatch(notification(false, message || data?.message || 'Some thing went wrong', true));
        return [];
      }
    } else {
      dispatch(myOfferSlice.actions.setLoading(false));
      return [];
    }
  } catch (error) {
    dispatch(myOfferSlice.actions.setLoading(false));
    return [];
  }
});

export const getSpecsSheetByOrderIdAction = createAsyncThunk(getMultipleSpecsSheetApiUrl, async (data, { dispatch }) => {
  try {
    dispatch(myOfferSlice.actions.setLoading(true));
    const response = await getRequest(getMultipleSpecsSheetApiUrl(data), data, true, dispatch);
    if (response) {
      dispatch(myOfferSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(myOfferSlice.actions.setSpecsSheet(data || []));
        return data;
      }
      if (!success) {
        // dispatch(notification(false,message||data?.message||'Some thing went wrong', true));
        return [];
      }
    } else {
      dispatch(myOfferSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(myOfferSlice.actions.setLoading(false));
    return [];
  }
});

export const getMySingleOfferDetail = createAsyncThunk(singleOfferApiUrl, async (data, { dispatch }) => {
  try {
    dispatch(myOfferSlice.actions.setLoading(true));
    const response = await getRequest(singleOfferApiUrl(data), data, true, dispatch);
    if (response) {
      dispatch(myOfferSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        dispatch(myOfferSlice.actions.setSingleOffer(data || []));
        return { success: success, data };
      }
      if (!success) {
        dispatch(notification(false, message || data?.message || 'Some thing went wrong', true));
        return { success: false, data: [] };
      }
    } else {
      dispatch(myOfferSlice.actions.setLoading(false));
      return { success: false, data: [] };
    }
  } catch (error) {
    dispatch(myOfferSlice.actions.setLoading(false));
    return { success: false, data: [] };
  }
});

export const updateOfferStatusAction = createAsyncThunk(updateOfferStatusApiUrl, async (data, { dispatch }) => {
  let { filters, ...rest } = data || {};
  let payload = { ...rest };
  try {
    dispatch(myOfferSlice.actions.setLoading(true));
    const response = await patchRequest(updateOfferStatusApiUrl, payload, true, dispatch);
    if (response) {
      dispatch(myOfferSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        let user = getUserDetail();
        let userId = get(user, 'userId', '');
        dispatch(getMyOfferDetail({ page: 1, limit: 10, userId: userId?.toString(), filterData: filters }));
        dispatch(getMyOfferAnalytics());
        dispatch(notification(success, message, error));
        return data;
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return [];
      }
    } else {
      dispatch(myOfferSlice.actions.setLoading(false));
    }
  } catch (error) {
    dispatch(myOfferSlice.actions.setLoading(false));
    return [];
  }
});

export const saveOrderPurchaseAction = createAsyncThunk(saveOrderPurchaseApiUrl, async (data, { dispatch }) => {
  let { filters, ...rest } = data || {};
  let payload = { ...rest };
  try {
    dispatch(myOfferSlice.actions.setLoading(true));
    const response = await patchRequest(saveOrderPurchaseApiUrl, payload, true, dispatch, 'formdata');
    if (response) {
      dispatch(myOfferSlice.actions.setLoading(false));
      const { error, success, message, data } = get(response, 'data', {});
      if (success) {
        let user = getUserDetail();
        let userId = get(user, 'userId', '');
        dispatch(getMyOfferDetail({ page: 1, limit: 10, userId: userId?.toString(), filterData: filters }));
        dispatch(getMyOfferAnalytics());
        dispatch(notification(success, message, error));
        return { success:true, data };
      }
      if (!success) {
        dispatch(notification(false, message, true));
        return { success: false };
      }
    } else {
      dispatch(myOfferSlice.actions.setLoading(false));
      return { success: false };
    }
  } catch (error) {
    dispatch(myOfferSlice.actions.setLoading(false));
    return { success: false };
  }
});

const myOfferSlice = createSlice({
  name: 'myOffer',
  initialState: {
    data: [],
    status: 'idle',
    error: null,
    singleOfferDetail: {},
    analyticsData: {}
  },
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setSingleOffer: (state, action) => {
      state.singleOfferDetail = action.payload;
    },
    setSpecsSheet: (state, action) => {
      state.specsSheet = action.payload;
    },
    setAnalyticsData: (state, action) => {
      state.analyticsData = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(getMyOfferDetail.pending, (state) => {
        state.status = 'loading';        
      })
      .addCase(getMyOfferDetail.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload;
      })
      .addCase(getMyOfferDetail.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })
      .addCase(getMySingleOfferDetail.pending, (state) => {
        state.singleOfferDetail = null;
      })
  }
});
export const { setLoading, setSingleOffer, setSpecsSheet } = myOfferSlice.actions;
export default myOfferSlice.reducer;
