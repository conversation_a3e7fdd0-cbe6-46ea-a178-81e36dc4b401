import React, { useEffect, useState } from 'react';
import { Modal, Box, Typography, Card, CardContent, CardMedia, CardActions, Button, Grid } from '@mui/material';
import DynamicTabs from 'pages/component/tabs';
import pdfImg from '../../../assets/images/pdfimg.png';
import { useDispatch } from 'react-redux';
import { fetchAutomationInfo } from 'utils/constant';
import { post } from 'utils/axios';
import { benchMarkTabsOption } from '../../singlrRfq/constant';
import Loader from 'components/Loader';
import { setLoading } from 'redux/reducers/offerReducer';

const Benchmark = ({ material, disableBtn }) => {
  const [openModal, setOpenModal] = useState(false);
  const [benchmarks, setBenchmarks] = useState([]);
  const [files, setFiles] = useState([]);
  const [tab, setTab] = useState('benchmark');
  const [automationInfo, setAutomationInfo] = useState([]);
  const [imgIndex, setImgIndex] = useState();
  const [isDisplayImg, setDisplayImg] = useState(false);
  const [loader, setLoader] = useState(false);
  const [info, setInfo] = useState({});
  const dispatch = useDispatch();

  useEffect(() => {
    if (tab === 'benchmark') {
      setAutomationInfo([...benchmarks]);
    } else {
      setAutomationInfo([...files]);
    }
  }, [tab, files, benchmarks]);

  useEffect(() => {
    setInfo(automationInfo[imgIndex]);
  }, [imgIndex]);

  const handleOpenImg = (index) => {
    setImgIndex(index);
    setInfo(automationInfo[index]);
    setDisplayImg(true);
  };

  const getAutomationInfo = async (materialId, partNo) => {
    let requestPayload = {
      materialID: materialId,
      partNumber: partNo?.toString()
    };

    try {
      dispatch(setLoading(true));
      const response = await post(fetchAutomationInfo, requestPayload, true, dispatch);
      const { data, files, success } = response?.data || {};
      if (response) {
        dispatch(setLoading(false));
        if (success) {
          setOpenModal(true);
          setBenchmarks([...data.benchmarks]);
          setFiles([...data.files]);
        }
      } else {
        dispatch(setLoading(false));
      }
    } catch (error) {
      dispatch(setLoading(false));
      console.error('Error fetching automation info:', error);
    }
  };

  const handleOpenModal = (material) => {
    let { Part_Number, Material_ID } = material || {};
    getAutomationInfo(Material_ID, Part_Number);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setTab('benchmark');
  };

  const handleChangeTab = (value) => {
    setTab(value);
  };

  const handleCloseImg = () => {
    setDisplayImg(false);
  };

  const handlePevious = () => {
    const index = imgIndex - 1;
    setImgIndex(index);
  };

  const handleNext = () => {
    const index = imgIndex + 1;
    setImgIndex(index);
  };

  return (
    <>
      {loader && <Loader />}
      <Button
        onClick={() => handleOpenModal(material)}
        variant="contained"
        disabled={loader || disableBtn}
        sx={{ minWidth: '150px' }}
        size="small"
      >
        Benchmark / Files
      </Button>
      <Modal open={openModal} onClose={handleCloseModal} aria-labelledby="modal-title" aria-describedby="modal-description">
        <Box sx={{ ...styles.benchMarkModal }}>
          <div style={{ display: 'flex', justifyContent: 'center', marginBottom: 30 }}>
            <DynamicTabs
              tabs={benchMarkTabsOption || []}
              handleChange={(name, value) => handleChangeTab(value)}
              value={tab}
              defaultValue="benchmark"
              tabClassName="tab-label"
            />
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 4 }}>
              <i className="fa-solid fa-xmark" style={{ fontSize: '20px', cursor: 'pointer' }} onClick={handleCloseModal}></i>
            </div>
          </div>

          <Box sx={{ overflow: 'auto', maxHeight: '70vh', padding: '10px' }}>
            {' '}
            {/* Scrollable content */}
            {automationInfo?.length > 0 ? (
              <Grid container spacing={2} columns={{ xs: 2, sm: 4, md: 8, lg: 12 }}>
                {automationInfo?.map((item, i) =>
                  tab === 'benchmark' ? (
                    <Grid item xs={2} sm={2} md={4} key={i}>
                      <Card sx={{ height: '100%' }}>
                        <CardContent>
                          <Typography gutterBottom variant="h5" color="secondary">
                            {item.SiteName}
                          </Typography>
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            Currency: {item.Currency}
                          </Typography>
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            Price: {item.Price}
                          </Typography>
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            Product Page URL:{' '}
                            <a href={item.ProductPageURL} target="_blank" rel="noopener noreferrer" className='link'>
                              Link
                            </a>
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ) : (
                    <Grid
                      item
                      xs={2}
                      sm={2}
                      md={4}
                      key={i}
                      sx={{cursor:'pointer'}}
                      onClick={() => {
                        item?.FileURL?.toLowerCase()?.endsWith('.pdf') ? window.open(item?.FileURL) : handleOpenImg(i);
                      }}
                    >
                      <Card sx={{ height: '100%' }}>
                        <Typography gutterBottom variant="h5" color="secondary" sx={{ marginLeft: '10px' }}>
                          {item.SiteName}
                        </Typography>
                        <CardMedia
                          sx={{
                            height: 170,
                            backgroundSize: 'contain',
                            objectFit: 'cover'
                          }}
                          image={item?.FileURL?.toLowerCase()?.endsWith('.pdf') ? pdfImg : item?.FileURL}
                          title={item.SiteName}
                        />
                      </Card>
                    </Grid>
                  )
                )}
              </Grid>
            ) : (
              <Typography variant="h4">{`No ${tab === 'benchmark' ? 'benchmarks' : 'files'} found`}</Typography>
            )}
          </Box>
        </Box>
      </Modal>
      <Modal open={isDisplayImg} onClose={handleCloseImg}>
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: { xs: '90%', sm: '80%' },
            maxHeight: '100%', // Constrain height to allow scrolling
            p: { xs: 2, sm: 4 }
          }}
        >
          <Card sx={{ maxWidth: '100%' }}>
            <Typography gutterBottom variant="h3" sx={{ margin: '10px 0', textAlign: 'center' }}>
              {info?.SiteName}
            </Typography>
            <i
              className="fa-solid fa-xmark"
              style={{
                fontSize: '30px',
                position: 'absolute',
                right: '15px',
                top: '15px',
                cursor: 'pointer'
              }}
              onClick={handleCloseImg}
            ></i>
            <CardMedia
              sx={{
                height: { xs: '50vh', sm: '60vh', md: '70vh' }, // Adjust height for responsiveness
                backgroundSize: 'contain'
              }}
              image={info?.FileURL?.toLowerCase()?.endsWith('.pdf') ? pdfImg : info?.FileURL}
              title={info?.SiteName}
            />
            <CardActions sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button variant="contained" onClick={handlePevious} disabled={imgIndex === 0}>
                Previous
              </Button>
              <div style={{ display: 'flex', gap: '10px' }}>
                <Button variant="contained" onClick={handleCloseImg}>
                  close
                </Button>
                <Button variant="contained" onClick={handleNext} disabled={automationInfo.length === imgIndex + 1}>
                  Next
                </Button>
              </div>
            </CardActions>
          </Card>
        </Box>
      </Modal>
    </>
  );
};

export default Benchmark;

const styles = {
  benchMarkModal: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: { xs: '90%', sm: '80%' },
    maxHeight: '90vh',
    bgcolor: 'background.paper',
    boxShadow: 24,
    p: { xs: 2, sm: 4 },
    overflow: 'auto'
  }
};
