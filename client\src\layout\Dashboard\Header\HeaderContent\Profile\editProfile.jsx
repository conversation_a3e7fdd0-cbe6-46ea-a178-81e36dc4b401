import React, { useEffect, useState } from 'react';
import { Text<PERSON><PERSON>, Button, Grid, Container, Typography, Box } from '@mui/material';
import InputField from 'pages/component/inputField';
import { validate } from 'pages/component/validation';
import { showAlert } from 'utils/helper';
import { useDispatch, useSelector } from 'react-redux';
import { getUserDetail } from 'utils/auth';
import { updateUserMaintenanace, changePasswordAction } from 'redux/reducers/userReducer'; // Make sure to import changePasswordAction
import Loader from 'components/Loader';

const EditProfile = () => {
  const [profile, setProfile] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState({});
  const [changingPassword, setChangingPassword] = useState(false);
  const userData = useSelector((state) => state.users)
  const dispatch = useDispatch();

  useEffect(() => {
    const userDetail = getUserDetail();

    if (userDetail) {
      const { firstName, lastName, email, userId ,role} = userDetail || {};

      setProfile({ userId, firstName, lastName, email,role  });
    }
  }, []);

  const updatefields = [
    { name: 'firstName', label: 'First Name', placeholder: 'Enter your first name', type: 'text' },
    { name: 'lastName', label: 'Last Name', placeholder: 'Enter your last name', type: 'text' },
    { name: 'email', label: 'Email', placeholder: 'Enter email', type: 'text', autoComplete: 'off' },
  ];

  const updatePasswordfields = [
    { name: 'oldPassword', label: 'Old Password', type: 'password', placeholder: 'Enter old password', autoComplete: 'new-password' },
    { name: 'newPassword', label: 'New Password', placeholder: 'Enter New password', type: 'password' },
  ];

  const handleChange = (name, value) => {
    setProfile((prevProfile) => ({
      ...prevProfile,
      [name]: value
    }));
  };

  const renderField = (field) => {
    const { type, name, placeholder, autoComplete, label } = field || {};
    return (<>
      <Typography variant="subtitle1" gutterBottom>{label}</Typography>
      <InputField
        type={type}
        name={name}
        placeholder={placeholder}
        value={profile[name] || ''}
        onChange={(e) => handleChange(name, e?.target?.value)}
        errors={errors}
        fullWidth
        autoComplete={autoComplete}

      />
    </>
    );
  };

  const updateProfile = () => {
    const rules = {
      firstName: { required: true, label: 'First Name' },
      lastName: { required: true, label: 'Last Name' },
      email: { required: true, type: 'email', label: 'Email' },
    };
    const validation = validate(profile, rules);
    setErrors(validation);

    if (Object.keys(validation).length === 0) {
      const { firstName, lastName, email, role, userId } = profile || {};
      const payload = {
        FirstName: firstName,
        LastName: lastName,
        Email: email,
        Role:role
      };

      if (userId) {
        dispatch(updateUserMaintenanace({ ...payload, UserID: userId?.toString() }));
      } else {
        showAlert(dispatch, false, 'User Id is required', true);
      }
    }
  };

  const updatePassword = async () => {
    const rules = {
      oldPassword: { required: changingPassword, label: 'Old Password' },
      newPassword: { required: changingPassword, label: 'New Password' },
    };
    const validation = validate(profile, rules);
    setErrors(validation);

    if (Object.keys(validation).length === 0) {
      const { oldPassword, newPassword } = profile || {};

      const payload = {
        oldPassword,
        newPassword
      };

      const response = await dispatch(changePasswordAction(payload));
      const { success } = response?.payload || {}
      
      if (success) {
        setChangingPassword(false)
      }

    }
  };
  const cancel = () => {
    setErrors({})
    setChangingPassword(false)
  }

  const isLoading = () => {
    return userData?.loading
  }

  const buttons = [
    {
      label: 'Change password',
      onClick: () => setChangingPassword(true),
      variant: 'outlined',
      color: 'primary',
      disabled: isLoading(),
      show: !changingPassword 
    },
    {
      label: 'Save',
      onClick: updateProfile,
      variant: 'contained',
      color: 'primary',
      disabled: isLoading(),
      show: !changingPassword
    },
    {
      label: 'Cancel',
      onClick: cancel,
      variant: 'outlined',
      color: 'error',
      disabled: isLoading(),
      show: changingPassword
    },
    {
      label: 'Update Password',
      onClick: updatePassword,
      variant: 'contained',
      color: 'primary',
      disabled: isLoading(),
      show: changingPassword
    }
  ];

  const fields = changingPassword ? updatePasswordfields : updatefields

  return (
    <Container maxWidth="sm">
      {isLoading() && <Loader />}
      <Box my={4}>
        <Typography variant="h4" component="h1" gutterBottom color="secondary" mb={4}>
          Update Profile
        </Typography>
        <Grid container spacing={4}>
          {fields
            ?.map((field) => (
              <Grid item xs={12} sm={field.sm} key={field.name}>
                {renderField(field)}
              </Grid>
            ))}
            
          <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
            {buttons
              ?.filter(button => button.show !== false)
              .map((button, index) => (
                <Button
                  key={index}
                  onClick={button.onClick}
                  variant={button.variant}
                  color={button.color}
                  disabled={button?.disabled}
                >
                  {button.label}
                </Button>
              ))}
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default EditProfile;
