const axios = require("axios");
const { CUSTOMS_TAX, MONDAY_TOKEN } = require("../constants");
const {
  DATASET_ID_SCRAPE,
  DATASET_ID_MAIN,
  TAX_PERCENT,
  HANDLING_COST,
  DATASET_ID_LANDINGZONE,
  DATASET_ID_AUTOMATION,
} = require("../constants");
const {
  mainDataset,
  bigQueryClient,
  scrapedDataset,
  landingZoneDataset,
  automationDataset,
  storageClient,
  authenticateGmail,
} = require("../db");
const fs = require("fs");
const mondaySdk = require("monday-sdk-js");

const isEmpty = (value) =>
  value === null ||
  value === undefined ||
  (Array.isArray(value) && value.length === 0) ||
  (typeof value === "string" && value.trim().length === 0) ||
  (typeof value === "object" &&
    Object.values(value).every((val) => isEmpty(val)));

const isEmptyV2 = (value) =>
  value === null ||
  value === undefined ||
  value === 0 || // Treat 0 or 0.0 as empty
  (Array.isArray(value) && value.length === 0) ||
  (typeof value === "string" && value.trim().length === 0) ||
  (typeof value === "object" &&
    Object.values(value).every((val) => isEmpty(val)));

// const generateID = (IDlength = 12) => {
//   const numbers = "0123456789";
//   let uid = "";
//   for (let i = 0; i < IDlength; i++) {
//     uid += numbers.charAt(Math.floor(Math.random() * numbers.length));
//   }
//   return uid;
// };

const generateID = (IDlength = 12) => {
  const numbers = "123456789";
  let uid = "";
  for (let i = 0; i < IDlength; i++) {
    uid += numbers.charAt(Math.floor(Math.random() * numbers.length));
  }
  return parseInt(uid, 10);
};

const round = (value, digits) => {
  return parseFloat(value.toFixed(digits));
};

const split = (value) => {
  let splits = 3;
  if (value.length > 3 && value.length <= 8) {
    splits = 2;
  } else if (value.length <= 3) {
    splits = 1;
  }

  const partLength = Math.ceil(value.length / splits);
  const result = [];

  for (let i = 0; i < value.length; i += partLength) {
    result.push(value.slice(i, i + partLength));
  }

  return result;
};

const setDate = (days) => {
  const date = new Date();
  if (!isEmpty(days)) {
    date.setDate(date.getDate() + days);
  }
  return date.toISOString().substring(0, 10);
};

const getCurrentDateTime = () => {
  const date = new Date();
  return date.toISOString();
};

const calculateOfferAmount = (subTotal, tax) => {
  const grandTotalBeforeTax = round(subTotal, 2);
  const taxCost = round(grandTotalBeforeTax * tax, 2);
  const grandTotalAfterTax = round(grandTotalBeforeTax + taxCost, 2);

  return {
    grandTotalBeforeTax,
    taxCost,
    grandTotalAfterTax,
  };
};

function applyFloor(obj) {
  if (Array.isArray(obj)) {
    return obj.map((element) => applyFloor(element));
  } else if (typeof obj === "object" && obj !== null) {
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        obj[key] = applyFloor(obj[key]);
      }
    }
    return obj;
  } else if (typeof obj === "number") {
    return Math.floor(obj);
  } else {
    return obj;
  }
}

const createBatches = (array, batchSize) => {
  const batches = [];
  for (let i = 0; i < array.length; i += batchSize) {
    batches.push(array.slice(i, i + batchSize));
  }
  return batches;
};

async function quoteCalculate(quote) {
  const {
    quoteId,
    supplierId,
    unitPrice,
    quantity,
    weight,
    currency,
    isTax,
    margin,
  } = quote;

  let selectExpression = "sr.Rate as shippingRate, er.Value as exchangeRate";
  let whereExpression =
    "WHERE s.SupplierID = @supplierId AND sr.Currency = @currency AND er.To = 'USD'";

  if (currency === "USD") {
    selectExpression = selectExpression.replace(
      ", er.Value as exchangeRate",
      ""
    );
    whereExpression = whereExpression.replace(" AND er.To = 'USD'", "");
  }
  const [calculationRates] = await mainDataset.query({
    query: `
      SELECT ${selectExpression}
      FROM Suppliers as s
      LEFT JOIN Shipping_Rates as sr ON UPPER(sr.Origin) = UPPER(s.Shipping)
      ${
        currency !== "USD"
          ? `LEFT JOIN Exchange_Rates as er ON er.From = @currency`
          : ``
      }
      ${whereExpression}
    `,
    params: {
      supplierId: parseInt(supplierId),
      currency,
    },
  });

  if (isEmpty(calculationRates)) {
    return apiError(NOT_FOUND, "Exchange Rates for Supplier", null, res);
  }
  const { shippingRate, exchangeRate } = calculationRates[0];

  const shippingCost = round(weight * shippingRate, 2);
  const unitPriceBeforeTax = round(unitPrice + shippingCost, 2);
  const customsTax = round(isTax ? CUSTOMS_TAX * unitPriceBeforeTax : 0, 2);
  const unitPriceAfterTax = round(unitPriceBeforeTax + customsTax, 2);
  const totalCost = round(unitPriceAfterTax * quantity, 2);
  const estimatedOfferCost = round(
    exchangeRate ? (totalCost / 0.7) * exchangeRate : totalCost / 0.7,
    2
  );

  const updatedCalculations = {
    shippingCost,
    unitPriceBeforeTax,
    customsTax,
    unitPriceAfterTax,
    totalCost,
    estimatedOfferCost,
    margin,
    unitPrice,
    quantity,
    weight,
    currency,
    isTax,
  };
  await mainDataset.query({
    query: `
      UPDATE Quotes
      SET Quantity = @quantity, TotalCost = @totalCost, OfferedPrice = @offerPrice
      WHERE QuoteID = @quoteId
    `,
    params: {
      quoteId: parseInt(quoteId),
      quantity,
      totalCost,
      offerPrice: estimatedOfferCost,
    },
  });

  return updatedCalculations;
}

async function pickAutomatedRFQ() {
  // picks automated RFQ which has not been processed yet

  const [automated_RFQ] = await automationDataset.query({
    query: `
    SELECT DISTINCT RFQID
    FROM automated_RFQ
    WHERE Status IS NULL
    `,
  });

  const RFQIDs = automated_RFQ.map((RFQ) => RFQ.RFQID);

  if (!isEmpty(RFQIDs)) {
    const [automated_RFQ_logs] = await automationDataset.query({
      query: `
      DELETE FROM automated_RFQ_logs
      WHERE RFQID IN UNNEST(@RFQIDs)
      `,
      params: {
        RFQIDs,
      },
    });
  }

  return RFQIDs;
}

async function fetchRFQInformation(RFQ_ID) {
  // picks RFQs information through RFQID

  const [existingRFQ] = await bigQueryClient.query({
    query: `
      WITH quote_data AS (
  SELECT
    q.MaterialID,
    ARRAY_AGG(
      STRUCT(
        q.QuoteID,
        q.RFQID AS RFQID,
        q.MaterialID as MaterialID,
        q.SupplierID as supplierID,
        q.QuoteDate,
        q.UnitPrice as unitPrice,
        q.UnitCurrency,
        q.Quantity as quantity,
        q.ShippingCost,
        q.Tax,
        q.TotalCost,
        q.OfferedPrice,
          CASE 
              WHEN q.Quantity = 0 THEN NULL 
              ELSE ROUND(q.OfferedPrice / q.Quantity, 2) 
          END AS unitOfferPrice, 
        q.DeliveryDate,
        'REGISTERED' AS status,
        q.KAM,
        q.Offered,
        q.Notes as notes,
        q.LeadTime,
        q.Weight
      )
    ) AS supplier_json_array
  FROM
    ${DATASET_ID_MAIN}.Quotes AS q
  GROUP BY
    q.MaterialID
),
material_data AS (
  SELECT
    r.RFQ_ID,
    r.RFQ_Number,
    r.RFQ_Name,
    r.RFQ_Date,
    r.Delivery_Date,
    r.Deadline,
    r.Portal,
    r.URL,
    r.Company_Name,
    c.ClientID,
    c.Name,
    u.FirstName,
    u.LastName,
    rs.CurrentStatus,
    m.Material_ID AS MaterialID,
    m.RFQ_ID as RFQID,
    COALESCE(pc.ConfirmedPartNumber, pc.ExtractedPartNumber) AS Part_Number,
    m.Quantity_Required AS Quantity_Required,
    m.Material_Description AS Material_Description,
    m.showSuppliers,
    m.orders,
    COALESCE(pc.ConfirmedBrand, pc.ExtractedBrand) AS brand,
    cr.ID as centralizedRequestID,
    (SELECT supplier_json_array FROM quote_data WHERE quote_data.MaterialID = m.Material_ID) AS supplier_json_array
  FROM
    ${DATASET_ID_SCRAPE}.RFQ AS r
  LEFT JOIN
    ${DATASET_ID_SCRAPE}.Material AS m
    ON m.RFQ_ID = r.RFQ_ID 
  LEFT JOIN
    ${DATASET_ID_MAIN}.Predictions_Confirmations AS pc
    ON pc.MaterialID = m.Material_ID AND pc.RFQID = r.RFQ_ID
  LEFT JOIN
    ${DATASET_ID_MAIN}.RFQ_Status AS rs
    ON r.RFQ_ID = rs.RFQID
  LEFT JOIN
    ${DATASET_ID_MAIN}.Users AS u
    ON u.UserID = rs.AssignedUser
  LEFT JOIN
    ${DATASET_ID_MAIN}.Clients AS c
    ON UPPER(TRIM(r.Company_Name)) = UPPER(TRIM(c.Name)) OR UPPER(TRIM(r.ClientID)) = UPPER(TRIM(c.ClientID))
  LEFT JOIN
    ${DATASET_ID_MAIN}.Centralized_Requests AS cr
    ON TRIM(cr.PartNumber) = TRIM(pc.ConfirmedPartNumber) AND TRIM(UPPER(cr.Brand)) = TRIM(UPPER(pc.ConfirmedBrand))
  WHERE
    r.RFQ_ID = @rfqId
),
final_result AS (
  SELECT
    RFQ_ID,
    RFQ_Number,
    RFQ_Name,
    RFQ_Date,
    Delivery_Date,
    Deadline,
    Portal,
    URL,
    Company_Name,
    ClientID,
    Name,
    FirstName,
    LastName,
    CurrentStatus,
    ARRAY_AGG(
      STRUCT(
        MaterialID as Material_ID,
        RFQID as RFQ_ID,
        Part_Number,
        Quantity_Required,
        Material_Description,
        brand,
        showSuppliers,
        orders,
        centralizedRequestID,
        supplier_json_array as suppliers
      )
    ) AS material_json_array
  FROM
    material_data
  GROUP BY
    RFQ_ID,
    RFQ_Number,
    RFQ_Name,
    RFQ_Date,
    Delivery_Date,
    Deadline,
    Portal,
    URL,
    Company_Name,
    ClientID,
    Name,
    FirstName,
    LastName,
    CurrentStatus
)
SELECT
  RFQ_ID,
  RFQ_Number,
  RFQ_Name,
  RFQ_Date,
  Delivery_Date,
  Deadline,
  Portal,
  URL,
  Company_Name,
  ClientID,
  Name,
  FirstName,
  LastName,
  CurrentStatus,
  material_json_array AS materials,
FROM
  final_result;
    `,
    params: {
      rfqId: parseInt(RFQ_ID),
    },
  });
  if (isEmpty(existingRFQ)) {
    await automationDataset.query({
      query: `
      INSERT INTO automated_RFQ_logs 
      (LogID, RFQID, Status, logMessage, logDate)
      VALUES
      (@logID, @rfqId, @status, @logMessage, @logDate)
      `,
      params: {
        logID: generateID(),
        rfqId: parseInt(RFQ_ID),
        status: "ERROR",
        logMessage:
          "RFQ information not found, Please Check RFQ table to traceback",
        logDate: getCurrentDateTime(),
      },
    });
    // Update the automated_RFQ table with a DONE status
    await automationDataset.query({
      query: `
            UPDATE automated_RFQ
            SET Status = 'ERROR'
            WHERE RFQID = @RFQID
          `,
      params: { RFQID: parseInt(RFQ_ID) },
    });

    return null;
  }

  const RFQ = existingRFQ[0];

  let materials = [];
  let materialIds = [];

  for (const material of existingRFQ[0].materials) {
    let suppliers = [];
    let supplierIds = [];
    for (const supplier of material.suppliers) {
      if (!supplierIds.includes(supplier.QuoteID)) {
        suppliers.push(supplier);
        supplierIds.push(supplier.QuoteID);
      }
    }
    if (!materialIds.includes(material.Material_ID)) {
      !isEmpty(material.Part_Number) ? materials.push(material) : null;
      materialIds.push(material.Material_ID);
    }
    materials.suppliers = suppliers;
  }
  RFQ.materials = materials;
  return RFQ;
}

async function generateRequestSupplierPayload(material, RFQInformation) {
  payload = {
    partNumber: material.Part_Number,
    brand: material.brand,
    materialID: material.Material_ID,
    quantity: material.Quantity_Required,
    description: material.Material_Description,
    rfqID: material.RFQ_ID,
    suppliers: material.suppliers,
    RFQ_ID: material.RFQ_ID,
    RFQ_Number: RFQInformation.RFQ_Number,
    URL: RFQInformation.URL,
    Portal: RFQInformation.Portal,
    Deadline: RFQInformation.Deadline,
    Delivery_Date: RFQInformation.Delivery_Date,
    RFQ_Date: RFQInformation.RFQ_Date,
    RFQ_Name: RFQInformation.RFQ_Name,
    FirstName: "REMIEX",
    LastName: "AUTOMATED",
    showSuppliers: material.showSuppliers,
  };

  return payload;
}

async function filterConfirmedMaterials(RFQInformation) {
  if (isEmpty(RFQInformation.materials)) {
    await automationDataset.query({
      query: `
      INSERT INTO automated_RFQ_logs
      (LogID, RFQID, Status, logMessage, logDate)
      VALUES (@logID, @rfqId, @status, @logMessage, @logDate)
    `,
      params: {
        logID: generateID(),
        rfqId: parseInt(RFQInformation.RFQ_ID),
        status: "ERROR",
        logMessage: `Material Information Not Found, Please check Prediction Confirmation and Materials tables to traceback`,
        logDate: getCurrentDateTime(),
      },
    });

    // Update the automated_RFQ table with a DONE status
    await automationDataset.query({
      query: `
        UPDATE automated_RFQ
        SET Status = 'ERROR'
        WHERE RFQID = @RFQID
      `,
      params: { RFQID: parseInt(RFQInformation.RFQ_ID) },
    });

    return null;
  }

  const materials = [];
  for (const material of RFQInformation.materials) {
    const partNumber = material?.Part_Number?.trim().toUpperCase();
    const brand = material?.brand?.trim().toUpperCase();

    // Skip materials with invalid partNumber or brand
    if (
      ["NA", "N/A", "NOT AVAILABLE"].includes(partNumber) ||
      ["NA", "N/A", "NOT AVAILABLE"].includes(brand)
    ) {
      continue;
    }

    // Handle Predictions_Confirmations logic
    const [confirmMaterial] = await mainDataset.query({
      query: `
        SELECT MaterialID FROM Predictions_Confirmations
        WHERE MaterialID = @materialId AND RFQID = @rfqId
      `,
      params: {
        materialId: parseInt(material.Material_ID),
        rfqId: parseInt(RFQInformation.RFQ_ID),
      },
    });

    if (isEmpty(confirmMaterial)) {
      await mainDataset.query({
        query: `
          INSERT INTO Predictions_Confirmations (MaterialID, RFQID, ExtractedBrand, ConfirmedBrand, 
                      ExtractedPartNumber, ConfirmedPartNumber, PredictionDate, PredictionStatus, 
                      ConfirmationDate, ConfirmationStatus)
          VALUES (@materialId, @rfqId, @brand, @brand, 
                  @partNumber, @partNumber, @date, @status, 
                  @date, @status)
        `,
        params: {
          materialId: parseInt(material.Material_ID),
          rfqId: parseInt(RFQInformation.RFQ_ID),
          partNumber: material.Part_Number.trim(),
          brand: material.brand.trim().toUpperCase(),
          date: getCurrentDateTime(),
          status: "Done",
        },
      });
    } else {
      await mainDataset.query({
        query: `
          UPDATE Predictions_Confirmations
          SET ConfirmedPartNumber = @partNumber, ConfirmedBrand = @brand,
              ConfirmationStatus = 'Done', ConfirmationDate = @date
          WHERE MaterialID = @materialId AND RFQID = @rfqId
        `,
        params: {
          materialId: parseInt(material.Material_ID),
          rfqId: parseInt(RFQInformation.RFQ_ID),
          partNumber: material.Part_Number.trim(),
          brand: material.brand.trim().toUpperCase(),
          date: getCurrentDateTime(),
        },
      });
    }

    // Check if the material exists in partnumbers
    const [existingMaterial] = await mainDataset.query({
      query: `
        SELECT manufacturer_catalog_number
        FROM partnumbers
        WHERE TRIM(manufacturer_catalog_number) = @partNumber
      `,
      params: {
        partNumber: material.Part_Number.trim(),
      },
    });

    if (isEmpty(existingMaterial)) {
      const id = generateID();
      await mainDataset.query({
        query: `
          INSERT INTO partnumbers (id, manufacturer_catalog_number, product_name, manufacturer_name, description)
          VALUES (@id, @partNumber, @productName, @brand, @description)
        `,
        params: {
          id: id.toString(),
          partNumber: material.Part_Number.trim(),
          productName: material.Part_Number.trim(),
          brand: material.brand.toUpperCase().trim(),
          description: "",
        },
      });
    }

    materials.push(material);
  }

  RFQInformation.materials = materials;

  if (isEmpty(RFQInformation.materials)) {
    await automationDataset.query({
      query: `
      INSERT INTO automated_RFQ_logs
      (LogID, RFQID, Status, logMessage, logDate)
      VALUES (@logID, @rfqId, @status, @logMessage, @logDate)
    `,
      params: {
        logID: generateID(),
        rfqId: parseInt(RFQInformation.RFQ_ID),
        status: "ERROR",
        logMessage: `RFQ do not contain any material with confirmed materials, Please check partNumbers and brands of the materials to traceback`,
        logDate: getCurrentDateTime(),
      },
    });

    // Update the automated_RFQ table with a DONE status
    await automationDataset.query({
      query: `
        UPDATE automated_RFQ
        SET Status = 'ERROR'
        WHERE RFQID = @RFQID
      `,
      params: { RFQID: parseInt(RFQInformation.RFQ_ID) },
    });

    return null;
  }

  return RFQInformation;
}

async function filterReadyToQuoteMaterials(RFQInformation) {
  const materials = [];
  outerloop: for (const material of RFQInformation.materials) {
    const [suppliers] = await mainDataset.query({
      query: `
        SELECT DISTINCT SupplierID FROM Suppliers_Brand 
        WHERE TRIM(UPPER(Brand)) = @brand
      `,
      params: { brand: material?.brand?.toUpperCase().trim() },
    });
    let supplierList = suppliers.map((supplier) => supplier.SupplierID);

    if (isEmpty(supplierList)) {
      continue outerloop;
    } else {
      supplierList = [...new Set(supplierList)];
      material.suppliers = supplierList;

      materials.push(material);
    }
  }

  RFQInformation.materials = materials;

  if (isEmpty(RFQInformation.materials)) {
    await automationDataset.query({
      query: `
      INSERT INTO automated_RFQ_logs
      (LogID, RFQID, Status, logMessage, logDate)
      VALUES (@logID, @rfqId, @status, @logMessage, @logDate)
    `,
      params: {
        logID: generateID(),
        rfqId: parseInt(RFQInformation.RFQ_ID),
        status: "ERROR",
        logMessage: `RFQ does not contain any material with brands listed in suppliers_brand table, Please check brands of the materials and suppliers_brand table to traceback`,
        logDate: getCurrentDateTime(),
      },
    });

    // Update the automated_RFQ table with a DONE status
    await automationDataset.query({
      query: `
        UPDATE automated_RFQ
        SET Status = 'ERROR'
        WHERE RFQID = @RFQID
      `,
      params: { RFQID: parseInt(RFQInformation.RFQ_ID) },
    });

    return null;
  }

  return RFQInformation;
}

async function RequestMaterialToSupplier(payload) {
  const {
    materialID,
    rfqID,
    partNumber,
    brand,
    quantity,
    suppliers,
    RFQ_ID,
    description,
    RFQ_Number,
    RFQ_Name,
    RFQ_Date,
    Delivery_Date,
    Deadline,
    Portal,
    URL,
    FirstName,
    LastName,
    showSuppliers,
  } = payload;

  // Initialize suppliers information array
  const suppliersInformation = [];

  const [systemUser] = await mainDataset.query({
    query: `
      SELECT UserID
      FROM Users
      WHERE isSystemReserved = TRUE
    `,
  });

  const SYSTEM_USER = systemUser[0].UserID.toString();

  const material = [
    {
      description,
      rfqID,
      materialID,
      partNumber,
      brand,
      quantity,
      status: "REQUESTED",
      unitPrice: "",
      totalPrice: "",
      productCondition: "",
      notes: "",
      currency: "",
      RFQ_ID,
      RFQ_Number,
      RFQ_Name,
      RFQ_Date,
      Delivery_Date,
      leadTime: "",
      Created_By: SYSTEM_USER,
      Deadline,
      Portal,
      URL,
      name: `${FirstName} ${LastName}`,
      isAutomatedRFQ: true,
    },
  ];

  // Loop through suppliers and fetch information
  for (const supplierID of suppliers) {
    const [existingSupplier] = await mainDataset.query({
      query: `
          SELECT Name, Email, SupplierID
          FROM Suppliers
          WHERE SupplierID = @supplierID
        `,
      params: { supplierID },
    });

    if (!existingSupplier || existingSupplier.length === 0) {
      await automationDataset.query({
        query: `
        INSERT INTO automated_RFQ_logs 
        (LogID, RFQID, Status, logMessage, logDate)
        VALUES
        (@logID, @rfqId, @status, @logMessage, @logDate)
        `,
        params: {
          logID: generateID(),
          rfqId: parseInt(rfqID),
          status: "ERROR",
          logMessage: `Supplier with ID ${supplierID} not found`,
          logDate: getCurrentDateTime(),
        },
      });
    }

    suppliersInformation.push(existingSupplier[0]);

    const [existingBrand] = await mainDataset.query({
      query: `
          SELECT * FROM Suppliers_Brand
          WHERE SupplierID = @supplierID AND UPPER(Brand) = @brand
        `,
      params: { supplierID, brand: brand?.toUpperCase() },
    });
  }

  // Handle Supplier_Requests
  outerloop: for (const supplierInfo of suppliersInformation) {
    const [existingSupplierRequest] = await mainDataset.query({
      query: `
          SELECT * FROM Supplier_Requests
          WHERE supplierID = @supplierID
        `,
      params: { supplierID: supplierInfo.SupplierID.toString() },
    });

    if (!existingSupplierRequest || existingSupplierRequest.length === 0) {
      const ID = generateID();

      for (const [key, value] of Object.entries(supplierInfo)) {
        if (isEmpty(value)) {
          continue outerloop;
        }
      }

      await mainDataset.query({
        query: `
            INSERT INTO Supplier_Requests
            (ID, supplierName, supplierID, supplierEmail, materialRequested, status)
            VALUES (@ID, @supplierName, @supplierID, @supplierEmail, @materialRequested, @status)
          `,
        params: {
          ID,
          supplierName: supplierInfo.Name,
          supplierID: supplierInfo.SupplierID.toString(),
          supplierEmail: supplierInfo.Email,
          materialRequested: JSON.stringify(material),
          status: "PENDING",
        },
      });
    } else {
      const existingMaterialRequestArray = JSON.parse(
        existingSupplierRequest[0].materialRequested
      );

      const foundExistingMaterial = existingMaterialRequestArray.some(
        (existingMaterial) =>
          existingMaterial.partNumber === material[0].partNumber &&
          existingMaterial.brand === material[0].brand
      );

      if (foundExistingMaterial) {
        existingMaterialRequestArray.forEach((existingMaterial) => {
          if (
            existingMaterial.partNumber === material[0].partNumber &&
            existingMaterial.brand === material[0].brand
          ) {
            existingMaterial.status = "REQUESTED";
            existingMaterial.productCondition = "";
            existingMaterial.currency = "";
            existingMaterial.unitPrice = "";
            existingMaterial.totalPrice = "";
          }
        });
      } else {
        existingMaterialRequestArray.push(material[0]);
      }

      await mainDataset.query({
        query: `
            UPDATE Supplier_Requests
            SET materialRequested = @materialRequested, respondDate = NULL, status = "PENDING"
            WHERE supplierID = @supplierID
          `,
        params: {
          materialRequested: JSON.stringify(existingMaterialRequestArray),
          supplierID: supplierInfo.SupplierID.toString(),
        },
      });
    }
  }
  // Update Material table with selected suppliers
  let selectedSuppliers = showSuppliers ? JSON.parse(showSuppliers) : [];
  selectedSuppliers = selectedSuppliers.concat(suppliers);

  await scrapedDataset.query({
    query: `
        UPDATE Material 
        SET showSuppliers = @showSuppliers 
        WHERE Material_ID = @materialID
      `,
    params: {
      materialID: parseInt(materialID),
      showSuppliers: JSON.stringify(selectedSuppliers),
    },
  });
}

const processAutomatedRFQs = async (RFQArray) => {
  if (RFQArray.length === 0) {
    return; // Base case: stop recursion when the array is empty
  }

  const RFQID = RFQArray[0]; // Take the first RFQ from the array

  let RFQInformation = {};

  try {
    // Fetch RFQ information
    RFQInformation = await fetchRFQInformation(RFQID);

    if (!isEmpty(RFQInformation)) {
      // Filter materials ready to quote
      const FilteredMaterialsRFQInformation = await filterConfirmedMaterials(
        RFQInformation
      );

      if (!isEmpty(FilteredMaterialsRFQInformation)) {
        const FilteredSuppliersRFQInformation =
          await filterReadyToQuoteMaterials(FilteredMaterialsRFQInformation);

        if (!isEmpty(FilteredSuppliersRFQInformation?.materials)) {
          // Sequentially process materials
          for (const material of FilteredSuppliersRFQInformation.materials) {
            const payload = await generateRequestSupplierPayload(
              material,
              FilteredSuppliersRFQInformation
            );
            await RequestMaterialToSupplier(payload); // Sequential request
          }

          // Update the automasted_RFQ table with a DONE status
          await automationDataset.query({
            query: `
              UPDATE automated_RFQ
              SET Status = 'DONE'
              WHERE RFQID = @RFQID
            `,
            params: { RFQID },
          });
          await automationDataset.query({
            query: `
                  INSERT INTO automated_RFQ_logs
                  (LogID, RFQID, Status, logMessage, logDate)
                  VALUES (@logID, @rfqId, @status, @logMessage, @logDate)
                `,
            params: {
              logID: generateID(),
              rfqId: parseInt(RFQID),
              status: "SUCCESS",
              logMessage: `RFQ Automated Successfully`,
              logDate: getCurrentDateTime(),
            },
          });
        }
      }
    }
  } catch (error) {
    // Log error details into the database
    await automationDataset.query({
      query: `
      INSERT INTO automated_RFQ_logs
      (LogID, RFQID, Status, logMessage, logDate)
      VALUES (@logID, @rfqId, @status, @logMessage, @logDate)
    `,
      params: {
        logID: generateID(),
        rfqId: parseInt(RFQID),
        status: "ERROR",
        logMessage: `Unexpected Error: ${error.message}`,
        logDate: getCurrentDateTime(),
      },
    });

    // Update the automated_RFQ table with a DONE status
    await automationDataset.query({
      query: `
          UPDATE automated_RFQ
          SET Status = 'ERROR'
          WHERE RFQID = @RFQID
        `,
      params: { RFQID },
    });
  }

  // Recursive call for the rest of the array
  await processAutomatedRFQs(RFQArray.slice(1));
};

const updateRFQOfferStatus = async (offerId, rfqId, status, date) => {
  const updateOfferStatusQuery = mainDataset.query({
    query: `
      UPDATE Offers
      SET Status = @status ${status === "Won" ? `, WinningDate = @date` : ``}
      WHERE OfferID = @offerId
    `,
    params: {
      offerId: parseInt(offerId),
      status: status.toUpperCase(),
      date,
    },
  });

  const updateRFQStatusQuery = mainDataset.query({
    query: `
      UPDATE RFQ_Status
      SET CurrentStatus = @status, StatusChangeDate = @date
      WHERE RFQID = @rfqId
    `,
    params: {
      rfqId: parseInt(rfqId),
      status: status.toUpperCase(),
      date,
    },
  });

  await Promise.all([updateOfferStatusQuery, updateRFQStatusQuery]);
};

const uploadFileToGCP = async (filePath, filename) => {
  const bucketName = "clientlogos20240624";
  const destFileName = `offers/${Date.now()}-${filename}`;
  let publicUrl = "";

  try {
    // Upload the file to the bucket
    await storageClient.bucket(bucketName).upload(filePath, {
      destination: destFileName,
    });

    // Construct the public URL
    publicUrl = `https://storage.googleapis.com/${bucketName}/${destFileName}`;
    return publicUrl;
  } catch (error) {
    throw new Error("Error uploading file to GCP");
  }
};

const insertWonRFQ = async (
  OC_Number,
  OC_File_Path,
  Amount_CLP,
  Amount_USD,
  Client,
  rfqId,
  offerId,
  Processed,
  RFQ_Link,
  date
) => {
  await mainDataset.query({
    query: `
      INSERT INTO Won_RFQ
      (OC_Number, OC_File, Amount_CLP, Amount_USD, Client, Won_Date, RFQ_ID, RFQ_Link, Processed)
      VALUES
      (@OC_Number, @OC_File_Path, @Amount_CLP, @Amount_USD, @Client, @date, @rfqId, @RFQ_Link, @Processed)
    `,
    params: {
      OC_Number,
      OC_File_Path,
      Amount_CLP: parseFloat(Amount_CLP),
      Amount_USD: parseFloat(Amount_USD),
      Client,
      date,
      rfqId: parseInt(rfqId),
      RFQ_Link,
      Processed,
    },
  });
};

const deleteFile = (filePath) => {
  return new Promise((resolve, reject) => {
    fs.unlink(filePath, (err) => {
      if (err) {
        reject(err);
      } else {
        resolve();
      }
    });
  });
};

const insertIntoMonday = async (
  OC_Number,
  Client,
  Amount_CLP,
  Amount_USD,
  publicUrl,
  RFQ_Link,
  date
) => {
  const mutation = `
  mutation {
    create_item(
      board_id: 7966008980,
      column_values: """
      {
        "oc": "${OC_Number}",
        "cliente": "${Client}",
        "fecha_entrega_cliente_": "${date}",
        "fecha_oc": "${date}",
        "texto_Mjj6ar86": "${RFQ_Link}",
        "texto_mkkc4735": "${publicUrl}",
        "monto_neto": "${Amount_CLP}"
      }
      """,
      item_name: "New Item",
      group_id: "topics",
      create_labels_if_missing: false
    ) {
      id
      name
      column_values {
        text
        value
      }
    }
  }`;

  // Setup the headers for the Monday.com API request
  const headers = {
    Authorization: MONDAY_TOKEN,
    "Content-Type": "application/json",
  };

  try {
    // Make the POST request to Monday.com GraphQL API to create an item
    const response = await axios.post(
      "https://api.monday.com/v2",
      { query: mutation },
      { headers }
    );

    // Extract and return the created item from the response
    const createdItem = response.data.data.create_item;
    return createdItem;
  } catch (error) {
    return null;
  }
};

const reserveAutomatedRFQ = async () => {
  const [systemUser] = await mainDataset.query({
    query: `
          SELECT UserID
          FROM Users
          WHERE isSystemReserved = TRUE
        `,
  });

  const SYSTEM_USER = systemUser[0].UserID;

  const [automatedRFQs] = await bigQueryClient.query({
    query: `
          SELECT rs.RFQID
          FROM ${DATASET_ID_AUTOMATION}.automated_RFQ AS ar
          LEFT JOIN ${DATASET_ID_MAIN}.RFQ_Status AS rs
          ON rs.RFQID = ar.RFQID
          WHERE rs.RFQID IS NOT NULL  AND rs.CurrentStatus != 'RESERVED';
        `,
  });

  const rfqIds = automatedRFQs.map((row) => row.RFQID);

  if (rfqIds.length > 0) {
    const updateQuery = `
          UPDATE ${DATASET_ID_MAIN}.RFQ_Status
          SET
            CurrentStatus = "RESERVED",
            StatusChangeDate = CURRENT_DATE(),
            AssignedUser = @systemUser
          WHERE RFQID IN UNNEST(@rfqIds);
        `;

    await bigQueryClient.query({
      query: updateQuery,
      params: {
        systemUser: parseInt(SYSTEM_USER),
        rfqIds: rfqIds,
      },
    });
  }
};

const extractLatestReply = (message) => {
  const replyParts = message.split(/\r?\n(?:On .* at .* wrote:|From: .*)/s);
  return replyParts[0].trim();
};

const extractRFQID = (subject) => {
  const parts = subject.split("-");
  return parts.length > 2 ? parts[1].trim() : null;
};

const checkReplies = async (RFQID) => {
  const gmail = await authenticateGmail();
  const options = {
    userId: "me",
    q: RFQID
      ? `in:inbox is:unread subject:(${RFQID})`
      : "in:inbox is:unread subject:(RFQID)",
  };
  const res = await gmail.users.messages.list(options);

  if (!res.data.messages?.length) {
    return { success: true, message: "No replies found.", replies: [] };
  }

  const fetchEmail = async (msg) => {
    const email = await gmail.users.messages.get({
      userId: "me",
      id: msg.id,
      format: "full",
    });

    const headers = email.data.payload.headers;
    const inReplyTo = headers.find((h) => h.name === "In-Reply-To");
    if (!inReplyTo) return null;

    const from = headers.find((h) => h.name === "From")?.value || "Unknown";
    const subject =
      headers.find((h) => h.name === "Subject")?.value || "No Subject";
    const messageId = msg.id;
    const RFQID = parseInt(extractRFQID(subject));

    const timestamp = email.data.internalDate
      ? new Date(parseInt(email.data.internalDate)).toISOString()
      : null;

    let messageBody = "";
    if (email.data.payload.parts) {
      const textPart = email.data.payload.parts.find(
        (part) => part.mimeType === "text/plain"
      );
      if (textPart?.body?.data) {
        messageBody = Buffer.from(textPart.body.data, "base64").toString(
          "utf-8"
        );
      }
    } else if (email.data.payload.body?.data) {
      messageBody = Buffer.from(
        email.data.payload.body.data,
        "base64"
      ).toString("utf-8");
    }

    const latestReply = extractLatestReply(messageBody);

    return { from, subject, messageId, reply: latestReply, RFQID, timestamp };
  };

  let replies = (await Promise.all(res.data.messages.map(fetchEmail))).filter(
    Boolean
  );

  replies = replies.filter((reply) => Number.isInteger(reply.RFQID));

  return replies;
};

module.exports = {
  isEmpty,
  isEmptyV2,
  generateID,
  round,
  split,
  setDate,
  calculateOfferAmount,
  applyFloor,
  getCurrentDateTime,
  createBatches,
  quoteCalculate,
  pickAutomatedRFQ,
  fetchRFQInformation,
  generateRequestSupplierPayload,
  filterReadyToQuoteMaterials,
  RequestMaterialToSupplier,
  processAutomatedRFQs,
  updateRFQOfferStatus,
  uploadFileToGCP,
  insertWonRFQ,
  deleteFile,
  insertIntoMonday,
  reserveAutomatedRFQ,
  checkReplies,
};
