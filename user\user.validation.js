const Joi = require("joi");
const {
  stringValidation,
  emailValidation,
  numberValidation,
  roleValidation,
} = require("../utils/validator.util");

const loginSchema = Joi.object({
  email: emailValidation,
  password: stringValidation,
});

const fetchAllNotificationsSchema = Joi.object({
  UserID: stringValidation,
  limit: numberValidation,
  page: numberValidation,
});

const readNotificationsSchema = Joi.object({
  UserID: stringValidation,
});

const addUserSchema = Joi.object({
  FirstName: stringValidation,
  LastName: stringValidation,
  Role: roleValidation,
  Email: emailValidation,
  Password: stringValidation,
  Postal_Code:stringValidation.optional().allow(""),
  Country:numberValidation,
  State:numberValidation, 
});

const editUserSchema = Joi.object({
  UserID: stringValidation,
  FirstName: stringValidation,
  LastName: stringValidation,
  Role: roleValidation.optional().allow(""),
  Email: emailValidation,
  Postal_Code:stringValidation.optional().allow(""),
  Country:numberValidation,
  State:numberValidation,
});

const updatePasswordSchema = Joi.object({
  oldPassword: stringValidation,
  newPassword: stringValidation,
});

const sendPasswordLinkSchema = Joi.object({
  Email: emailValidation,
});

const recoverPasswordSchema = Joi.object({
  newPassword: stringValidation,
  token: stringValidation,
});

const getSingleUserSchema = Joi.object({
  ID: stringValidation,
});

const deleteUserSchema = Joi.object({
  ID: stringValidation,
});

module.exports = {
  loginSchema,
  fetchAllNotificationsSchema,
  readNotificationsSchema,
  addUserSchema,
  editUserSchema,
  updatePasswordSchema,
  sendPasswordLinkSchema,
  recoverPasswordSchema,
  getSingleUserSchema,
  deleteUserSchema,
};
