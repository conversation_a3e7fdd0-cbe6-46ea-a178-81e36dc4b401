/* eslint-disable no-unused-vars */
import React, { useState } from 'react';
import { Grid, Typography } from '@mui/material';
import { getSupplierName, isCountryManager } from 'utils/helper';
import AlertDialog from 'pages/component/dialogbox';
import ConvertHtml from 'pages/singlrRfq/convertHtmlContent';
import TableComponent from 'pages/component/table/table';
import OfferMaterialActionButton from 'pages/offer/offerMaterial/offerMaterialActionButton';
import ViewOfferAction from './action';

const OfferMaterialDetail = ({ suppliers, supplierList }) => {
  const [open, setOpen] = useState(false);
  const [modalContent, setmodalContent] = useState('');
  const unitPriceIconContent = [
    { label: 'Unit Price', value: 'unitOfferPriceCost' },
    { label: 'Unit Handling Price', value: 'unitOfferPriceHandlingCost' }
  ];
  const offerIconContent = [
    { label: 'Offer Cost', value: 'exchangedOfferCost' },
    { label: 'Offer Handling Cost', value: 'exchangeOfferHandlingCost' }
  ];
  const headers = [
    {
      name: 'supplierName',
      type: 'text',
      hideField: isCountryManager(),
      title: 'Supplier',
      highlight: 'isOffered',
      sortingactive: true,
      minWidth: '170px'
    },
    {
      name: 'unitPrice',
      type: 'text',
      title: 'Unit price',
      defaultValue: '0',
      highlight: 'isOffered',
      sortingactive: true,
      minWidth: '150px'
    },
    {
      name: 'sh',
      type: 'text',
      title: 'Shipping',
      defaultValue: '0',
      highlight: 'isOffered',
      sortingactive: true
    },

    {
      name: 'tax',
      type: 'text',
      title: 'Tax',
      defaultValue: '0',
      highlight: 'isOffered',
      sortingactive: true
    },
    {
      name: 'quantity',
      type: 'text',
      title: 'Qty',
      defaultValue: '0',
      highlight: 'isOffered',
      sortingactive: true
    },
    {
      name: 'totalCost',
      type: 'text',
      title: 'Total Cost',
      sortingactive: true,
      highlight: 'isOffered',
      minWidth: '150px'
    },
    {
      name: 'currency',
      type: 'text',
      title: 'Currency',
      defaultValue: '0',
      highlight: 'isOffered',
      sortingactive: true
    },
    {
      name: 'Weight',
      type: 'text',
      title: 'Weight',
      defaultValue: '0',
      highlight: 'isOffered',
      sortingactive: true
    },
    {
      name: 'margin',
      type: 'text',
      title: 'Margin',
      defaultValue: '0',
      highlight: 'isOffered',
      sortingactive: true
    },
    {
      name: 'actions',
      type: 'actions',
      title: 'Offer',
      sortingactive: false,
      highlight: 'isOffered',
      component: ({ rowData }) => <ViewOfferAction rowData={rowData} name="offer" iconContent={offerIconContent} />
    },
    {
      name: 'actions',
      type: 'actions',
      title: 'Offer Unit Price',
      minWidth: '200px',
      sortingactive: false,
      highlight: 'isOffered',
      component: ({ rowData }) => <ViewOfferAction rowData={rowData} name="unitOfferPrice" iconContent={unitPriceIconContent} />
    },
    {
      name: 'offerCurrency',
      type: 'text',
      minWidth: '130px',
      title: 'Offer Currency',
      defaultValue: '0',
      minWidth: '150px',
      sortingactive: true,
      highlight: 'isOffered'
    },
    {
      name: 'actions',
      btnType: 'pdf',
      type: 'actions',
      title: 'Notes',
      sortingactive: false,
      component: OfferMaterialActionButton,

      // disableBtn: loader || false,
      buttonOnClick: (type, id, materialId, index, data) => {
        if (type === 'pdf') {
          handleOpen(data?.notes);
        }
      }
    }
  ];
  const getValueByName = (supplier, name, item) => {
    if (name === 'supplierName') {
      return getSupplierName(supplier, supplierList);
    } else {
      return supplier[name] || item?.defaultValue || '-';
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleOpen = (value) => {
    setOpen(true);
    setmodalContent(value);
  };

  return (
    <>
      <AlertDialog
        content=""
        onAgree={() => {}}
        cancel={() => handleClose()}
        Component={
          <>
            <div style={{ minWidth: '400px' }}>
              <Typography variant="h5" color="secondary">
                Notes
              </Typography>
              <ConvertHtml content={modalContent || ''} />
            </div>
          </>
        }
        open={open}
        showCancelBtn={true}
        showCard={false}
        borderRadius="20px"
      />
      <TableComponent
        columns={headers?.filter((header) => !header?.hideField)}
        maxHeight="100%"
        rows={
          suppliers?.map((supplier) => ({
            ...supplier,
            supplierName: getValueByName(supplier, 'supplierName')
          })) || []
        }
        enablePagination={true}
        defaultPage={5}
        placeActionButtonsIn="header"
      />
    </>
  );
};

export default OfferMaterialDetail;
