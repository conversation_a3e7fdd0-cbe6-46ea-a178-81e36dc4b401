import { Button } from '@mui/material';
import FilterComponent, { createCentralizedFilterPayload } from './filter';
import SelectComponent from 'pages/component/selectComponent';
import { addSupplierFilter, ARRAY_TYPE, defaultFilterPayload, FORMREQUEST, PRICETABLE } from '../constant';
import DynamicAutocomplete from 'pages/component/autoComplete';
import { getApprovedClients } from 'utils/helper';
import { requestQuoteFunction } from '../sendEmail';
import { useDispatch } from 'react-redux';
import { ARRAY } from 'pages/availableRfq/materialFilter.js/component/constant';
import AlertDialog from 'pages/component/dialogbox';
import { useState } from 'react';
import UseQuote from '../priceTable/UseQuote';
import EnterPriceManually from '../requestedPrice/enterManualPrice';
import { validate } from 'pages/component/validation';
import { addManualPriceAction, getAllCentralizedRequestAction } from 'redux/reducers/centralizedReducer';
import { get } from 'lodash';
const RequestedPriceHeader = ({ formData, setFormData, supplierList, loading }) => {
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);
  const [errors, setErrors] = useState(false);
  const [manualPrice, setManualPrice] = useState({});
  const handleChange = (e, newValue) => {
    const newFilter = {
      field: 'supplierBrandList',
      type: ARRAY_TYPE,
      notShowFilter: true,
      data: newValue?.brands
    };

    setFormData({
      ...formData,
      requestedPrice: {
        ...formData?.requestedPrice,
        supplier: newValue
      },
      filters: {
        ...formData?.filters,
        page: 1,
        filterValues: addSupplierFilter(formData?.filters?.filterValues, newFilter, 'supplierBrandList')
      }
    });
  };

  const { supplier, tableData } = formData?.requestedPrice || {};

  const isDisabled = () => {
    return !supplier || loading || formData?.requestedPrice?.tableData?.length === 0;
  };

  const handleOpenPriceTable = () => {
    let serializedData;

    const selectedRequests = formData?.requestedPrice?.tableData?.filter((request) => request?.select);

    if (selectedRequests?.length > 0) {
      // If there are selected items, send only their IDs
      serializedData = selectedRequests.map((request) => request?.ID).join(',');
    } else {
      // If no items are selected, send all IDs
      serializedData = formData?.requestedPrice?.tableData?.map((request) => request?.ID).join(',');
    }

    if (serializedData) {
      const url = `/price-table/${encodeURIComponent(serializedData)}`;
      window.open(url, '_blank');
    }
  };
  const handleConfirm = async () => {
    const rules = {
      supplier: { required: true, label: 'Supplier' },
      qty: { required: true, label: 'Quantity', isNumber: true },
      price: { required: true, label: 'Price', isNumber: true },
      currency: { required: true, label: 'Currency' }
    };

    const validationErrors = validate(manualPrice, rules);
    setErrors(validationErrors || {});
    if (Object.keys(validationErrors)?.length === 0) {
      const requestedPriceData = formData?.requestedPrice?.tableData?.find((item) => item?.select);

      // Destructure the Material_Information and requestedPriceData objects
      const {
        Material_Information: {
          brand,
          partNumber,
          quantity,
          firstName: FirstName,
          lastName: LastName,
          RFQ_Number,
          RFQ_Name,
          RFQ_Date,
          Portal,
          Delivery_Date,
          Deadline,
          Material_Description,
          Material_ID: materialID,
          URL
        } = {}
      } = requestedPriceData || {};

      const { RFQ_ID, Date } = requestedPriceData || {};

      const payload = {
        suppliers: [manualPrice?.supplier?.SupplierID],
        data: {
          status: 'RESPONDED',
          description: Material_Description,
          partNumber,
          brand,
          quantity: Number(manualPrice?.qty),
          unitPrice: manualPrice?.price,
          productCondition: '',
          notes: '',
          currency: manualPrice?.currency,
          RFQ_ID: Number(RFQ_ID),
          RFQ_Number,
          RFQ_Name,
          RFQ_Date: Date?.value || RFQ_Date?.value,
          Delivery_Date: Delivery_Date?.value,
          Deadline: Deadline?.value,
          Portal,
          URL,
          totalPrice: Number((Number(manualPrice?.qty || 0) * Number(manualPrice?.price || 0)).toFixed(2))
        }
      };

      const response = await dispatch(addManualPriceAction(payload));

      if (get(response, 'payload.success', false)) {
        handleClose();
        const { page, limit, filterValues } = formData?.filters || {};

        dispatch(
          getAllCentralizedRequestAction({
            ...defaultFilterPayload,
            page: page,
            limit: limit || 10,
            filterData: createCentralizedFilterPayload(filterValues)?.filterData
          })
        );
      }
    }
  };
  const handleClose = () => {
    setOpen(false);
    setManualPrice({});
    setErrors({});
  };
  const checkEnterIsManualPriceDisable = () => {
    return (
      (
        loading ||
        formData?.requestedPrice?.tableData?.length === 0 ||
        formData?.requestedPrice?.tableData?.filter((requestedPriceData) => requestedPriceData?.select)
      )?.length !== 1
    );
  };

  const getSelectedBrand = () => {
    if (checkEnterIsManualPriceDisable) {
      const selectedRequest = formData?.requestedPrice?.tableData?.filter((requestedPriceData) => requestedPriceData?.select);
      return selectedRequest?.length > 0 ? selectedRequest[0]?.Brand : null;
    }
    return '';
  };

  const buttons = [
    { label: 'Cancel', onClick: () => handleClose(), variant: 'outlined', color: 'error', disabled: loading },
    { label: 'Submit', onClick: () => handleConfirm(), variant: 'contained', color: 'primary', disabled: loading }
  ];

  return (
    <div className="header-container">
      <AlertDialog
        cancel={handleClose}
        showCard={true}
        buttons={buttons}
        borderRadius="20px"
        Component={<EnterPriceManually Brand={getSelectedBrand()} manualPriceInfo={[manualPrice, setManualPrice, errors, supplierList]} />}
        open={open}
      />
      <FilterComponent setFormData={setFormData} formData={formData} />
      <div className="actions-container">
        <div className="buttons-container">
          <div className="auto-select">
            <DynamicAutocomplete
              options={getApprovedClients(supplierList)}
              value={supplier}
              onChange={handleChange}
              placeholder="Select Supplier"
              getOptionLabel={(option) => option.label}
            />
          </div>
          <Button
            variant="contained"
            size="small"
            onClick={() => setFormData({ ...formData, changeComponent: FORMREQUEST })}
            disabled={isDisabled()}
          >
            Form Request
          </Button>
          <Button
            variant="contained"
            size="small"
            onClick={handleOpenPriceTable}
            disabled={loading || formData?.requestedPrice?.tableData?.length === 0}
          >
            Price List
          </Button>
          <Button variant="contained" size="small" onClick={() => setOpen(true)} disabled={checkEnterIsManualPriceDisable()}>
            Enter Price Manually
          </Button>
        </div>
      </div>
    </div>
  );
};

export default RequestedPriceHeader;
