// controllers/centralizedMaterial.controller.js
const {
  bigQueryClient,
  sourcingDataset,
  mainDataset,
  scrapedDataset,
} = require("../db");
const {
  DATASET_ID_SOURCING,
  DATASET_ID_SCRAPE,
  DATASET_ID_MAIN,
} = require("../constants");
const { apiHandler, apiError, apiResponse } = require("../utils/api.util");
const {
  NOT_FOUND,
  FETCH,
  CUSTOM_SUCCESS,
  ADD_SUCCESS,
} = require("../utils/message.util");
const { generateID, isEmpty } = require("../utils/misc.util");
const crypto = require("crypto");
const { supplierMaterialRequestTemplate } = require("../utils/templates");
const { sendMail } = require("../utils/email.util");

const getListofCentralizedMaterialForQuoting = apiHandler(async (req, res) => {
  const {
    deadlineFrom,
    deadlineTo,
    brand,
    searchQuery,
    status = "0",
    page = 1,
    limit = 25,
  } = req.body;

  const offset = (Number(page) - 1) * Number(limit);
  const where = [];
  const params = { limit: Number(limit), offset };

  if (deadlineFrom) {
    where.push("cm.Deadline >= @deadlineFrom");
    params.deadlineFrom = deadlineFrom;
  }
  if (deadlineTo) {
    where.push("cm.Deadline <= @deadlineTo");
    params.deadlineTo = deadlineTo;
  }

  if (brand) {
    where.push("cm.Brand = @brand");
    params.brand = brand.trim();
  }

  if (searchQuery) {
    const searchQueryTrimmed = searchQuery.trim();
    where.push(`
      (
        cm.PartNumber LIKE CONCAT('%', @searchQuery, '%')
        OR CAST(cm.MaterialID AS STRING) LIKE CONCAT('%', @searchQuery, '%')
        OR CAST(rfq.RFQ_ID AS STRING) LIKE CONCAT('%', @searchQuery, '%')
        OR rfq.Company_Name LIKE CONCAT('%', @searchQuery, '%')
      )
    `);
    params.searchQuery = searchQueryTrimmed;
  }

  switch (String(status)) {
    case "1":
      where.push("cm.Status = 'New'");
      where.push("IFNULL(cm.OfferReady, FALSE) = FALSE");
      break;
    case "2":
      where.push("cm.Status = 'Quote Received'");
      break;
    case "3":
      where.push("IFNULL(cm.OfferReady, FALSE) = TRUE");
      break;
  }

  const whereClause = where.length ? `WHERE ${where.join(" AND ")}` : "";

  const sql = `
    SELECT
      cm.MaterialID,
      cm.PartNumber,
      cm.Brand,
      cm.Quantity,
      cm.Deadline,
      cm.Status,
      cm.OfferReady,
      cm.HasHistory,
      cm.CreatedTimestamp,
      cm.UpdatedTimestamp,
      rfq.RFQ_ID,
      rfq.RFQ_Number,
      rfq.Company_Name AS ClientName,
      mat.Material_Description AS MaterialDescription,
      mat.Quantity_Required AS MaterialQuantityRequired,
      mat.Position AS MaterialPosition,
      mat.Notes AS MaterialNotes
    FROM ${DATASET_ID_SOURCING}.Centralized_Materials AS cm
    LEFT JOIN ${DATASET_ID_SCRAPE}.RFQ AS rfq
      ON cm.RFQID = rfq.RFQ_ID
    LEFT JOIN ${DATASET_ID_SCRAPE}.Material AS mat
      ON cm.MaterialID = mat.Material_ID
    ${whereClause}
    ORDER BY cm.Deadline ASC
    LIMIT @limit
    OFFSET @offset
  `;

  const [rows] = await bigQueryClient.query({ query: sql, params });

  if (!rows.length) {
    return apiError(NOT_FOUND, "Materials", null, res);
  }

  return apiResponse(
    FETCH,
    "Centralized Material",
    {
      count: rows.length, // page count; switch to COUNT(*) OVER() if you need total rows
      page: Number(page),
      limit: Number(limit),
      materials: rows,
    },
    res
  );
});

const getAvailableBrands = apiHandler(async (_req, res) => {
  const sql = `
    SELECT DISTINCT Brand
    FROM ${DATASET_ID_SOURCING}.Centralized_Materials
    WHERE Brand IS NOT NULL AND Brand != ''
    ORDER BY Brand
  `;

  const [rows] = await bigQueryClient.query({ query: sql });

  return apiResponse(
    FETCH,
    "Available brands",
    rows.map((r) => r.Brand),
    res
  );
});

const requestPrice = apiHandler(async (req, res) => {
  const { supplier, materials } = req.body;

  const [supplierInformation] = await mainDataset.query({
    query: `
        SELECT Name, Email, SupplierID
        FROM Suppliers
        WHERE SupplierID = @SupplierID
      `,
    params: {
      SupplierID: supplier,
    },
  });

  if (isEmpty(supplierInformation)) {
    return apiError(NOT_FOUND, "Supplier Information", null, res);
  }

  for (const [key, value] of Object.entries(supplierInformation[0])) {
    if (isEmpty(value)) {
      return apiError(CUSTOM_ERROR, `Supplier does not have ${key}`, null, res);
    }
  }

  const materialsWithInfo = materials.map((material) => ({
    description: material.description || "",
    RFQ_ID: material.RFQID,
    materialID: material.materialID,
    partNumber: material.partNumber || "",
    brand: material.brand || "",
    quantity: material.quantity || 0,
    status: "REQUESTED",
    Created_By: req.user.userId.toString(),
  }));

  const [existingSupplier] = await sourcingDataset.query({
    query: `
      SELECT materialRequested FROM Supplier_Requests
      WHERE supplierID = @SupplierID
      `,
    params: {
      SupplierID: supplierInformation[0].SupplierID,
    },
  });

  let supplierRequestID = null;

  if (isEmpty(existingSupplier)) {
    const ID = generateID();
    supplierRequestID = ID;

    const [newSupplier] = await sourcingDataset.query({
      query: `
          INSERT INTO Supplier_Requests
          (ID, supplierID, supplierEmail, materialRequested, Status, Created_At)
          values (@ID,  @supplierID, @supplierEmail, @materialRequested, @status, TIMESTAMP_MILLIS(@NowMs))
          `,
      params: {
        ID,
        supplierID: supplierInformation[0].SupplierID,
        supplierEmail: supplierInformation[0].Email,
        materialRequested: JSON.stringify(materialsWithInfo),  
        status: "PENDING",
        NowMs: Date.now(),
      },
    });
  } else {
    supplierRequestID = existingSupplier[0].ID;
    const existingMaterialRequestString = existingSupplier[0].materialRequested;
    const existingMaterialRequestArray = JSON.parse(
      existingMaterialRequestString
    );

    for (const newMaterial of materialsWithInfo) {
      const existingMaterial = existingMaterialRequestArray.find(
        (existingMaterial) =>
          existingMaterial.partNumber.trim() ===
            newMaterial.partNumber.trim() &&
          existingMaterial.brand.trim().toUpperCase() ===
            newMaterial.brand.trim().toUpperCase()
      );

      if (!existingMaterial) {
        existingMaterialRequestArray.push(newMaterial);
      } else {
        existingMaterial.status = "REQUESTED";
      }
    }

    const [updatingSupplier] = await mainDataset.query({
      query: `
      UPDATE Supplier_Requests SET
      materialRequested = @materialRequested, Replyed_At = NULL, status = "PENDING"
      WHERE supplierID=@supplierID

    `,
      params: {
        materialRequested: JSON.stringify(existingMaterialRequestArray),
        supplierID: supplierInformation[0].SupplierID,
      },
    });
  }

  const materialIDs = materials.map((m) => m.materialID);
  const rfqIDs = materials.map((m) => m.RFQID);

  await sourcingDataset.query({
    query: `
    UPDATE Centralized_Materials
    SET Status = 'QUOTE_REQUESTED'
    WHERE MaterialID IN UNNEST(@MaterialIDs) AND RFQID IN UNNEST(@RFQIDs)
  `,
    params: { MaterialIDs: materialIDs, RFQIDs: rfqIDs },
  });

  const { name, email, ID } = existingSupplier[0];

  const link = `${BASE_URL}/supplier-portal/centralized-material-requests/${ID}`;

  const data = {
    name,
    link,
  };

  let option = {
    to: email,
    subject: supplierMaterialRequestTemplate.emailSubject,
    html: supplierMaterialRequestTemplate.htmlTemplate(data),
  };

  try {
    await sendMail(option);
  } catch (error) {
    console.log("Error Occured While Sending Mail - ", error);
  }

  return apiResponse(ADD_SUCCESS, "Suppliers Requests", null, res);
});

const getRequestedMaterialsPrice = apiHandler(async (req, res) => {
  const { materialID, RFQID } = req.body;

  const [result] = await sourcingDataset.query({
    query: `
    SELECT
      cq.*,
      s.Name  AS SupplierName,
      s.Email AS SupplierEmail,
      cm.Status AS MaterialStatus
    FROM ${DATASET_ID_SOURCING}.Centralized_Quotes AS cq
    LEFT JOIN ${DATASET_ID_MAIN}.Suppliers AS s
      ON s.SupplierID = cq.SupplierID
    LEFT JOIN ${DATASET_ID_SOURCING}.Centralized_Materials AS cm
      ON cm.MaterialID = cq.MaterialID AND cm.RFQID = cq.RFQID
    LEFT JOIN ${DATASET_ID_SCRAPE}.RFQ AS rfq
      ON rfq.RFQ_ID = cq.RFQID
    LEFT JOIN ${DATASET_ID_SCRAPE}.Material AS m
      ON m.Material_ID = cq.MaterialID
    WHERE cq.MaterialID = CAST(@MaterialID AS INT64)
      AND cq.RFQID    = CAST(@RFQID    AS INT64)
  `,
    params: { MaterialID: materialID, RFQID: RFQID },
  });

  return apiResponse(FETCH, "Requested Materials Price", result, res);
});

const addRequestedMaterialsPrice = apiHandler(async (req, res) => {
  const {
    supplierID,
    materialID,
    RFQID,
    unitPrice,
    quantity,
    leadTime,
    currency,
  } = req.body;

  const [existingQuote] = await sourcingDataset.query({
    query: `
      SELECT QuoteID
      FROM Centralized_Quotes
      WHERE MaterialID = @MaterialID
        AND RFQID    = @RFQID
        AND SupplierID = @SupplierID
    `,
    params: { MaterialID: materialID, RFQID: RFQID, SupplierID: supplierID },
  });

  if (!isEmpty(existingQuote)) {
    return apiError(
      CUSTOM_ERROR,
      `Quote already exist for the same supplier`,
      null,
      res
    );
  }

  const quoteID = parseInt(String(Date.now()).slice(-12), 10);

  await sourcingDataset.query({
    query: `
      INSERT INTO Centralized_Quotes
      (QuoteID, SupplierID, MaterialID, RFQID, CreatedTimestamp, unitPrice, quantity, leadTime, Currency, totalPrice, source, notes, QuoteDate)
      VALUES (@QuoteID, @SupplierID, @MaterialID, @RFQID, TIMESTAMP_MILLIS(@NowMs), @unitPrice, @quantity, @leadTime, @currency, @totalPrice, @source, @notes,   DATE(TIMESTAMP_MILLIS(@QuoteDateMs)))
    `,
    params: {
      QuoteID: quoteID,
      SupplierID: supplierID,
      MaterialID: materialID,
      RFQID: RFQID,
      NowMs: Date.now(),
      unitPrice,
      quantity,
      leadTime,
      currency,
      totalPrice: unitPrice * quantity,
      source: "MANUAL",
      notes: "",
      QuoteDateMs: Date.now(),
    },
  });
  return apiResponse(ADD_SUCCESS, "Requested Materials Price", null, res);
});

module.exports = {
  getListofCentralizedMaterialForQuoting,
  getAvailableBrands,
  requestPrice,
  getRequestedMaterialsPrice,
  addRequestedMaterialsPrice,
};
