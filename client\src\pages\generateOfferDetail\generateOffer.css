.offer-detail-select {
  width: 200px;
  margin-bottom: 10px;
  height: 30px
}

.select-input-label {
  margin-top: -7px;
  font-size: 12px
}

.generate-offer-input-field {
  margin-bottom: 10px;
  width: 200px;
  height: 30px
}

.offer-detail-label {
  padding-left: 20px;
}

.margin-field {
  width: 50px
}

.card-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.quotes-inner-container {
  width: 100%;
}

.material-requested {
  display: flex;
  justify-content: space-between;
  overflow-x: auto;
  scroll-behavior: smooth;
}

.quote-label {
  width: 100px;
  margin-top: 20px
}

.field-wrapper {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 20px
}

.update-material {
  display: flex;
  justify-content: space-between;
}
.update-btn-wrapper {
  display: flex;
  align-items: center;
}
.benchmark-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 50px;
}

.material-name-wrapper {
  max-width: 200px;
  flex: 1;
}
@media (max-width: 600px) {
  .benchmark-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }

  .material-name-wrapper {
    max-width: 100%;
  }
}
