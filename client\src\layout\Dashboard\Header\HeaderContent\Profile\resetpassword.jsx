import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>, Button, Grid, Container, Typography, Box } from '@mui/material';
import InputField from 'pages/component/inputField';
import { validate } from 'pages/component/validation';
import { logoutUser, showAlert } from 'utils/helper';
import { useDispatch, useSelector } from 'react-redux';
import { getUserDetail } from 'utils/auth';
import { updateUserMaintenanace, changePasswordAction, forgotPasswordAction, resetPasswordAction } from 'redux/reducers/userReducer'; // Make sure to import changePasswordAction
import Loader from 'components/Loader';
import { useParams } from 'react-router';

const ResetPassword = () => {
  const [userDetail, setUserDetail] = useState({
    email: '',
  });
  const [errors, setErrors] = useState({});
  const userData = useSelector((state) => state.users)
  const dispatch = useDispatch();
  const token = useParams()?.id

  const fields = [
    { name: 'newPassword', label: 'New Password', type: 'password', placeholder: 'Enter New password', autoComplete: 'new-password' },
    { name: 'confirmPassword', label: 'Confirm Password', placeholder: 'Enter Confirm password', type: 'password' },
  ];

  const handleChange = (name, value) => {
    setUserDetail((prevUserDetail) => ({
      ...prevUserDetail,
      [name]: value
    }));
  };

  const renderField = (field) => {
    const { type, name, placeholder, autoComplete, label } = field || {};
    return (<>
      <Typography variant="subtitle1" gutterBottom>{label}</Typography>
      <InputField
        type={type}
        name={name}
        placeholder={placeholder}
        value={userDetail[name] || ''}
        onChange={(e) => handleChange(name, e?.target?.value)}
        errors={errors}
        fullWidth
        autoComplete={autoComplete}

      />
    </>
    );
  };

  const sendForgotPasswordMail = async () => {
    const rules = {
      newPassword: { required: true, label: 'Password' },
      confirmPassword: { required: true, type: 'confirmPassword', label: 'Confirm Password', matchValue: 'newPassword' },
    };
    const validation = validate(userDetail, rules);
    setErrors(validation);

    if (Object.keys(validation).length === 0) {
      const { newPassword } = userDetail || {};

      const payload = {
        token,
        newPassword
      };
      const response = await dispatch(resetPasswordAction(payload));
      const success = response?.payload || {}

      if (success) {
        logoutUser()
      }
    }
  };

  const cancel = () => {

  }

  const isLoading = () => {
    return userData?.loading
  }

  const buttons = [
    {
      label: 'Cancel',
      onClick: cancel,
      variant: 'outlined',
      color: 'error',
      disabled: isLoading(),
    },
    {
      label: 'Save',
      onClick: sendForgotPasswordMail,
      variant: 'contained',
      color: 'primary',
      disabled: isLoading(),
    }
  ];

  return (
    <div style={styles?.container}>
      {isLoading() && <Loader />}

      <Box my={4}>
        <Typography variant="h4" component="h1" gutterBottom color="secondary" mb={4}>
          Reset Password
        </Typography>
        <Grid container spacing={4}>
          {fields
            ?.map((field) => (
              <Grid item xs={12} sm={field.sm} key={field.name}>
                {renderField(field)}
              </Grid>
            ))}

          <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', gap: '10px' }}>
            {buttons
              ?.filter(button => button.show !== false)
              .map((button, index) => (
                <Button
                  key={index}
                  onClick={button.onClick}
                  variant={button.variant}
                  color={button.color}
                  disabled={button?.disabled}
                >
                  {button.label}
                </Button>
              ))}
          </Grid>
        </Grid>
      </Box>
    </div>
  );
};

export default ResetPassword;

const styles = {
  container: {
    maxWidth: '300px',
    margin: '0 auto',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center'
  }
}