const Joi = require("joi");
const {
  stringValidation,
  numberValidation,
  currencyValidation,
  booleanValidation,
} = require("../utils/validator.util");

const getClientSchema = Joi.object({
  limit: numberValidation.optional().allow(""),
  page: numberValidation.optional().allow(""),
  search: stringValidation.optional().allow(""),
  sort: Joi.object({
    field: Joi.array().items(stringValidation),
    order: Joi.array().items(stringValidation),
  })
    .optional()
    .allow(""),
}).unknown();

const addClientSchema = Joi.object({
  ClientID: stringValidation,
  Name: stringValidation,
  Address: stringValidation.optional().allow(""),
  Municipality: stringValidation.optional().allow(""),
  BusinessActivity: stringValidation.optional().allow(""),
  Postal_Code:stringValidation.optional().allow(""),
  State:numberValidation, 
  Country:numberValidation,
  Region:stringValidation
});

const getSingleClientSchema = Joi.object({
  ClientID: stringValidation,
});

const editClientSchema = Joi.object({
  ClientID: stringValidation,
  Name: stringValidation,
  Address: stringValidation.optional().allow(""),
  Municipality: stringValidation.optional().allow(""),
  BusinessActivity: stringValidation.optional().allow(""),
  Postal_Code:stringValidation.optional().allow(""),
  State:numberValidation, 
  Country:numberValidation,
  Region:stringValidation
});

const deleteClientSchema = Joi.object({
  ClientID: stringValidation,
});

const updateClientStatusSchema = Joi.object({
  ID: stringValidation,
  status: stringValidation,
  reason: stringValidation.optional().allow(""),
  token: stringValidation,
});

const getStateListSchema = Joi.object({
  CountryId: stringValidation,
});

module.exports = {
  getClientSchema,
  addClientSchema,
  getSingleClientSchema,
  editClientSchema,
  deleteClientSchema,
  updateClientStatusSchema,
  getStateListSchema
};
