const Joi = require("joi");
const {
  stringValidation,
  numberValidation,
  currencyValidation,
  conditionValidation,
  dateValidation,
  emailValidation,
  urlValidation,
  booleanValidation,
} = require("../utils/validator.util");

const fetchSuppliersSchema = Joi.object({
  brand: stringValidation,
  partNumber: stringValidation,
  Material_ID: stringValidation,
  RFQ_ID: stringValidation,
});

const addSupplierPriceSchema = Joi.object({
  RequestID: stringValidation,
  supplierID: stringValidation,
  isIndividual: booleanValidation.optional().allow(""),
  data: Joi.array()
    .required()
    .items(
      Joi.object({
        partNumber: stringValidation,
        brand: stringValidation,
        description: stringValidation,
        quantity: numberValidation,
        unitPrice: numberValidation.allow("").optional(),
        totalPrice: numberValidation.allow("").optional(),
        currency: currencyValidation.allow("").optional(),
        productCondition: conditionValidation.allow("").optional(),
        notes: stringValidation.allow("").optional(),
        RFQ_ID: numberValidation,
        RFQ_Number: stringValidation.allow("").optional(),
        RFQ_Name: stringValidation.allow("").optional(),
        // RFQ_Date: dateValidation,
        // Delivery_Date: dateValidation.allow("").optional(),
        // Deadline: dateValidation,
        Portal: stringValidation.allow("").optional(),
        URL: stringValidation.allow("").optional(),
        name: stringValidation.allow("").optional(),
        Created_By: stringValidation.allow("").optional(),
      }).unknown()
    ),
}).unknown();

const saveSupplierPriceSchema = Joi.object({
  RequestID: stringValidation,
  supplierID: stringValidation,
  data: Joi.array()
    .required()
    .items(
      Joi.object({
        partNumber: stringValidation.allow("").optional(),
        brand: stringValidation.allow("").optional(),
        description: stringValidation.allow("").optional(),
        quantity: numberValidation.optional(),
        unitPrice: numberValidation.allow("").optional(),
        totalPrice: numberValidation.allow("").optional(),
        currency: currencyValidation.allow("").optional(),
        productCondition: stringValidation.allow("").optional(),
        notes: stringValidation.allow("").optional(),
        RFQ_ID: numberValidation,
        RFQ_Number: stringValidation.allow("").optional(),
        RFQ_Name: stringValidation,
        // RFQ_Date: dateValidation,
        // Delivery_Date: dateValidation.allow("").optional(),
        // Deadline: dateValidation,
        Portal: stringValidation.allow("").optional(),
        URL: stringValidation.allow("").optional(),
        name: stringValidation.allow("").optional(),
        Created_By: stringValidation.allow("").optional(),
      }).unknown()
    ),
}).unknown();

const requestSupplierSchema = Joi.object({
  materialID: stringValidation,
  rfqID: stringValidation,
  partNumber: stringValidation,
  brand: stringValidation,
  quantity: numberValidation,
  suppliers: Joi.array().required().items(numberValidation),
  RFQ_ID: numberValidation,
  RFQ_Number: stringValidation.allow("").optional(),
  RFQ_Name: stringValidation,
  RFQ_Date: dateValidation,
  Delivery_Date: dateValidation.allow("").optional(),
  Deadline: dateValidation,
  Portal: stringValidation.allow("").optional(),
  URL: stringValidation.allow("").optional(),
  FirstName: stringValidation.allow("").optional(),
  LastName: stringValidation.allow("").optional(),
});

const fetchSuppliersRequestsSchema = Joi.object({
  ID: stringValidation,
});

const useSupplierSchema = Joi.object({
  Material_ID: numberValidation,
  showSuppliers: Joi.array().items(numberValidation.optional()),
});

const importSupplierPriceSchema = Joi.object({
  SupplierID: stringValidation,
  Action: stringValidation,
  data: Joi.array()
    .required()
    .items(
      Joi.object({
        PartNumber: stringValidation.allow("").optional(),
        Price: numberValidation.optional(),
        Currency: currencyValidation.allow("").optional(),
      })
    ),
});

const addSupplierSchema = Joi.object({
  Name: stringValidation,
  ContactName: stringValidation,
  ContactLastname: stringValidation,
  Email: emailValidation,
  Country: stringValidation,
  Web: urlValidation.optional().allow(""),
  Shipping: stringValidation,
  Notes: stringValidation.optional().allow(""),
});

const getSingleSupplierSchema = Joi.object({
  SupplierID: stringValidation,
});

const editSupplierSchema = Joi.object({
  SupplierID: numberValidation,
  Name: stringValidation,
  ContactName: stringValidation,
  ContactLastname: stringValidation,
  Email: emailValidation,
  Country: stringValidation,
  Web: urlValidation.optional().allow(""),
  Shipping: stringValidation,
  Notes: stringValidation.optional().allow(""),
});

const deleteSupplierSchema = Joi.object({
  SupplierID: stringValidation,
});

const getSupplierSchema = Joi.object({
  limit: numberValidation.optional().allow(""),
  page: numberValidation.optional().allow(""),
  search: stringValidation.optional().allow(""),
  sort: Joi.object({
    field: Joi.array().items(stringValidation),
    order: Joi.array().items(stringValidation),
  })
    .optional()
    .allow(""),
}).unknown();

const mailSupplierSchema = Joi.object({
  ID: stringValidation,
});

const updateSupplierStatusSchema = Joi.object({
  ID: stringValidation,
  status: stringValidation,
  reason: stringValidation.optional().allow(""),
  token: stringValidation,
});

const markUnavailableSchema = Joi.object({
  data: Joi.object().unknown(),
  materialID: stringValidation.optional().allow(""),
  partNumber: stringValidation,
  supplierId: stringValidation.optional(),
});

module.exports = {
  fetchSuppliersSchema,
  addSupplierPriceSchema,
  requestSupplierSchema,
  fetchSuppliersRequestsSchema,
  saveSupplierPriceSchema,
  useSupplierSchema,
  importSupplierPriceSchema,
  addSupplierSchema,
  getSingleSupplierSchema,
  editSupplierSchema,
  deleteSupplierSchema,
  getSupplierSchema,
  mailSupplierSchema,
  updateSupplierStatusSchema,
  markUnavailableSchema,
};
