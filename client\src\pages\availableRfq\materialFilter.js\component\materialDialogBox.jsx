import * as React from 'react';
import PropTypes from 'prop-types';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import { Grid, ListItemAvatar, ListItemText, Typography } from '@mui/material';
import Image from 'pages/component/image';
import { get } from 'lodash';
import Loader from 'components/Loader';
import ConvertHtml from '../../../singlrRfq/convertHtmlContent';

export default function Modal({ title, icon, actions, open, handleClose, secondaryContent, loading }) {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));
  return (
    <React.Fragment>
      <Dialog fullScreen={fullScreen} open={open} onClose={handleClose} aria-labelledby="responsive-dialog-title">
        <DialogTitle id="responsive-dialog-title" className="material-dialogbox">
          {' '}
          <ListItemAvatar className="material-icon">
            <Image src={icon} alt={title} className="company-image" />
          </ListItemAvatar>
          <Grid item xs>
            <ListItemText
              sx={{ width: '390px' }}
              primary={
                <Typography variant="h5" className="title-text material-dates" color="secondary">
                  {title}
                </Typography>
              }
            />
          </Grid>
        </DialogTitle>
        <DialogContent sx={{ padding: '0px', paddingRight: 2 }}>
          <DialogContentText>
            {' '}
            <Grid justifyContent="center">
              {secondaryContent?.map((item, index) => (
                <>
                  <Typography variant="h5" key={index} className="material-dates" sx={{ paddingTop: '15px' }} color="secondary">
                    {item?.Material_Description}
                  </Typography>
                  <Typography variant="body2" key={index} className="material-dates" sx={{ paddingTop: '10px' }}>
                    Part Number : {item?.Part_Number}
                  </Typography>
                  <Typography variant="body2" key={index} className="material-dates" sx={{ paddingTop: '5px' }}>
                    <ConvertHtml content={get(item, 'Notes', '')} />
                  </Typography>
                </>
              ))}
            </Grid>
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'center' }}>
          {actions?.map((action, index) => (
            <Button key={index} onClick={action.handler} autoFocus={action.autoFocus} variant={action?.variant} disabled={loading}>
              {action.text}
            </Button>
          ))}
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}

Modal.propTypes = {
  buttonText: PropTypes.string,
  title: PropTypes.string,
  content: PropTypes.string,
  actions: PropTypes.arrayOf(
    PropTypes.shape({
      text: PropTypes.string,
      handler: PropTypes.func,
      autoFocus: PropTypes.bool
    })
  ),
  open: PropTypes.bool.isRequired,
  handleOpen: PropTypes.func.isRequired,
  handleClose: PropTypes.func.isRequired
};
