import React from 'react';
import { Typography, Box, CardContent, Grid } from '@mui/material';
import { convertDateToStringFormat } from 'utils/helper';

const ClientDetailTable = React.memo(({ fields, supplierData }) => {
  return (
    <Grid container spacing={2} className="gridContainer">
      {fields?.map((field) => {
        // Check whether to display the field (label, icon, and value)
        if (field.show && !supplierData[field.show]) {
          return null; // Don't render anything if field.show is present but not valid in supplierData
        }

        return (
          <Grid item xs={12} sm={3} key={field.key} className="gridItem">
            <CardContent className="cardContent">
              {field.icon && (
                <Box className="iconBox" sx={{ color: 'primary.main' }}>
                  {field.icon}
                </Box>
              )}

              <Box sx={{ minHeight: '40px' }}>
                <Typography variant="body1" className="labelText">
                  {field.label}:
                </Typography>

                <Typography variant="body2" className="valueText">
                  {field.type === 'date'
                    ? supplierData[field.key]
                      ? convertDateToStringFormat(supplierData[field.key], false, false)
                      : 'N/A'
                    : field.type === 'link'
                      ? supplierData[field.key] ? (
                          <a
                            href={supplierData[field.key]}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="link"
                          >
                            Link
                          </a>
                        ) : 'N/A'
                      : supplierData[field?.key] || 'N/A'}
                </Typography>
              </Box>
            </CardContent>
          </Grid>
        );
      })}
    </Grid>
  );
});

export default ClientDetailTable;
