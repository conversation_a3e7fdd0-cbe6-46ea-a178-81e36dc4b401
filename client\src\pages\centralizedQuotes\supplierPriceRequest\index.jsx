import { get } from 'lodash';
import ActionButton from 'pages/component/actionButton';
import TableComponent from 'pages/component/table/table';

const SupplierPriceRequest = ({ setFormData, formData, loading }) => {
  const { tableData, selectAllBrand } = get(formData, 'requestedPrice', {});

  const headers = [
    {
      name: 'brand',
      type: 'text',
      title: 'Brand',
      sortingactive: true
    },
    {
      name: 'partNumber',
      type: 'text',
      title: 'Part Number',
      sortingactive: true
    },
    {
      name: 'description',
      type: 'text',
      title: 'Description',
      sortingactive: true
    },
    {
      name: 'quantity',
      type: 'text',
      title: 'Qty',
      sortingactive: true
    },
    {
      name: 'kam',
      type: 'text',
      title: 'Kam',
      sortingactive: true
    },

    {
      name: 'actions',
      btnType: 'checkbox',
      type: 'actions',
      title: 'Select',
      checkboxName: 'select',
      sortingactive: false,
      component: ActionButton,
      columnAction: {
        btnType: 'multipleButton',
        component: ActionButton,

        multipleButtons: [
          {
            toolTipTitle: 'Select All',
            type: 'checkbox',
            disabledBtn: tableData?.length === 0,
            columnName: 'selectAllBrand',
            buttonOnClick: () => {
              selectAllBrandAction();
            }
          }
        ]
      },
      buttonOnClick: (type, id, materialId, index, data) => {
        selectSingleBrandAction(data);
      }
    }
  ];
  const selectAllBrandAction = () => {
    const { tableData } = get(formData, 'requestedPrice', {});

    const updatedData = tableData?.map((previousRequest) => ({ ...previousRequest, select: !formData?.requestedPrice?.selectAllBrand }));
    setFormData({
      ...formData,
      requestedPrice: {
        ...formData?.requestedPrice,
        selectAllBrand: !formData?.requestedPrice?.selectAllBrand,
        tableData: updatedData || [],
        selectedBrands: updatedData || []
      }
    });
  };

  const isAllBrandSelected = (brands) => {
    return brands?.every((brand) => brand?.select);
  };

  const selectSingleBrandAction = (data) => {
    const updatedData = tableData?.map((previousRequest) =>
      previousRequest?.id === data?.id ? { ...previousRequest, select: !data?.select } : previousRequest
    );
    setFormData({
      ...formData,
      requestedPrice: {
        ...formData?.requestedPrice,
        selectAllBrand: isAllBrandSelected(updatedData),
        tableData: updatedData || [],
        selectedBrands: updatedData || []
      }
    });
  };

  return (
    <>
      <TableComponent
        columns={headers}
        columnValues={{ selectAllBrand: selectAllBrand }}
        rows={tableData || []}
        title="Vovier"
        titleLink="dashboard"
        enablePagination={true}
      />
    </>
  );
};
export default SupplierPriceRequest;
