import React from 'react';
import { Table<PERSON>ell, Button } from '@mui/material';
import { convertDateToStringFormat, formattedPrice } from 'utils/helper';
import { get } from 'lodash';
import BadgeInputComponent from './badgeInput';

const renderArrayCell = (items, name, type, dateFormat, includeTime, showToday, actionBtns, column, row) => {
  return (
    <TableCell>
      {items?.map((item, index) => {
        const cellData = get(item, name, '-');

        switch (type) {
          case 'date':
            return (
              <div key={index} style={{ marginTop: '10px' }}>
                {cellData ? (includeTime ? getDateWithTime(cellData) : convertDateToStringFormat(cellData, dateFormat, showToday)) : '-'}
              </div>
            );
          case 'price':
            return (
              <div key={index} style={{ marginTop: '10px' }}>
                {cellData ? formattedPrice(cellData) : '-'}
              </div>
            );
          case 'email':
            return (
              <div key={index} style={{ textTransform: 'lowercase', marginTop: '10px' }}>
                {cellData}
              </div>
            );
          case 'badge':
            return (
              <BadgeInputComponent
                onlyTextColour={column?.onlyTextColour}
                badgeContent={cellData}
                color={item?.color}
                onClick={() => {}}
                className={column?.badgeClassName}
              />
            );
          case 'actions':
            return (
              <div key={index} style={{ marginTop: '10px' }}>
                {actionBtns?.map((actionBtn, btnIndex) => (
                  <Button
                    key={btnIndex}
                    size="small"
                    className="small-btn"
                    variant={actionBtn.variant || 'contained'}
                    color={item[actionBtn?.isDynamicBtnColor] || actionBtn.color || 'primary'}
                    onClick={() => actionBtn.onClick(row, item)}
                    disabled={item[actionBtn?.dynamicDisableBtn] || actionBtn.disabled}
                  >
                    {item[actionBtn?.title]}
                  </Button>
                ))}
              </div>
            );
          default:
            return (
              <div key={index} style={{ marginTop: '10px' }}>
                {cellData || '-'}
              </div>
            );
        }
      })}
    </TableCell>
  );
};

export default renderArrayCell;
