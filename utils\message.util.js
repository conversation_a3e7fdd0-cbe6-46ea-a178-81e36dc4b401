module.exports = {
  FETCH: [200, ":item fetched successfully."],
  LOGIN: [200, "Login successfully."],
  SUCCESS: [200, ":item sent successfully."],
  SENT_SUCCESS: [200, ":item sent successfully."],
  ADD_SUCCESS: [200, ":item added successfully."],
  CONFIRM_SUCCESS: [200, ":item confirmed successfully."],
  STATUS_SUCCESS: [200, ":item status updated successfully."],
  UPDATE_SUCCESS: [200, ":item updated successfully."],
  DELETE_SUCCESS: [200, ":item deleted successfully."],
  CUSTOM_SUCCESS: [200, ":item."],

  CUSTOM_ERROR: [400, ":item."],
  SENT_ERROR: [400, "Couldn't sent :item."],
  ADD_ERROR: [400, "Couldn't create :item."],
  UPDATE_ERROR: [400, "Couldn't update :item."],
  DELETE_ERROR: [400, "Couldn't delete :item."],
  REQUIRED: [400, ":item is required."],
  EXISTS: [400, ":item already exists."],
  INVALID: [400, "Invalid :item."],
  NOT_MATCH: [400, ":item do not match."],
  UNAUTHORIZED: [401, ":item is unauthorized. Please login again."],
  FORBIDDEN: [403, ":item does not have access."],
  NOT_FOUND: [404, ":item not found."],

  SERVER_ERROR: [500, "Something went wrong. Please try again later."],
};
